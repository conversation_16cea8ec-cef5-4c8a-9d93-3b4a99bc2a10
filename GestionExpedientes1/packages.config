﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.4.1.9004" targetFramework="net461" />
  <package id="AspNet.ScriptManager.bootstrap" version="3.0.0" targetFramework="net461" />
  <package id="AspNet.ScriptManager.jQuery" version="1.10.2" targetFramework="net461" />
  <package id="bootstrap" version="3.0.0" targetFramework="net461" />
  <package id="jQuery" version="1.10.2" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.0.6" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.2.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.FriendlyUrls" version="1.0.2" targetFramework="net461" />
  <package id="Microsoft.AspNet.FriendlyUrls.Core" version="1.0.2" targetFramework="net461" />
  <package id="Microsoft.AspNet.FriendlyUrls.Core.es" version="1.0.2" targetFramework="net461" />
  <package id="Microsoft.AspNet.ScriptManager.MSAjax" version="5.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.ScriptManager.WebForms" version="5.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization.es" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization.WebForms" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="1.0.5" targetFramework="net461" />
  <package id="Microsoft.Net.Compilers" version="2.1.0" targetFramework="net461" developmentDependency="true" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net461" />
  <package id="Modernizr" version="2.6.2" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="6.0.4" targetFramework="net461" />
  <package id="Respond" version="1.2.0" targetFramework="net461" />
  <package id="WebGrease" version="1.5.2" targetFramework="net461" />
</packages>
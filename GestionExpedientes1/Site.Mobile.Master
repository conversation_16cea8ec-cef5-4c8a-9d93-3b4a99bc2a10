<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.Mobile.master.cs" Inherits="GestionExpedientes1.Site_Mobile" %>
<%@ Register Src="~/ViewSwitcher.ascx" TagPrefix="friendlyUrls" TagName="ViewSwitcher" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <asp:ContentPlaceHolder runat="server" ID="HeadContent" />
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <h1>Mobile Master Page</h1>
        <asp:ContentPlaceHolder runat="server" ID="FeaturedContent" />
        <section class="content-wrapper main-content clear-fix">
            <asp:ContentPlaceHolder runat="server" ID="MainContent" />
        </section>
        <friendlyUrls:ViewSwitcher runat="server" />
    </div>
    </form>
</body>
</html>

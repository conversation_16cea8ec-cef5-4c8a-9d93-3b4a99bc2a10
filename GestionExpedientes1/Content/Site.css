﻿/* Move down content because we have a fixed navbar that is 50px tall */
body {
    padding-top: 50px;
    padding-bottom: 20px;
    overflow:hidden;
}

/* Wrapping element */
/* Set some basic padding to keep content from hitting the edges */
.body-content {
  
    background-color: #ccc;
    position: absolute;
    left: 0;
    right: 0;
    top: 148px;
    bottom: 0;
    background-color: #DFDFDF;
    border-top: 1px solid #999;
}

/* Set widths on the form inputs since otherwise they're 100% wide */
input,
select,
textarea {
    max-width: 280px;
}


/* Responsive: Portrait tablets and up */
@media screen and (min-width: 768px) {
    .jumbotron {
        margin-top: 20px;
    }

    .body-content {
        padding: 0;
    }
}


/* Nuevo css */
#app-top {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    border-bottom: 1px solid #0159a2;
    height: 25px;
    background-color: #0070cd;
}

#app-header {
    position: absolute;
    left: 0;
    right: 0;
    top: 25px;
    border-bottom: 1px solid #ccc;
    height: 76px;
}

#app-header-logo {
    width: 200px;
    height: 75px;
    background-image: url(../Images/app-header-logo.png);
    background-size: cover;
    /* border: 1px solid #ccc; */
    position: absolute;
}

#app-header-titulo {
    width: 400px;
    height: 75px;
    /* border: 1px solid #ccc; */
    margin-left: 200px;
    position: absolute;
}

#app-header-titulo h1 {
        font-family: 'Segoe UI', Helvetica, sans-serif;
        font-weight: normal;
        font-size: 26px;
        font-variant: small-caps;
        color: #0081c2;
        line-height: 22px;
        padding-left: 10px;
        margin: 0;
        margin-top: 10px;
        margin-bottom: 10px;
    }
    #app-header-titulo h2 {
        font-family: 'Segoe UI', Helvetica, sans-serif;
        font-weight: 200;
        font-size: 22px;
        font-variant: small-caps;
        color: #ff9f35;
        line-height: 22px;
        padding-left: 10px;
        margin: 0;
        padding: 0;
        outline: 0;
        font-weight: inherit;
        font-style: inherit;
        font-size: 100%;
        font-family: inherit;
        vertical-align: baseline;
        display: block;
        margin-left: 10px;
    }
#app-navegacion {
    position: absolute;
    left: 0;
    right: 0;
    top: 101px;
    border-bottom: 1px solid #999;
    padding-left: 10px;
    line-height: 50px;
    font-weight: bold;
}
#app-header-tool {
    position: absolute;
    right: 10px;
    top: 0px;
}
#app-contenido {
    position: absolute;
    left: 0;
    right: 0;
    top: 148px;
    bottom: 0;
    background-color: #DFDFDF;
    border-top: 1px solid #999;
}
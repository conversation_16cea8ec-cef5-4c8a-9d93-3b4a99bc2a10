using Dapper;
using INAIPI.Service.Api.DataAcces;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace INAIPI.Service.Api.Services
{
	public class CentrosService : QEC_DA
	{
		public IEnumerable<dynamic> getAllCentrosEnServicios()
		{
			using (SqlConnection sqlConnection = this.Conectar())
			{
				return SqlMapper.Query((IDbConnection)sqlConnection, "sp_TER_Estancias", (object)new
				{
					Accion = "LISTAR_EN_SERVICIO_API"
				}, (IDbTransaction)null, true, (int?)null, (CommandType?)CommandType.StoredProcedure);
			}
		}
	}
}

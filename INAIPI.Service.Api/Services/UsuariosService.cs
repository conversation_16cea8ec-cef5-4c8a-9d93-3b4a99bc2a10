using Dapper;
using INAIPI.Core;
using INAIPI.Service.Api.DataAcces;
using INAIPI.Service.Api.Models;
using System;
using System.Data;
using System.Data.SqlClient;

namespace INAIPI.Service.Api.Services
{
	public class UsuariosService : QEC_DA
	{
		public Resultado<INS_Usuario> getByLogin(string UserName, string Password)
		{
			Resultado<INS_Usuario> obj = new Resultado<INS_Usuario>();
			obj.TodoBien = true;
			Resultado<INS_Usuario> val = obj;
			using (SqlConnection sqlConnection = this.Conectar())
			{
				try
				{
					INS_Usuario iNS_Usuario = SqlMapper.QuerySingleOrDefault<INS_Usuario>((IDbConnection)sqlConnection, "sp_INS_Usuarios", (object)new
					{
						Accion = "LOGIN_FROM_WEB_API_LEVANTAMIENTO",
						UserName = UserName
					}, (IDbTransaction)null, (int?)null, (CommandType?)CommandType.StoredProcedure);
					if (iNS_Usuario != null && iNS_Usuario.Password == Password && !iNS_Usuario.Inactivo)
					{
						iNS_Usuario.EstatusLogin = INS_Usuario.LoginResult.UsuarioLogeagoSatifactoriaMente;
						iNS_Usuario.Password = "***********";
						val.Objeto = iNS_Usuario;
						return val;
					}
					if (iNS_Usuario != null && iNS_Usuario.Password == Password && iNS_Usuario.Inactivo)
					{
						val.Objeto = (new INS_Usuario
						{
							EstatusLogin = INS_Usuario.LoginResult.UusarioInactivo
						});
						val.TodoBien = false;
						val.strError = "El usuario Actual se encuentra inactivo.";
						return val;
					}
					val.Objeto = (new INS_Usuario
					{
						EstatusLogin = INS_Usuario.LoginResult.UusarioOContrasenaIncorreco
					});
					val.TodoBien = false;
                    val.strError = "El usuario Actual se encuentra inactivo.";
                    return val;
				}
				catch (Exception ex)
				{
                    val.TodoBien = false;
                    val.strError = ex.Message;
					return val;
				}
			}
		}
	}
}

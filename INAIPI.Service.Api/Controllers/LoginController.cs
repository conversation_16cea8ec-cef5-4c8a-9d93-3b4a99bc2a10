using INAIPI.Core;
using INAIPI.Service.Api.Models;
using INAIPI.Service.Api.Services;
using System.Collections.Generic;
using System.Web.Http;

namespace INAIPI.Service.Api.Controllers
{
	public class LoginController : ApiController
    {
		private UsuariosService service = new UsuariosService();

		public IEnumerable<string> Get()
		{
			return new string[2]
			{
				"value1",
				"value2"
			};
		}

		public string Get(int id)
		{
			return "value";
		}

		[HttpPost]
		public IHttpActionResult Post([FromBody] TokenRequest token)
		{
			Resultado<INS_Usuario> byLogin = service.getByLogin(token.UserName, token.Password);
			return this.Ok<INS_Usuario>(byLogin.Objeto);
		}

		public void Put(int id, [FromBody] string value)
		{
		}

		public void Delete(int id)
		{
		}

		public LoginController()
			: base()
		{
		}
	}
}

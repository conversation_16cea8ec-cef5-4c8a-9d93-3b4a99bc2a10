using INAIPI.Service.Api.Services;
using System.Collections.Generic;
using System.Web.Http;

namespace INAIPI.Service.Api.Controllers
{
	public class CentrosController : ApiController
    {
		private CentrosService servicio = new CentrosService();

		public IEnumerable<dynamic> Get()
		{
			return servicio.getAllCentrosEnServicios();
		}

		public string Get(int id)
		{
			return "value";
		}

		public void Post([FromBody] string value)
		{
		}

		public void Put(int id, [FromBody] string value)
		{
		}

		public void Delete(int id)
		{
		}

		public CentrosController() : base()
		{
		}
	}
}

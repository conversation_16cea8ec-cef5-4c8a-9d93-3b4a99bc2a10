namespace INAIPI.Service.Api.Models
{
	public class INS_Usuario
	{
		public enum LoginResult
		{
			UusarioOContrasenaIncorreco = 3,
			UsuarioLogeagoSatifactoriaMente = 1,
			UusarioInactivo = 2
		}

		public LoginResult EstatusLogin
		{
			get;
			set;
		}

		public decimal IdUsuario
		{
			get;
			set;
		}

		public string UserName
		{
			get;
			set;
		}

		public decimal IdEstancia
		{
			get;
			set;
		}

		public string Estancia
		{
			get;
			set;
		}

		public string Nombres
		{
			get;
			set;
		}

		public string Apellidos
		{
			get;
			set;
		}

		public string NombreCompleto
		{
			get;
			set;
		}

		public string Cedula
		{
			get;
			set;
		}

		public string Password
		{
			get;
			set;
		}

		public string Posicion
		{
			get;
			set;
		}

		public int Perfil
		{
			get;
			set;
		}

		public decimal IdTerritorio
		{
			get;
			set;
		}

		public decimal IdRed
		{
			get;
			set;
		}

		public byte IdAvatar
		{
			get;
			set;
		}

		public bool Inactivo
		{
			get;
			set;
		}

		public string RutaAvatar
		{
			get;
			set;
		}

		public string Puesto
		{
			get;
			set;
		}

		public string Departamento
		{
			get;
			set;
		}

		public int IdPkSeccion
		{
			get;
			set;
		}

		public decimal IdColaborador
		{
			get;
			set;
		}

		public decimal IdTerritorioLevantamiento
		{
			get;
			set;
		}

		public string TerritorioLevantamiento
		{
			get;
			set;
		}

		public decimal IdRedLevantamiento
		{
			get;
			set;
		}

		public string RedLevantamiento
		{
			get;
			set;
		}

		public decimal IdSubRedLevantamiento
		{
			get;
			set;
		}

		public string SubRedLevantamiento
		{
			get;
			set;
		}

		public string NoNucleoLevantamiento
		{
			get;
			set;
		}
	}
}

using System;

namespace INAIPI.Service.Api.Helpers
{
	public class DefaultHelper
	{
		public static DateTime TokenToDate(string token)
		{
			string empty = string.Empty;
			string empty2 = string.Empty;
			string empty3 = string.Empty;
			string str = empty;
			char c = token[0];
			empty = str + c.ToString();
			string str2 = empty;
			c = token[4];
			empty = str2 + c.ToString();
			string str3 = empty2;
			c = token[1];
			empty2 = str3 + c.ToString();
			string str4 = empty2;
			c = token[5];
			empty2 = str4 + c.ToString();
			string str5 = empty3;
			c = token[2];
			empty3 = str5 + c.ToString();
			string str6 = empty3;
			c = token[3];
			empty3 = str6 + c.ToString();
			string str7 = empty3;
			c = token[6];
			empty3 = str7 + c.ToString();
			string str8 = empty3;
			c = token[7];
			empty3 = str8 + c.ToString();
			int result = 0;
			int result2 = 0;
			int result3 = 0;
			int.TryParse(empty, out result);
			int.TryParse(empty2, out result2);
			int.TryParse(empty3, out result3);
			return new DateTime(result3, result2, result);
		}

		public static string DateToToken(DateTime date)
		{
			string text = date.ToString("ddMMyyyy");
			string empty = string.Empty;
			string str = empty;
			char c = text[0];
			empty = str + c.ToString();
			string str2 = empty;
			c = text[2];
			empty = str2 + c.ToString();
			string str3 = empty;
			c = text[4];
			empty = str3 + c.ToString();
			string str4 = empty;
			c = text[5];
			empty = str4 + c.ToString();
			string str5 = empty;
			c = text[1];
			empty = str5 + c.ToString();
			string str6 = empty;
			c = text[3];
			empty = str6 + c.ToString();
			string str7 = empty;
			c = text[6];
			empty = str7 + c.ToString();
			string str8 = empty;
			c = text[7];
			return str8 + c.ToString();
		}

		public static bool TokenIsValid(string token)
		{
			return TokenToDate(token).CompareTo(DateTime.Today) == 0;
		}
	}
}

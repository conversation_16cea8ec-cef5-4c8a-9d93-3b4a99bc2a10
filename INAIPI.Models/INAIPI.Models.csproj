﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6F28BA3B-4272-48B6-914B-712EA9CCA745}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>INAIPI.Models</RootNamespace>
    <AssemblyName>INAIPI.Models</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Dapper, Version=1.50.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.1.50.0\lib\net45\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="Dapper.Contrib, Version=1.50.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.Contrib.1.50.0\lib\net45\Dapper.Contrib.dll</HintPath>
    </Reference>
    <Reference Include="SQLite-net, Version=1.6.292.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\sqlite-net-pcl.1.6.292\lib\netstandard1.1\SQLite-net.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.batteries_green, Version=1.1.13.388, Culture=neutral, PublicKeyToken=a84b7dcfb1391f7f, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.bundle_green.1.1.13\lib\net45\SQLitePCLRaw.batteries_green.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.batteries_v2, Version=1.1.13.388, Culture=neutral, PublicKeyToken=8226ea5df37bcae9, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.bundle_green.1.1.13\lib\net45\SQLitePCLRaw.batteries_v2.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.core, Version=1.1.13.388, Culture=neutral, PublicKeyToken=1488e028ca7ab535, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.core.1.1.13\lib\net45\SQLitePCLRaw.core.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.provider.e_sqlite3, Version=1.1.13.388, Culture=neutral, PublicKeyToken=9c301db686d0bd12, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.provider.e_sqlite3.net45.1.1.13\lib\net45\SQLitePCLRaw.provider.e_sqlite3.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaseModel.cs" />
    <Compile Include="Class1.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QEC\CONFI\CONFI_VersionesSIGEPI_APP.cs" />
    <Compile Include="QEC\FAM\FAM_CentrosTerapeuticos.cs" />
    <Compile Include="QEC\FAM\FAM_Familias.cs" />
    <Compile Include="QEC\FAM\FAM_FamiliasMiembrosTipos.cs" />
    <Compile Include="QEC\FAM\FAM_MasterDetalle.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosAlternativos.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosCompromisos.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosPreguntasCrianza.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosPreguntasCrianzaOpciones.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosRiesgoHabitacional.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosSenalesAlerta.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosTalleres.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosTipoDiscapacidad.cs" />
    <Compile Include="QEC\FAM\FAM_MiembrosTipoVulneracion.cs" />
    <Compile Include="QEC\FAM\FAM_Visitas.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasActividadesEducativas.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasMiembrosAnimos.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasMiembrosPadreEnCrianzaPreguntas.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasMiembrosPadreEnCrianzaRespuestas.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasMiembrosRazonesNoLME.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasProgramaciones.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasProgramacionesEstatus.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasProgramacionesRazonesNoVisita.cs" />
    <Compile Include="QEC\FAM\FAM_VisitasTemasATratar.cs" />
    <Compile Include="QEC\INDICADORES\vi_INS_InscripcionesLite.cs" />
    <Compile Include="QEC\INDICADORES\vi_INS_Inscripciones.cs" />
    <Compile Include="QEC\INS\INS_Usuarios.cs" />
    <Compile Include="QEC\NN.cs" />
    <Compile Include="QEC\SAD\SAD_AlertaEstado.cs" />
    <Compile Include="QEC\SAD\SAD_Discapacidad.cs" />
    <Compile Include="QEC\SAD\SAD_SenalAlerta.cs" />
    <Compile Include="QEC\SAD\SAD_Terapias.cs" />
    <Compile Include="QEC\TAL\TAL_EstatusTaller.cs" />
    <Compile Include="QEC\TAL\TAL_Taller.cs" />
    <Compile Include="QEC\TER\TER_Estancias.cs" />
    <Compile Include="QEC\TER\TER_Regiones.cs" />
    <Compile Include="QEC\TER\TER_Red.cs" />
    <Compile Include="QEC\TER\TER_Salas.cs" />
    <Compile Include="QEC\TER\TER_Territorios.cs" />
    <Compile Include="Resultado.cs" />
    <Compile Include="QEC\SAD\SAD_Alerta.cs" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\SQLitePCLRaw.lib.e_sqlite3.linux.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.linux.targets" Condition="Exists('..\packages\SQLitePCLRaw.lib.e_sqlite3.linux.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.linux.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Este proyecto hace referencia a los paquetes NuGet que faltan en este equipo. Use la restauración de paquetes NuGet para descargarlos. Para obtener más información, consulte http://go.microsoft.com/fwlink/?LinkID=322105. El archivo que falta es {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\SQLitePCLRaw.lib.e_sqlite3.linux.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.linux.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SQLitePCLRaw.lib.e_sqlite3.linux.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.linux.targets'))" />
    <Error Condition="!Exists('..\packages\SQLitePCLRaw.lib.e_sqlite3.osx.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.osx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SQLitePCLRaw.lib.e_sqlite3.osx.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.osx.targets'))" />
    <Error Condition="!Exists('..\packages\SQLitePCLRaw.lib.e_sqlite3.v110_xp.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.v110_xp.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SQLitePCLRaw.lib.e_sqlite3.v110_xp.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.v110_xp.targets'))" />
  </Target>
  <Import Project="..\packages\SQLitePCLRaw.lib.e_sqlite3.osx.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.osx.targets" Condition="Exists('..\packages\SQLitePCLRaw.lib.e_sqlite3.osx.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.osx.targets')" />
  <Import Project="..\packages\SQLitePCLRaw.lib.e_sqlite3.v110_xp.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.v110_xp.targets" Condition="Exists('..\packages\SQLitePCLRaw.lib.e_sqlite3.v110_xp.1.1.13\build\net35\SQLitePCLRaw.lib.e_sqlite3.v110_xp.targets')" />
</Project>
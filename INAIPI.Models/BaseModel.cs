﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models
{
    [Serializable]
    public class BaseModel
    {
        public BaseModel()
        {
            Accion = enumAccion.Agregar;
        }

        [SQLite.Ignore]
        [Dapper.Contrib.Extensions.Write(false)]
        public enumAccion Accion { get; set; }

        public enum enumAccion : int { Agregar = 1, Editar = 2, Eliminar = 3 , Ninguna = 4 }
    }
}

﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Dapper" version="1.50.0" targetFramework="net45" />
  <package id="Dapper.Contrib" version="1.50.0" targetFramework="net45" />
  <package id="sqlite-net-pcl" version="1.6.292" targetFramework="net45" />
  <package id="SQLitePCLRaw.bundle_green" version="1.1.13" targetFramework="net45" />
  <package id="SQLitePCLRaw.core" version="1.1.13" targetFramework="net45" />
  <package id="SQLitePCLRaw.lib.e_sqlite3.linux" version="1.1.13" targetFramework="net45" />
  <package id="SQLitePCLRaw.lib.e_sqlite3.osx" version="1.1.13" targetFramework="net45" />
  <package id="SQLitePCLRaw.lib.e_sqlite3.v110_xp" version="1.1.13" targetFramework="net45" />
  <package id="SQLitePCLRaw.provider.e_sqlite3.net45" version="1.1.13" targetFramework="net45" />
</packages>
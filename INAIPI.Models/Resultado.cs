﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace INAIPI.Models
{
    public class Resultado
    {
        public bool TodoBien { get; set; }
        public string strError { get; set; }
        public virtual object ID { get; set; }
        public bool EscritoEnElLog { get; set; }
        public object result { get; set; }

        public Resultado()
        {
            strError = string.Empty;
        }

        public Resultado(bool TodoBien, string strError = "", object ID = null, bool EscribirLogWindows = false)
        {
            this.TodoBien = TodoBien;
            this.strError = strError;
            this.ID = ID;

            //if (!TodoBien)
            //{
            //    this.LogWindows();
            //}
            //else
            //{
            //    if (EscribirLogWindows)
            //    {
            //        this.LogWindows();
            //    }
            //}
        }
    }

    public class Resultado<T> : Resultado where T  : class  
    {
        public new T Objeto { get; set; }
        public Resultado()
        {
            strError = string.Empty;
        }

        public Resultado(bool TodoBien, string strError = "", object ID = null, object Objeto = null, bool EscribirLogWindows = false)
        {
            this.TodoBien = TodoBien;
            this.strError = strError;
            this.Objeto = (T)Objeto;

            //if (!TodoBien)
            //{
            //    this.LogWindows();
            //}
            //else
            //{
            //    if (EscribirLogWindows)
            //    {
            //        this.LogWindows();
            //    }
            //}
        }
    }

}


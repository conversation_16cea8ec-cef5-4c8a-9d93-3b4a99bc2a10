﻿using Dapper.Contrib.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.TER
{
    [Table("TER_EstanciasSalas")]
    [Serializable]
    public class TER_Salas
    {
        public decimal IdEstancia { get; set; }
        public string Alias { get; set; }
        public string Asistente { get; set; }
        public int Cupo { get; set; }
        public string Agente { get; set; }
        public string NucleoNo { get; set; }
        public string Sala { get; set; }

        [Write(false)]
        public DateTime? FechaUltimaAsistencia { get; set; }
        [Write(false)]
        public DateTime? FechaInicioAsistencia { get; set; }
        [Write(false)]
        public DateTime? FechaAsistencia { get; set; }
        [Write(false)]
        public int DiasRestantesAsistencia { get; set; }
        [Write(false)]
        public bool Selected { get; set; }        
    }
}

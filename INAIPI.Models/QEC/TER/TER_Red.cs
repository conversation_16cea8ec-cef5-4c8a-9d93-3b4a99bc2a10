﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.TER
{
    [Serializable]
    public class TER_Red
    {
        public decimal IdRed { set; get; }
        public string Red { set; get; }
        public decimal IdTerritorio { set; get; }
        public string Territorio { set; get; }
        public string Tipo { set; get; }
        public string SubRegionId { set; get; }
        public string SubRegion { set; get; }
        public string ProvinciaId { set; get; }
        public string Provincia { set; get; }
        public string MunicipioId { set; get; }
        public string Municipio { set; get; }
        public string DistritoMunicipalId { set; get; }
        public string DistritoMunicipal { set; get; }
        public string SeccionId { set; get; }
        public string Seccion { set; get; }
        public string BarrioId { set; get; }
        public string Barrio { set; get; }
        public string SubBarrioId { set; get; }
        public string SubBarrio { set; get; }
        public string CodigoONE { set; get; }
        public decimal IdRegion { set; get; }
        public string Region { set; get; }
        public decimal IdTerritorioSec { set; get; }
        public string Clase { set; get; }
        public int NivelRed { set; get; }
        public string RedCompleta { set; get; }
    }
}

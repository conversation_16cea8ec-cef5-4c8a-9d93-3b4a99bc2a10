﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.TER
{
    [Serializable]
    [Dapper.Contrib.Extensions.Table("TER_Regiones")]
    public class TER_Regiones : BaseModel
    {
        public decimal IdRegion { get; set; }

        public string Region { get; set; }

        public string RegionStr => $"[{IdRegion}] {Region}";

        public override string ToString() => $"[{IdRegion}] {Region}";

    }
}

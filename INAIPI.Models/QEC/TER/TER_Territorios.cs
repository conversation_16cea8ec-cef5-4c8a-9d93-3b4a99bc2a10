﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.TER
{
    [Serializable]
    public class TER_Territorios : BaseModel
    {
        public decimal IdTerritorio { get; set; }
        public string Territorio { get; set; }
        public string Tipo { get; set; }
        public byte Nivel { get; set; }
        public decimal IdProvincia { get; set; }
        public decimal IdMunicipio { get; set; }
        public decimal IdDistrito { get; set; }
        public decimal IdSector { get; set; }
        public string ProvinciaId { get; set; }
        public string MunicipioId { get; set; }
        public string DistritoMunicipalId { get; set; }
        public string SeccionId { get; set; }
        public string BarrioId { get; set; }
        public string SubBarrioId { get; set; }
        public decimal IdRegion { get; set; }
        public decimal IdTerritorioSec { get; set; }
        public string UrlMapa { get; set; }
        public bool EnDigitacion { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace INAIPI.Models.QEC.TER
{
    [Serializable]
    [Dapper.Contrib.Extensions.Table("TER_Estancias")]
    [SQLite.Table("TER_Estancias")]
    public class TER_Estancias
    {
        [Dapper.Contrib.Extensions.ExplicitKey]
        [SQLite.PrimaryKey]
        public decimal IdEstancia { get; set; }
        public string Estancia { get; set; }
        public string Tipo { get; set; }
        public decimal IdEstanciaPrincipal { get; set; }
        public decimal IdProvincia { get; set; }
        public string ProvinciaId { get; set; }
        public string Provincia { get; set; }
        public int IdPK { get; set; }
        public string MunicipioId { get; set; }
        public string Municipio { get; set; }
        public decimal IdDistrito { get; set; }
        public string Distrito { get; set; }
        public decimal IdSector { get; set; }
        public string Sector { get; set; }
        public decimal Cupo { get; set; }
        public string Coordinador { get; set; }
        public string CorreoCoordinador { get; set; }
        public string EncargadoAdm { get; set; }
        public string CorreoEncargadoAdm { get; set; }
        public string Direccion { get; set; }
        public string Telefono { get; set; }
        public string NumeroContacto { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }
        public decimal IdTerritorio { get; set; }
        public string Territorio { get; set; }
        public decimal IdRed { get; set; }
        public string Red { get; set; }
        public decimal IdSubRed { get; set; }
        public string TipoGestion { get; set; }
        public decimal IdRegional { get; set; }
        public string Control { get; set; }
        public string NombreCompleto { get; set; }
        public decimal IdTerritorioSec { get; set; }
        public decimal IdRedSec { get; set; }
        public decimal IdCuadranteSec { get; set; }
        public bool EnInscripcion { get; set; }
        public DateTime? FechaInscripcion { get; set; }
        public DateTime FechaServicio { get; set; }
        public int IdOrganizacion { get; set; }
        public string Organizacion { get; set; }
        public int NoSalida { get; set; }
        public bool EnCampana { get; set; }
        public int IdRedCG { get; set; }
        public decimal IdRegion { get; set; }
        public string Region { get; set; }
        public string TelefonoFlota { get; set; }
        public string ExtRecepcion { get; set; }
        public string ExtEncargado { get; set; }
        public string ExtCoordinador { get; set; }
        public short IdEstatusServicio { get; set; }
        public string EstatusDelServicio { get; set; }
        public DateTime? FechaAperturaServicio { get; set; }
        public DateTime FechaEstatusServicio { get; set; }
        public string MesApertura 
        {
            get
            {
                if (FechaAperturaServicio != null)
                {
                    return FechaAperturaServicio.Value.ToString("MM-yyyy");
                }
                else
                {
                    return "";
                }
            } 
        }
        public string CAFI_Estrategias { get; set; }
        public string Latitud { get; set; }
        public string Longitud { get; set; }
        public decimal IdTecnicoProvincial { get; set; }
        public string TecnicoProvincial { get; set; }
        public decimal IdTecnicoRegional { get; set; }
        public string TecnicoRegional { get; set; }
        public decimal IdTecnicoNacional { get; set; }
        public string TecnicoNacional { get; set; }
        public string StrTieneLocal { get; set; }
        [SQLite.Ignore]
        public List<TER_EstanciasSalas> Salas { get; set; }
        [SQLite.Ignore]
        public List<TER_Estancias.TER_EstanciasRangoEtario> RangosEtarios { get; set; }
        public decimal IdUsuario { get; set; }

        [Serializable]
        public class TER_EstanciasSalas : TER_Estancias
        {

            public int CupoSala { get; set; }
            public string Sala { get; set; }
            public string SalaDetalle { get { return string.Format(Sala + "|" + Grupo + "|" + Dia + "|" + Horario); } }
            public string Alias { get; set; }
            public string Agente { get; set; }
            public string Asistente { get; set; }
            public bool Inactiva { get; set; }
            public string Grupo { get; set; }
            public string Dia { get; set; }
            public string Horario { get; set; }

            public DateTime? FechaInactivacion { get; set; }
        }

        [Serializable]
        public class TER_EstanciasRangoEtario : TER_Estancias
        {
            public decimal IdEstancia { get; set; }
            public byte IdRangoEtario { get; set; }
            public string RangoEtario { get; set; }
            public decimal CupoBruto { get; set; }
            public decimal Reserva { get; set; }
            public decimal Cupo { get; set; }
        }

        public enum enumEstatusServicio : short { SIN_PROYECCION = 1, PROYECTADO, EN_SERVICIO, FUERA_DE_SERVICIO };

        public enum enumEstatusServiciosBasicos : short { SIN_SERVICIO = 1, SERVICIO_SOLICITADO, RECEPCION_INSTALACION_DEL_SERVICIO, NO_APLICA = 5 };
        
        public enum enumEstatusLocal : short { CONTRATO_CERTIFICADO = 9 };

        public enum enumTieneLocal : short { SIN_LOCAL = 0, CON_LOCAL};
        
        [Serializable]
        public class TER_EstanciasEstatus : TER_Estancias
        {
            public int IdEstatusElectricidad { get; set; }
            public string EstatusElectricidad { get; set; }
            public string NICElectricidad { get; set; }
            public int DiaPagoElectricidad { get; set; }
            public DateTime? FechaActElectricidad { get; set; }
            public int IdNombreElectricidad { get; set; }
            public string NombreElectricidad { get; set; }
            public DateTime? FechaEntregaElectricidad { get; set; }
            public bool ElectricidadEsMinerd { get; set; }
            public string ElectricidadEsMinerdStr
            {
                get
                {
                    if (ElectricidadEsMinerd == true)
                    {
                        return "SÍ";
                    }
                    else
                    {
                        return "NO";
                    }
                }
            }
            public DateTime FechaInstalacionElectricidad { get; set; }
            public int IdSuplidorElectricidad { get; set; }
            public string SuplidorElectricidad { get; set; }
            public string ComentariosServiciosElectricidad { get; set; }
            public decimal IdUsuarioElectricidad { get; set; }
            public int IdEstatusAgua { get; set; }
            public string EstatusAgua { get; set; }
            public string NICAgua { get; set; }
            public int DiaPagoAgua { get; set; }
            public DateTime? FechaActAgua { get; set; }
            public int IdNombreAgua { get; set; }
            public string NombreAgua { get; set; }
            public DateTime? FechaEntregaAgua { get; set; }
            public DateTime FechaInstalacionAgua { get; set; }
            public int IdSuplidorAgua { get; set; }
            public string SuplidorAgua { get; set; }
            public string ComentariosServiciosAgua { get; set; }
            public decimal IdUsuarioAgua { get; set; }
            public int IdEstatusDesechos { get; set; }
            public string EstatusDesechos { get; set; }
            public string NICDesechos { get; set; }
            public int DiaPagoDesechos { get; set; }
            public DateTime? FechaActDesechos { get; set; }
            public int IdNombreDesechos { get; set; }
            public string NombreDesechos { get; set; }
            public DateTime? FechaEntregaDesechos { get; set; }
            public DateTime FechaInstalacionDesechos { get; set; }
            public int IdSuplidorDesechos { get; set; }
            public string SuplidorDesechos { get; set; }
            public string ComentariosServiciosDesechos { get; set; }
            public decimal IdUsuarioDesechos { get; set; }
            public int IdEstatusTelefonia { get; set; }
            public string EstatusTelefonia { get; set; }
            public DateTime? FechaActTelefonia { get; set; }
            public int IdNombreTelefonia { get; set; }
            public string NombreTelefonia { get; set; }
            public DateTime? FechaEntregaTelefonia { get; set; }
            public decimal IdUsuarioTelefonia { get; set; }
            public string ComentariosServiciosTelefonia { get; set; }
            public string NICTelefonia { get; set; }
            public int IdTipoTelefonia { get; set; }
            public string TipoTelefonia { get; set; }
            public string NumeroTelefonia { get; set; }
            public long NumeroTelefoniaInt
            {
                get
                {
                    long.TryParse((NumeroTelefonia ?? "0").ToString(), out long noTelefonia);
                    return noTelefonia;
                }
            }
            public int DiaPagoTelefonia { get; set; }
            public int IdTipoServicioTelefonia { get; set; }
            public string TipoServicioStr { get; set; }
            public string NumeroDeServicio { get; set; }
            public DateTime FechaInstalacionTelefonia { get; set; }
            public int IdSuplidorTelefonia { get; set; }
            public string SuplidorTelefonia { get; set; }
            public int IdEstatusGas { get; set; }
            public string NICGas { get; set; }
            public DateTime FechaInstalacionGas { get; set; }
            public int DiaPagoGas { get; set; }
            public int IdSuplidorGas { get; set; }
            public string SuplidorGas { get; set; }
            public string EstatusGas { get; set; }
            public int IdEstatusLocalia { get; set; }
            public string EstatusAlquiler { get; set; }
            public string NoDeContrato { get; set; }
            public string NoContrato { get; set; }
            public int IdProveedorLocal { get; set; }
            public int DiaPagoLocal { get; set; }
            public decimal ValorContratoAlquiler { get; set; }
            public string NombreSuplidorLocal { get; set; }
            public string SuplidorLocal { get; set; }
            public int DiaPago { get; set; }
            public DateTime? FechaInicioContrato { get; set; }
            public DateTime? FechaVencContrato { get; set; }
            public string ComentariosLocal { get; set; }
            public int IdUsuarioLocal { get; set; }
            public bool TieneLocal { get; set; }
            public int IdEstatusGestionLocal { get; set; }
            public int IdEstatusDetalleGestionLocal { get; set; }
            public int IdEstatusEquipamiento { get; set; }
            public string EstatusEquipamiento { get; set; }
            public bool ParticipacionComunitaria { get; set; }
            public bool DireccionOperaciones { get; set; }
            public bool DireccionServicios { get; set; }
            public int IdApoderadoLocal { get; set; }
            public string NombrePropietario { get; set; }
            public bool Ocupado { get; set; }
            public bool Acondicionado { get; set; }
            public string Latitud { get; set; }
            public string Longitud { get; set; }
            public string Direccion { get; set; }
            public decimal TecnicoEvaluador { get; set; }
            public string ComentariosGestionLocal { get; set; }
            public int IdUsuarioActGestionLocal { get; set; }
            public int IdEstatusEntradaTerritorio { get; set; }
            public DateTime FechaEstatusET { get; set; }
            public DateTime FechaEstatusEntradaTerritorio { get; set; }
            public int IdUsuarioEstatusEntradaTerritorio { get; set; }
            public int IdEstatusMovilizacionSocial { get; set; }
            public DateTime FechaEstatusMS { get; set; }
            public DateTime FechaEstatusMovilizacionSocial { get; set; }
            public int IdUsuarioEstatusPC { get; set; }
            public int IdEstatusCaracterizacionTerritorial { get; set; }
            public DateTime FechaEstatusCT { get; set; }
            public DateTime FechaEstatusCaracterizacionTerritorial { get; set; }
            public int IdEstatusProcesamientoLevantamiento { get; set; }
            public DateTime FechaEstatusPL { get; set; }
            public DateTime FechaEstatusProcesamientoLevantamiento { get; set; }
            public int IdUsuarioEstatusProcesamientoLevantamiento { get; set; }
            public int IdEstatusReclutamiento { get; set; }
            public DateTime FechaEstatusReclut { get; set; }
            public DateTime FechaEstatusReclutamiento { get; set; }
            public int IdUsuarioEstatusReclutamiento { get; set; }
            public int IdEstatusFormacionBasica { get; set; }
            public DateTime FechaEstatusFB { get; set; }
            public DateTime FechaEstatusFormacionBasica { get; set; }
            public int IdUsuarioEstatusFormacionBasica { get; set; }
            public string EstatusRI { get; set; }
            public string EstatusMS { get; set; }
            public string EstatusCT { get; set; }
            public string EstatusTIC { get; set; }
            public string EstatusReclutamiento { get; set; }
            public string EstatusFB { get; set; }
            public string TieneLocalidad { get; set; }
            public string EstaAcondicionado { get; set; }
            public string EstaOcupado { get; set; }
            public string EstatusGestionLocal { get; set; }
            public string EstatusDetalleGestionLocal { get; set; }
            public string Apoderado { get; set; }
            public string Tecnico { get; set; }
            public DateTime FechaInicioAsistencia { get; set; }
            public decimal IdUsuarioActFechaInicioAsistencia { get; set; }
            public int CantidadAlquileresPorVencer { get; set; }
            public string CuerpoCorreoAlertaAlquileres { get; set; }

        }

        [Serializable]
        public class TER_EstanciasEstatusServiciosBasicos : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstanciasEstatusLocal : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstanciasEstatusGestionLocal : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstanciasTecnicoEvaluador : TER_EstanciasEstatus
        {
            public decimal IdColaborador { get; set; }
            public string Nombre { get; set; }
            public string Estado { get; set; }
        }

        [Serializable]
        public class TER_EstatusEntradaTerritorio : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstatusMovilizacionSocial : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstatusCaracterizacionTerritorial : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstatusProcesamientoLevantamiento : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstatusReclutamiento : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstatusFormacionBasica : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstatusEquipamiento : TER_EstanciasEstatus
        {
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
        }

        [Serializable]
        public class TER_EstanciasEstatusServiciosBasicosCambios : TER_EstanciasEstatus
        {
            public int IdCambio { get; set; }
            public decimal IdEstancia { get; set; }
            public int IdServicioBasico { get; set; }
            public int IdEstatus { get; set; }
            public string Estatus { get; set; }
            public string UserName { get; set; }
            public int IdEstatusAnterior { get; set; }
            public DateTime FechaEstatus { get; set; }
            public decimal IdUsuario { get; set; }
            public string Comentarios { get; set; }
            public DateTime FechaSistema { get; set; }
        }

        [Serializable]
        public class TER_EstanciasEstatusServicios : BaseModel
        {
            public short IdEstatus { get; set; }
            public string Estatus { get; set; }
            public bool Activo { get; set; }
        }

        public override string ToString() => $"{Tipo} [{TipoGestion}] {Estancia} [{IdEstancia}] {Municipio}";

        [Serializable]
        [SQLite.Table("TER_Estancias")]
        public class TER_EstanciasView
        {
            [SQLite.PrimaryKey]
            public decimal IdEstancia { get; set; }
            public string Estancia { get; set; }
            public string Tipo { get; set; }
            public string TipoGestion { get; set; }
            public string Municipio { get; set; }
            public string EstanciaDisplay { get { return $"{Estancia} [{IdEstancia.ToString("N0")}]"; } }
            public override string ToString() => $"{Tipo} [{TipoGestion}] {Estancia} [{IdEstancia}] {Municipio}";
        }


    }

}
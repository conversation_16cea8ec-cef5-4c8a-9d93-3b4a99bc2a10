﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.TAL
{
    [Serializable]
    [Dapper.Contrib.Extensions.Table("TAL_EstatusTalleres")]
    [SQLite.Table("TAL_EstatusTalleres")]
    public class TAL_EstatusTaller : BaseModel
    {
        [Dapper.Contrib.Extensions.ExplicitKey]
        [SQLite.PrimaryKey]
        public byte IdEstatusTaller { get; set; }
        public string EstatusTaller { get; set; }
        public bool Inactivo { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.TAL
{
    [Serializable]
    [Dapper.Contrib.Extensions.Table("TAL_Talleres")]
    [SQLite.Table("TAL_Talleres")]
    public class TAL_Taller : BaseModel
    {
        [Dapper.Contrib.Extensions.ExplicitKey]
        [SQLite.PrimaryKey]
        public int IdTaller { get; set; }
        public int IdTemaATratar { get; set; }
        public byte IdEstatusTaller { get; set; }
        public decimal IdColaboradorEncargado { get; set; }
        public decimal IdRed { get; set; }
        public decimal? IdTecnicoRed { get; set; }
        public string OtroTema { get; set; }
        public decimal IdUsuarioRegistra { get; set; }
        public DateTime FechaRegistro { get; set; }
        public decimal? IdUsuarioEdita { get; set; }
        public DateTime? FechaEdita { get; set; }
        public byte? IdRazonCancelacion { get; set; }
        public decimal? IdUsuarioCancela { get; set; }
        public DateTime? FechaCancelacion { get; set; }
    }
}

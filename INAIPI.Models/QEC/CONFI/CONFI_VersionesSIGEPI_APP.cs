﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.CONFI
{
    [Serializable]
    public class CONFI_VersionesSIGEPI_APP
    {
        public int IdVersion { get; set; }
        public int IdApp { get; set; }
        public string NombreApp { get; set; }
        public DateTime FechaSistema { get; set; }
        public decimal IdUsuario { get; set; }
        public decimal NoVersion { get; set; }
        public string Url { get; set; }
        public bool EsProduccion { get; set; }
        public byte[] Apk { get; set; }
        public string NombreApk { get; set; }
        public decimal SizeApk { get; set; }
        public bool Eliminada { get; set; }
        public decimal? IdUsuarioElimina { get; set; }
        public DateTime? FechaElimina { get; set; }
        public string <PERSON>uario { get; set; }
    }
}

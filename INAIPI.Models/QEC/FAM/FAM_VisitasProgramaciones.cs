﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace INAIPI.Models.QEC
{
    [Serializable]
    public class FAM_VisitasProgramaciones : BaseModel
    {
        public int IdProgramacion { get; set; }
        public int IdFamilia { get; set; }
        public DateTime FechaVisita { get; set; }
        public decimal IdVisitante { get; set; }
        public decimal IdEstancia { get; set; }
        public byte NucleoNo { get; set; }
        public int IdPeriodo { get; set; }
        public byte IdEstatus { get; set; }
        public string ComentariosEstatus { get; set; }
        public byte? IdRazonNoVisita { get; set; }
        public decimal IdUsuarioRegistra { get; set; }
        public DateTime FechaRegistro { get; set; }
        public decimal? IdUsuarioModifica { get; set; }
        public DateTime? FechaModifica { get; set; }
        public decimal IdUsuarioEstatus { get; set; }
        public DateTime FechaEstatus { get; set; }

        [Serializable]
        public class vi_FAM_VisitasProgramaciones : FAM_VisitasProgramaciones
        {
            public int? IdFormulario { get; set; }
            public decimal? IdFIF { get; set; }
            public string Calle { get; set; }
            public string NumeroVivienda { get; set; }
            public string Manzana { get; set; }
            public string Edificio { get; set; }
            public string Apartamento { get; set; }
            public string EntreLaCalle { get; set; }
            public string YLaCalle { get; set; }
            public string Georeferencia { get; set; }
            public string TelefonoMobil { get; set; }
            public string TelefonoResidencial { get; set; }
            public string TelefonoOtro { get; set; }
            public decimal? IdAnimador { get; set; }
            public string Animador { get; set; }
            public string Visitante { get; set; }
            public decimal? IdPuestoVisitante { get; set; }
            public string PuestoVisitante { get; set; }
            public string Estancia { get; set; }
            public string Tipo { get; set; }
            public string TipoGestion { get; set; }
            public decimal IdRegion { get; set; }
            public string Region { get; set; }
            public string ProvinciaId { get; set; }
            public string Provincia { get; set; }
            public string MunicipioId { get; set; }
            public string Municipio { get; set; }
            public decimal IdTerritorio { get; set; }
            public string Territorio { get; set; }
            public decimal IdRed { get; set; }
            public string Red { get; set; }
            public decimal IdCuadrante { get; set; }
            public string Cuadrante { get; set; }
            public short AnoPeriodoDesde { get; set; }
            public short AnoPeriodoHasta { get; set; }
            public DateTime FechaDesde { get; set; }
            public DateTime FechaHasta { get; set; }
            public bool CerradoPeriodo { get; set; }
            public string Estatus { get; set; }
            public string RazonNoVisita { get; set; }
            public string UsuarioRegistra { get; set; }
            public string UsuarioModifica { get; set; }
            public string UsuarioEstatus { get; set; }
        }

    }
}
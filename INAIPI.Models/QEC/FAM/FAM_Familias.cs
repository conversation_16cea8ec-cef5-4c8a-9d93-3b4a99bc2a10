﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace INAIPI.Models.QEC.FAM
{
    [Serializable]
    public class FAM_Familias : BaseModel
    {
        public int IdFamilia { get; set; }
        public int? IdFormulario { get; set; }
        public decimal? IdFIF { get; set; }
        public decimal IdRed { get; set; }
        public decimal IdSubRed { get; set; }
        public byte NucleoNo { get; set; }
        public decimal IdEstancia { get; set; }
        public string Calle { get; set; }
        public string NumeroVivienda { get; set; }
        public string Manzana { get; set; }
        public string Edificio { get; set; }
        public string Apartamento { get; set; }
        public string EntreLaCalle { get; set; }
        public string YLaCalle { get; set; }
        public string Georeferencia { get; set; }
        public string TelefonoMobil { get; set; }
        public string TelefonoResidencial { get; set; }
        public string TelefonoOtro { get; set; }
        public decimal? IdAnimador { get; set; }
        public decimal IdUsuarioRegistra { get; set; }
        public DateTime FechaRegistro { get; set; }
        public decimal? IdUsuarioModifica { get; set; }
        public DateTime? FechaModifica { get; set; }
        public bool RegistroInvalido { get; set; }
        public decimal? IdUsuarioInvalida { get; set; }
        public DateTime? FechaInvalida { get; set; }
        [Serializable]
        public class vi_FAM_Familias : FAM_Familias
        {
            public vi_FAM_Familias()
            {
                Miembros = new List<FAM_FamiliasMiembros>();
            }

            public string Territorio { get; set; }
            public string Red { get; set; }
            public decimal IdCuadrante { get; set; }
            public string Cuadrante { get; set; }
            public string Estancia { get; set; }
            public string Tipo { get; set; }
            public string TipoGestion { get; set; }
            public decimal IdRegion { get; set; }
            public string Region { get; set; }
            public string ProvinciaId { get; set; }
            public string Provincia { get; set; }
            public string MunicipioId { get; set; }
            public string Municipio { get; set; }
            public string EstatusDelServicio { get; set; }
            public short IdEstatusServicio { get; set; }
            public bool Activo { get; set; }
            public string CAFI_Estrategias { get; set; }
            public string TieneLocal { get; set; }
            public string Animador { get; set; }
            public string UsRegistraFamilia { get; set; }
            public string UsModificaFamilia { get; set; }
            public string UsInvalidaFamilia { get; set; }
            public List<FAM_FamiliasMiembros> Miembros { get; set; }
        }
    }

    [Serializable]
    public class FAM_FamiliasMiembros : BaseModel
    {
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        /// <summary>
        /// 1 = NN,
        /// 2 = GESTANTE,
        /// 3 = MIEMBRO
        /// </summary>
        public byte IdTipo { get; set; }
        public decimal? IdNN { get; set; }
        public string Nombre1 { get; set; }
        public string Nombre2 { get; set; }
        public string Apellido1 { get; set; }
        public string Apellido2 { get; set; }
        public DateTime? FechaNacimiento { get; set; }
        public string Sexo { get; set; }
        public string CedulaNUI { get; set; }
        public decimal IdUsuarioRegistra { get; set; }
        public DateTime FechaRegistro { get; set; }
        public decimal? IdUsuarioModifica { get; set; }
        public DateTime? FechaModifica { get; set; }
        public bool Eliminado { get; set; }
        public decimal? IdUsuarioElimina { get; set; }
        public DateTime? FechaElimina { get; set; }
        public bool SinActaNacimiento { get; set; }
        public bool Migrante { get; set; }
        public int CantidadVisitasPracticaCrianza { get; set; }
        public DateTime? FechaUltimoCicloMenstrual { get; set; }
        public string NombreCompleto { get { return (Nombre1 ?? "").Trim() + " " + (Nombre2 ?? "").Trim() + " " + (Apellido1 ?? "").Trim() + " " + (Apellido2 ?? "").Trim(); } }
        public string NombreCompleto2 { get { return (Nombre1 ?? "").Trim() +  (Apellido1 ?? "").Trim() + " " + (Apellido2 ?? "").Trim(); } }
        public string UsuarioRegistra { get; set; }


        [Serializable]
        public class vi_FAM_FamiliasMiembros : FAM_FamiliasMiembros
        {
            public int? IdFormulario { get; set; }
            public decimal? IdFIF { get; set; }
            public decimal IdRed { get; set; }
            public string Territorio { get; set; }
            public string Red { get; set; }
            public decimal IdSubRed { get; set; }
            public decimal IdCuadrante { get; set; }
            public string Cuadrante { get; set; }
            public byte NucleoNo { get; set; }
            public decimal IdEstancia { get; set; }
            public string Estancia { get; set; }
            public string Tipo { get; set; }
            public string TipoGestion { get; set; }
            public decimal IdRegion { get; set; }
            public string Region { get; set; }
            public string ProvinciaId { get; set; }
            public string Provincia { get; set; }
            public string MunicipioId { get; set; }
            public string Municipio { get; set; }
            public string EstatusDelServicio { get; set; }
            public short IdEstatusServicio { get; set; }
            public bool Activo { get; set; }
            public string CAFI_Estrategias { get; set; }
            public string TieneLocal { get; set; }
            public string Calle { get; set; }
            public string NumeroVivienda { get; set; }
            public string Manzana { get; set; }
            public string Edificio { get; set; }
            public string Apartamento { get; set; }
            public string EntreLaCalle { get; set; }
            public string YLaCalle { get; set; }
            public string Georeferencia { get; set; }
            public string TelefonoMobil { get; set; }
            public string TelefonoResidencial { get; set; }
            public string TelefonoOtro { get; set; }
            public decimal? IdAnimador { get; set; }
            public string Animador { get; set; }
            public string UsRegistraFamiliaMiembro { get; set; }
            public string UsModificaFamiliaMiembro { get; set; }
            public bool RegistroInvalido { get; set; }
            public decimal? IdUsuarioInvalida { get; set; }
            public string UsEliminaFamiliaMiembro { get; set; }
            public string TipoMiembro { get; set; }
            public decimal Edad { get; set; }
            public int EdadEnMeses { get; set; }
            public string EdadLiteralStr { get; set; }
            public FAM_Familias Familia { get; set; }
            public string IdTipoStr
            {
                get
                {
                    string T = "NN";

                    if (IdTipo == 2)
                    {
                        T = "GESTANTE";
                    }
                    else
                    {
                        T = "MIEMBRO";
                    }
                    return T;
                }

            }
            public string strEsJefeDelHogar { get; set; }
            public string EsJefeDelHogar {
                get
                {
                    string es = JefeHogar.ToString();

                    if (es == "True")
                    {
                        return "Si";
                    }
                    else
                    {
                        return "No";
                    }
                }
            }
            public string strEstaEmbarazada { get; set; }
            public int MesesEmbarazo { get; set; }
            public string Direccion
            {
                get
                {
                    return Calle + ", Entre la " + Calle + " y la " + YLaCalle;
                }

            }
            public string strFamiliasyJefeDelHogar
            {
                get
                {
                    return IdFamilia + " | " + Nombre1 + " " + Apellido1;
                }

            }
            public override string ToString() => $"{IdFamilia} | {Nombre1} {Apellido1} | {IdFormulario}";
            public bool? JefeHogar { get; set; }
            
            public int strMesesEmbarazo
            {
                get
                {
                    if (FechaUltimoCicloMenstrual != null)
                    {
                        DateTime fechaActual = DateTime.Now;
                        var anosAnteriores = fechaActual - FechaUltimoCicloMenstrual;
                        int anos = (int)(anosAnteriores?.TotalDays / 365.25);
                        int meses = (int)(((anosAnteriores?.TotalDays / 365.25) - anos) * 12);
                        return meses;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
            public int strEdad
            {
                get
                {
                    if (FechaNacimiento != null)
                    {
                        DateTime fechaActual = DateTime.Now;

                        var anosAnteriores = fechaActual - FechaNacimiento;
                        int anos = (int)(anosAnteriores?.TotalDays / 365.25);
                        int meses = (int)(((anosAnteriores?.TotalDays / 365.25) - anos) * 12);

                        //return $"Tienes: {years} años {(months > 0 ? $"y {months} meses" : "")}";
                        return anos;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
            public DateTime? FechaUltEvaluacionEDI { get; set; }
            public string TipoCentro { get; set; }
            public decimal IdEmbarazo { get; set; }
            public int TotalNNInsritosPorEmbarazada { get; set; }
            public string Foto { get; set; }
            public string GetFoto
            {
                get
                {
                    if (Foto == null)
                    {
                        return "http://localhost:7550/Images/img-default.png";
                    }
                    return "http://localhost:7550/FileServer/" + Foto;
                }

            }
            public string GetFotoRutaFisica
            {
                get
                {

                    //string pathToFiles = System.Web.Hosting.HostingEnvironment.MapPath("/FileServer");

                    if (Foto == null)
                    {
                        return "../../Images/img-default.png";
                    }
                    return "http://localhost:7550/FileServer/" + Foto;
                }

            }

            


            public byte? IdEmbarazoEstatus { get; set; }
        }

        [Serializable]
        public class EMB_Embarazos : vi_FAM_FamiliasMiembros
        {
            public byte? IdEmbarazoEstatus { get; set; }
            public DateTime? FechaRegistra { get; set; }
        }
    }
}
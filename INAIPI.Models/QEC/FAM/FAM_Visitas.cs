﻿using INAIPI.Models.QEC.FAM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace INAIPI.Models.QEC
{
    [Serializable]
    public class FAM_Visitas : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdTipoVisita { get; set; }
        public int FamiliaVisitaNo { get; set; }
        public DateTime FechaVisita { get; set; }
        public DateTime FechaDigitacion { get; set; }
        public decimal? IdVisitante { get; set; }
        public decimal IdEstancia { get; set; }
        public byte NucleoNo { get; set; }
        public string Georeferencia { get; set; }
        public int IdPeriodo { get; set; }
        public bool VisitaRealizada { get; set; }
        public int? IdRazonNoVisita { get; set; }
        public int CantidadPersonasVisita { get; set; }
        public int NoVisitasEnPapel { get; set; }
        public byte? IdActividadEducativa { get; set; }
        public int IdTemaATratar { get; set; }
        public int? IdProgramacion { get; set; }
        public string Objetivo { get; set; }
        public string Actividades { get; set; }
        public string ObservacionesGenerales { get; set; }
        public bool? RequiereReferimiento { get; set; }
        public string EspecifiqueReferimiento { get; set; }
        public string RevisionCompromisosAnteriores { get; set; }
        public string AcompanamientoSaludNutricion { get; set; }
        public string AcompanamientoEducacion { get; set; }
        public string AcompanamientoSalud { get; set; }
        public string AcompanamientoProteccion { get; set; }
        public string CompromisosProximaVisita { get; set; }
        public string CompromisosDeLaFamilia { get; set; }
        public string TemaMasInformacion { get; set; }
        public string ComoSeSintioConLaVisita { get; set; }
        public string SugerenciaProximaVisita { get; set; }
        public string Notas { get; set; }
        public decimal IdUsuarioRegistra { get; set; }
        public DateTime FechaRegistro { get; set; }
        public decimal? IdUsuarioModifica { get; set; }
        public DateTime? FechaModifica { get; set; }
        public bool Cerrada { get; set; }
        public decimal? IdUsuarioCierra { get; set; }
        public DateTime? FechaCierre { get; set; }
        public bool Anulado { get; set; }
        public decimal? IdUsuarioAnula { get; set; }
        public int? IdRazonAnulacion { get; set; }
        public string DescripcionAnulacion { get; set; }
        public DateTime? FechaAnula { get; set; }
        public DateTime HoraInicio { get; set; }
        public DateTime HoraFin { get; set; }
        public string Mobil_IMEI { get; set; }
        public string Mobil_Guid { get; set; }

        [Serializable]
        public class vi_FAM_Visitas : FAM_Visitas
        {
            public vi_FAM_Visitas()
            {
                VisitasMiembros = new List<FAM_VisitasMiembros>();
                TemasATratar = new List<FAM_VisitasTemasTratarDetalle>();
            }
            public int IdLocal { get; set; }
            public int? IdFormulario { get; set; }
            public decimal? IdFIF { get; set; }
            public string Calle { get; set; }
            public string NumeroVivienda { get; set; }
            public string Manzana { get; set; }
            public string Edificio { get; set; }
            public string Apartamento { get; set; }
            public string EntreLaCalle { get; set; }
            public string YLaCalle { get; set; }
            public string TelefonoMobil { get; set; }
            public string TelefonoResidencial { get; set; }
            public string TelefonoOtro { get; set; }
            public decimal? IdAnimador { get; set; }
            public string Animador { get; set; }
            public string Visitante { get; set; }
            public decimal? IdPuestoVisitante { get; set; }
            public string PuestoVisitante { get; set; }
            public string Estancia { get; set; }
            public string Tipo { get; set; }
            public string TipoGestion { get; set; }
            public decimal IdRegion { get; set; }
            public string Region { get; set; }
            public string ProvinciaId { get; set; }
            public string Provincia { get; set; }
            public string MunicipioId { get; set; }
            public string Municipio { get; set; }
            public decimal IdTerritorio { get; set; }
            public string Territorio { get; set; }
            public decimal IdRed { get; set; }
            public string Red { get; set; }
            public decimal IdSubRed { get; set; }
            public decimal IdCuadrante { get; set; }
            public string Cuadrante { get; set; }
            public short AnoPeriodoDesde { get; set; }
            public short AnoPeriodoHasta { get; set; }
            public bool CerradoPeriodo { get; set; }
            public string UsRegistraVisita { get; set; }
            public string UsModificaVisita { get; set; }
            public string UsCierraVisita { get; set; }
            public string UsAnulaVisita { get; set; }
            public FAM_VisitasMiembros.vi_FAM_VisitasMiembros Miembro { get; set; }
            public List<FAM_VisitasMiembros> VisitasMiembros { get; set; }
            public List<FAM_VisitasTemasTratarDetalle> TemasATratar { get; set; }
        }
    }

    [Serializable]
    public class FAM_VisitasMiembros : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public int MiembroVisitaNo { get; set; }
        public int AcompananteCuidaNN { get; set; }
        public bool? EmbarazoPlanificado { get; set; }
        public bool? EstaEstudiando { get; set; }
        public bool? AtencionPsicologica { get; set; }
        public bool? Discapacidad { get; set; }
        public bool? DiagnosticoMedico { get; set; }
        public bool ?SeguimientoMedico { get; set; }
        public byte IdTipoDiscapacidad { get; set; }
        public bool? RequiereReferimiento { get; set; }
        public byte IdCentroTerapeutico { get; set; }
        public bool? Vulnerado { get; set; }
        public byte IdTipoVulneracion { get; set; }
        public string ComentarioVulnerado { get; set; }
        public bool? VulneradoAtendido { get; set; }
        public bool? VulneradoRequiereDerivacion { get; set; }
        public bool? ControlSalud { get; set; }
        public DateTime? FechaUltimoControl { get; set; }
        public bool? LME { get; set; }
        public int? IdRazonNoLME { get; set; }
        public bool? PadreEnCrianza { get; set; }
        public TimeSpan? HoraInicio { get; set; }
        public bool? SenalAlertaEnDesarrollo { get; set; }
        public bool? GestionSeguroSalud { get; set; }
        public bool? HaParticipadoEnTalleres { get; set; }
        public bool? ParticipaOrganizacionComunicacion { get; set; }
        public bool? RiesgoHabitacional { get; set; }
        public bool? VacunacionAlDia { get; set; }
        public TimeSpan? HoraFin { get; set; }
        public DateTime? FechaProximaVisita { get; set; }
        public string ProximoCompromiso { get; set; }
        public decimal PorcCompromisoActual { get; set; }
        public string Observaciones { get; set; }
        public decimal IdUsuarioRegistra { get; set; }
        public DateTime FechaRegistro { get; set; }
        public decimal? IdUsuarioActualizaRegistro { get; set; }
        public DateTime? FechaActualizacionRegistro { get; set; }

        [Serializable]
        public class vi_FAM_VisitasMiembros : FAM_VisitasMiembros
        {
            public vi_FAM_VisitasMiembros()
            {
                PadreEnCrianzaMiembros = new List<FAM_VisitasMiembrosPadreEnCrianza>();
                MiembrosAltenativos = new List<FAM_VisitasMiembrosAltenativosDetalle>();
                AnimosMiembro = new List<FAM_VisitasMiembrosAnimosDetalle>();
                SenalesAlertaMiembros = new List<FAM_VisitasMiembrosSenalesAlertaDetalle>();
                Discapacidades = new List<FAM_VisitasMiembrosTipoDiscapacidadDetalle>();
                TiposVulnerabilidadesMiembros = new List<FAM_VisitasMiembrosTipoVulneracionDetalle>();
                TalleresMiembros = new List<FAM_VisitasMiembrosTalleresDetalle>();
                RiesgosHabitacionales = new List<FAM_VisitasMiembrosRiesgoHabitacionalDetalle>();
                CentrosTerapeuticos = new List<FAM_VisitasMiembrosCentrosTerapeuticosDetalle>();
                PracticasCrianzaDetalles = new List<FAM_VisitasMiembrosPracticasCrianzaDetalle>();
                CompromisosDetalle = new List<FAM_VisitasMiembrosCompromisosDetalle>();
            }

            public int FamiliaVisitaNo { get; set; }
            public DateTime FechaVisita { get; set; }
            public decimal? IdVisitante { get; set; }
            public decimal IdEstancia { get; set; }
            public byte NucleoNo { get; set; }
            public string Georeferencia { get; set; }
            public int IdPeriodo { get; set; }
            public string Objetivo { get; set; }
            public string Actividades { get; set; }
            public string Notas { get; set; }
            public decimal? IdUsuarioModifica { get; set; }
            public DateTime? FechaModifica { get; set; }
            public bool Cerrada { get; set; }
            public decimal? IdUsuarioCierra { get; set; }
            public DateTime? FechaCierre { get; set; }
            public bool Anulado { get; set; }
            public decimal? IdUsuarioAnula { get; set; }
            public DateTime? FechaAnula { get; set; }
            public string UsRegistraVisitaMiembro { get; set; }
            public string UsuarioActualizaRegistro { get; set; }
            public FAM_FamiliasMiembros.vi_FAM_FamiliasMiembros MiembroDetalle { get; set; }
            public FAM_VisitasMiembrosPadreEnCrianza PadreEnCrianzaMiembro { get; set; }
            public List<FAM_VisitasMiembrosPadreEnCrianza> PadreEnCrianzaMiembros { get; set; }
            public List<FAM_VisitasMiembrosAltenativosDetalle> MiembrosAltenativos { get; set; }
            public List<FAM_VisitasMiembrosAnimosDetalle> AnimosMiembro { get; set; }
            public List<FAM_VisitasMiembrosSenalesAlertaDetalle> SenalesAlertaMiembros { get; set; }
            public List<FAM_VisitasMiembrosTipoDiscapacidadDetalle> Discapacidades { get; set; }
            public List<FAM_VisitasMiembrosTipoVulneracionDetalle> TiposVulnerabilidadesMiembros { get; set; }
            public List<FAM_VisitasMiembrosTalleresDetalle> TalleresMiembros { get; set; }
            public List<FAM_VisitasMiembrosPracticasCrianzaDetalle> PracticasCrianzaDetalles { get; set; }
            public List<FAM_VisitasMiembrosRiesgoHabitacionalDetalle> RiesgosHabitacionales { get; set; }
            public List<FAM_VisitasMiembrosCentrosTerapeuticosDetalle> CentrosTerapeuticos { get; set; }
            public List<FAM_VisitasMiembrosCompromisosDetalle> CompromisosDetalle { get; set; }
        }
    }

    [Serializable]
    public class FAM_VisitasMiembrosPadreEnCrianza : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdPregunta { get; set; }
        public byte IdRespuesta { get; set; }
        public bool Valor { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosAltenativosDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdMiembroAlternativo { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosAnimosDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdAnimo { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosSenalesAlertaDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdSenalAlerta { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosTipoDiscapacidadDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdTipoDiscapacidad { get; set; }
        public string OtraDiscapacidad { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosTipoVulneracionDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdTipoVulneracion { get; set; }
        public string OtraVulneracion { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosTalleresDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdTaller { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosPracticasCrianzaDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdPreguntaPracticasCrianza { get; set; }
        public byte IdOpcion { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosRiesgoHabitacionalDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdRiesgoHabitacional { get; set; }
        public string OtraSituacion { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosCentrosTerapeuticosDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdCentroTerapeutico { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasMiembrosCompromisosDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public byte IdCompromiso { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasTemasTratarDetalle : BaseModel
    {
        public int IdVisita { get; set; }
        public int IdFamilia { get; set; }
        public byte IdMiembro { get; set; }
        public int IdTema { get; set; }
        public string Tema { get; set; }
        public bool Estado { get; set; }
    }

    [Serializable]
    public class FAM_VisitasRazonesAnulacion : BaseModel
    {
        public int IdRazon { get; set; }
        public string Razon { get; set; }
        public bool Inactivo { get; set; }
    }
}
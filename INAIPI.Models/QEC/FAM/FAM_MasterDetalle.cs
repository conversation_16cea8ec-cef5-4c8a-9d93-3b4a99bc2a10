﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.FAM
{
    [Serializable]
    public class FAM_MasterDetalle : BaseModel
    {
        public FAM_MasterDetalle()
        {
            RazonesNoVisita = new List<FAM_VisitasProgramacionesRazonesNoVisita>();
            TemasATratar = new List<FAM_VisitasTemasATratar>();
            ActividadesEducativas = new List<FAM_VisitasActividadesEducativas>();
            RazonesLME = new List<FAM_VisitasMiembrosRazonesNoLME>();
            MiembrosAlternativos = new List<FAM_MiembrosAlternativos>();
            MiembrosAnimos = new List<FAM_VisitasMiembrosAnimos>();
            PadreEnCrianzaPreguntas = new List<FAM_VisitasMiembrosPadreEnCrianzaPreguntas>();
            PadreEnCrianzaRespuestas = new List<FAM_VisitasMiembrosPadreEnCrianzaRespuestas>();
            RiesgosHabitacionales = new List<FAM_MiembrosRiesgoHabitacional>();
            SenalesAlertas = new List<FAM_MiembrosSenalesAlerta>();
            Talleres = new List<FAM_MiembrosTalleres>();
            TiposDiscapacidades = new List<FAM_MiembrosTipoDiscapacidad>();
            CentrosTerapeuticos = new List<FAM_CentrosTerapeuticos>();
            TiposVulnerabilidades = new List<FAM_MiembrosTipoVulneracion>();
            Compromisos = new List<FAM_MiembrosCompromisos>();
            PreguntasPracticasCrianza = new List<FAM_MiembrosPreguntasCrianza>();
            OpcionesPracticasCrianza = new List<FAM_MiembrosPreguntasCrianzaOpciones>();
        }

        public List<FAM_VisitasProgramacionesRazonesNoVisita> RazonesNoVisita { get; set; }
        public List<FAM_VisitasTemasATratar> TemasATratar { get; set; }
        public List<FAM_VisitasActividadesEducativas> ActividadesEducativas { get; set; }
        public List<FAM_VisitasMiembrosRazonesNoLME> RazonesLME { get; set; }
        public List<FAM_MiembrosAlternativos> MiembrosAlternativos { get; set; }
        public List<FAM_VisitasMiembrosAnimos> MiembrosAnimos { get; set; }
        public List<FAM_VisitasMiembrosPadreEnCrianzaPreguntas> PadreEnCrianzaPreguntas { get; set; }
        public List<FAM_VisitasMiembrosPadreEnCrianzaRespuestas> PadreEnCrianzaRespuestas { get; set; }
        public List<FAM_MiembrosRiesgoHabitacional> RiesgosHabitacionales { get; set; }
        public List<FAM_MiembrosSenalesAlerta> SenalesAlertas { get; set; }
        public List<FAM_MiembrosTalleres> Talleres { get; set; }
        public List<FAM_MiembrosTipoDiscapacidad> TiposDiscapacidades { get; set; }
        public List<FAM_CentrosTerapeuticos> CentrosTerapeuticos { get; set; }
        public List<FAM_MiembrosTipoVulneracion> TiposVulnerabilidades { get; set; }
        public List<FAM_MiembrosCompromisos> Compromisos { get; set; }
        public List<FAM_MiembrosPreguntasCrianza> PreguntasPracticasCrianza { get; set; }
        public List<FAM_MiembrosPreguntasCrianzaOpciones> OpcionesPracticasCrianza { get; set; }
    }
}

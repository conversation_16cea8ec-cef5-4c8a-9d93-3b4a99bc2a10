﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;


namespace INAIPI.Models.QEC
{
    [Serializable]
    public class NN : BaseModel
    {
        #region DATOS NNs
        public decimal IdNN { get; set; }

        public decimal IdFIF { get; set; }
        public decimal IdNNFIF { get; set; }
        public string IdFIFPOS { get; set; }
        public string Nombre { get; set; }
        public string Nombres { get; set; }
        public string Apellidos { get; set; }
        public string ParentescoJefeHogar { get; set; }
        public DateTime? FechaNacimiento { get; set; }
        public int? Edad { get; set; }
        public string EdadLiteral { get; set; }
        public DateTime? FechaEvaluacion { get; set; }

        //public string EdadEvaluacionDeSaludStr
        //{
        //    get
        //    {
        //        //    DateTime.TryParse((FechaUltimaES.ToString()??"0"), out DateTime EdadEvaluacionSalud);
        //        //    return EdadEvaluacionSalud.CalcularTiempoStr(FechaNacimiento);
        //        if (FechaEvaluacion == null)
        //            return string.Empty;

        //        //return FechaNacimiento?.CalcularTiempoStr(FechaEvaluacion);
        //        return FechaNacimiento?.CalcularTiempoStr(FechaEvaluacion.Value);
        //    }
        //}

        public string RangoEtario { get; set; }
        public string RangoEtarioCAIPI2 { get; set; }
        public string RangoEtarioCAIPISala { get; set; }
        public string RangoEtarioCAFI { get; set; }
        public string RangoEtarioCAFI2 { get; set; }
        public string RangoEtarioCAFISala { get; set; }
        public string RangoEtarioAbastecimiento { get; set; }
        public string Sexo { get; set; }
        public string SexoDetalle
        {
            get
            {
                string n = string.Empty;

                switch (Sexo)
                {
                    case "M":
                        n = "Masculino";
                        break;
                    case "F":
                        n = "Femenino";
                        break;
                }

                return n;
            }
        }
        public string LugarNacimiento { get; set; }
        public string Nacionalidad { get; set; }
        public string Cedula { get; set; }
        public bool TieneActaNacimiento { get; set; }
        public bool TieneSeguroSalud { get; set; }
        public string TieneSeguroSaludStr { get { return (TieneSeguroSalud ? "SI" : "NO"); } }
        public string SeguroNoAfiliado { get; set; }
        public int IdARS { get; set; }
        public string ARS { get; set; }
        public bool HijoEmpleadaCentro { get; set; }
        public bool SaludEsDiscapacitado { get; set; }
        public string SaludDiscapacidad { get; set; }
        public string SaludDiagnostico { get; set; }
        public string SaludTratamiento { get; set; }
        public string SaludTipoSangre { get; set; }
        public string SaludRh { get; set; }
        public bool SaludEsAlergico { get; set; }
        public string SaludTipoAlergia { get; set; }
        public string ViviendaProvinciaId { get; set; }
        public string Provincia { get; set; }
        public string ViviendaMunicipioId { get; set; }
        public string Municipio { get; set; }
        public string Territorio { get; set; }
        public string ViviendaDistritoMunicipalId { get; set; }
        public string DistritoMunicipal { get; set; }
        public string ViviendaSeccionId { get; set; }
        public string Seccion { get; set; }
        public string ViviendaBarrioId { get; set; }
        public string Barrio { get; set; }
        public string ViviendaSubBarrioId { get; set; }
        public string SubBarrio { get; set; }
        public string ViviendaCalle { get; set; }
        public string ViviendaCalleNo { get; set; }
        public string ViviendaPuntoReferencia { get; set; }
        public string ViviendaTelefono1 { get; set; }
        public string ViviendaTelefono2 { get; set; }
        public decimal ViviendaNucleoNo { get; set; }
        public string MadreNombre { get { return (MadreNombre1.Trim() + " " + MadreNombre2.Trim() + " " + MadreApellido1.Trim() + " " + MadreApellido2.Trim()).Replace("  ", " ").Trim(); } }
        public string MadreNombre1 { get; set; }
        public string MadreNombre2 { get; set; }
        public string MadreApellido1 { get; set; }
        public string MadreApellido2 { get; set; }
        public string MadreCedula { get; set; }
        public DateTime? MadreFechaNacimiento { get; set; }
        public string MadreTipoSangre { get; set; }
        public string MadreRh { get; set; }
        public bool MadreViveConNN { get; set; }
        public string MadreCelular { get; set; }
        public string MadreTelCasa { get; set; }
        public string MadreDireccion { get; set; }
        public string MadreCalleNo { get; set; }
        public string MadrePuntoReferencia { get; set; }
        public string MadreProvinciaId { get; set; }
        public string MadreMunicipioId { get; set; }
        public string MadreDistritoMunicipalId { get; set; }
        public string MadreSeccionId { get; set; }
        public string MadreBarrioId { get; set; }
        public string MadreSubBarrioId { get; set; }
        public bool MadreEstaEmbarazada { get; set; }
        public decimal MadreEmbarazadaMesesGestacion { get; set; }
        public bool MadreEmbarazadaLactante { get; set; }
        public string MadreNivelEstudios { get; set; }
        public string MadreOcupacion { get; set; }
        public string PadreNombre { get { return (PadreNombre1.Trim() + " " + PadreNombre2.Trim() + " " + PadreApellido1.Trim() + " " + PadreApellido2.Trim()).Replace("  ", " ").Trim(); } }
        public string PadreNombre1 { get; set; }
        public string PadreNombre2 { get; set; }
        public string PadreApellido1 { get; set; }
        public string PadreApellido2 { get; set; }
        public string PadreCedula { get; set; }
        public DateTime? PadreFechaNacimiento { get; set; }
        public string PadreTipoSangre { get; set; }
        public string PadreRh { get; set; }
        public bool PadreViveConNN { get; set; }
        public string PadreCelular { get; set; }
        public string PadreTelCasa { get; set; }
        public string PadreDireccion { get; set; }
        public string PadreCalleNo { get; set; }
        public string PadrePuntoReferencia { get; set; }
        public string PadreProvinciaId { get; set; }
        public string PadreMunicipioId { get; set; }
        public string PadreDistritoMunicipalId { get; set; }
        public string PadreSeccionId { get; set; }
        public string PadreBarrioId { get; set; }
        public string PadreSubBarrioId { get; set; }
        public string PadreNivelEstudios { get; set; }
        public string PadreOcupacion { get; set; }
        public string TutorNombre { get { return (TutorNombre1.Trim() + " " + TutorNombre2.Trim() + " " + TutorApellido1.Trim() + " " + TutorApellido2.Trim()).Replace("  ", " ").Trim(); } }
        public string TutorNombre1 { get; set; }
        public string TutorNombre2 { get; set; }
        public string TutorApellido1 { get; set; }
        public string TutorApellido2 { get; set; }
        public string TutorCedula { get; set; }
        public DateTime? TutorFechaNacimiento { get; set; }
        public string TutorTipoSangre { get; set; }
        public string TutorRh { get; set; }
        public bool TutorViveConNN { get; set; }
        public string TutorCelular { get; set; }
        public string TutorTelCasa { get; set; }
        public string TutorDireccion { get; set; }
        public string TutorCalleNo { get; set; }
        public string TutorPuntoReferencia { get; set; }
        public string TutorProvinciaId { get; set; }
        public string TutorMunicipioId { get; set; }
        public string TutorDistritoMunicipalId { get; set; }
        public string TutorSeccionId { get; set; }
        public string TutorBarrioId { get; set; }
        public string TutorSubBarrioId { get; set; }
        public string TutorNivelEstudios { get; set; }
        public string TutorOcupacion { get; set; }
        public string SeguridadLlamarA { get; set; }
        public string SeguridadLlamarAParentesco { get; set; }
        public string SeguridadLlamarATelefono1 { get; set; }
        public string SeguridadLlamarATelefono2 { get; set; }
        public bool AnexosActaNacimiento { get; set; }
        public bool AnexosFotos2x2 { get; set; }
        public bool AnexosConstanciaLaboral { get; set; }
        public bool AnexosFotosAutorizados { get; set; }
        public bool AnexosComprobanteDomicilio { get; set; }
        public bool AnexosContactoPediatra { get; set; }
        public string Observaciones { get; set; }
        public int NoOrden { get; set; }
        public string PathPerfilPhoto { get; set; }
        public string NoActaNacimientoRazon { get; set; }
        public string ViviendaEdificio { get; set; }
        public string ViviendaApartamento { get; set; }
        public bool IngresoSinFicha { get; set; }
        public int Seguro_IdRegionalSalud { get; set; }
        public string Seguro_RegionalSalud { get; set; }
        public int Seguro_IdUNAP { get; set; }
        public string Seguro_UNAP { get; set; }
        public string Seguro_Titular { get; set; }
        public string Seguro_TitularNombre
        {
            get
            {
                string result = "";

                switch (Seguro_Titular)
                {
                    case "MADRE":
                        result = MadreNombre;
                        break;

                    case "PADRE":
                        result = PadreNombre;
                        break;

                    case "TUTOR":
                        result = TutorNombre;
                        break;
                    default:
                        if (MadreNombre.Trim().Length > 0)
                        {
                            result = MadreNombre;
                        }

                        if (PadreNombre.Trim().Length > 0)
                        {
                            result = PadreNombre;
                        }

                        if (TutorNombre.Trim().Length > 0)
                        {
                            result = TutorNombre;
                        }
                        break;
                }

                return result;
            }
        }
        public string Nombre1 { get; set; }
        public string Nombre2 { get; set; }
        public string Apellido1 { get; set; }
        public string Apellido2 { get; set; }
        public decimal? IdPais { get; set; }
        
        public override string ToString() => $"{Nombre1.Trim()} {Apellido1.Trim()} [{IdNN.ToString()}] [{Cedula}] [{Sexo}] [{EdadLiteral}]";

        public string Seguro_TitularCedula
        {
            get
            {
                string result = "";

                switch (Seguro_Titular)
                {
                    case "MADRE":
                        result = MadreCedula;
                        break;

                    case "PADRE":
                        result = PadreCedula;
                        break;

                    case "TUTOR":
                        result = TutorCedula;
                        break;
                }

                return result;
            }
        }
        public string Seguro_TitularNSS { get; set; }
        public byte Seguro_Parentesco { get; set; }
        public enum enumSeguro_Parentesco { Padre = 1, Madre, Esposo, Esposa, Hijo, Hija, Hermano, Hermana, Abuelo, Abuela, Sobrino, Sobrina, Tío, Tía, Nieto, Nieta, Hijastro, Hiastra }
        public string Seguro_ParentescoStr
        {
            get
            {
                string str = "";

                str = Enum.GetName(typeof(enumSeguro_Parentesco), Seguro_Parentesco);

                return str.ToUpper();
            }
        }
        public int ActaMunicipio { get; set; }
        public int ActaOficialia { get; set; }
        public int ActaLibro { get; set; }
        public int ActaFolio { get; set; }
        public int ActaNo { get; set; }
        public int ActaAnoLibro { get; set; }
        public byte Seguro_TipoAfiliacio { get; set; }
        public enum enumSeguro_TipoAfiliacio { NORMAL = 1, RE_AFILIACIÓN, DISCAPACIDAD, RECIÉN_NACIDOS, PERSONAS_CON_VIH }
        public string Seguro_TipoAfiliacioStr
        {
            get
            {
                string str = "";

                str = Enum.GetName(typeof(enumSeguro_TipoAfiliacio), Seguro_TipoAfiliacio);

                return str.Replace("_", " ");
            }
        }
        public bool Seguro_Actualizado { get; set; }
        public string Seguro_ActualizadoStr { get { return (Seguro_Actualizado ? "SI" : "NO"); } }
        public decimal PesoLB { get; set; }
        public decimal PesoKG { get; set; }
        public decimal TallaCMS { get; set; }
        public decimal PerimetroCefalicoCMS { get; set; }
        public int Percentil { get; set; }
        public DateTime? FechaUltimaES { get; set; }
        public enum enumpzPerimetroCefalico : byte { NO_APLICA = 0, MUY_GRANDE, GRANDE, NORMAL, PEQUEÑO, MUY_PEQUEÑO }
        public enumpzPerimetroCefalico pzPerimetroCefalico { get; set; }
        public string pzPerimetroCefalicoStr
        {
            get
            {
                string str = pzPerimetroCefalico.ToString();
                return str.Replace("_", " ");
            }
        }
        //TODO: Fijate como esta en el modelo de evaluaciones
        public enum enumpzPesoEdad : byte { NO_APLICA = 0, OBESIDAD, SOBREPESO, RIESGO_DE_SOBREPESO, NORMAL, DESNUTRICIÓN_LEVE, DESNUTRICIÓN_MODERADA, DESNUTRICIÓN_SEVERA }
        public enumpzPesoEdad pzPesoEdad { get; set; }
        public string pzPesoEdadStr
        {
            get
            {
                string str = pzPesoEdad.ToString();
                return str.Replace("_", " ");
            }
        }
        public enum enumpzLongitudEdad : byte { NO_APLICA = 0, MUY_ALTO, ALTO, NORMAL, RETARDO_DE_CRECIMIENTO_MODERADO, RETARDO_DE_CRECIMIENTO_SEVERO }
        public enumpzLongitudEdad pzLongitudEdad { get; set; }
        public string pzLongitudEdadStr
        {
            get
            {
                string str = pzLongitudEdad.ToString();
                return str.Replace("_", " ");
            }
        }
        public enum enumpzPesoLongitud : byte { NO_APLICA = 0, OBESIDAD, RIESGO_ALTO_DE_OBESIDAD, RIESGO_LEVE_DE_OBESIDAD, NORMAL, DESNUTRICIÓN_LEVE, DESNUTRICIÓN_MODERADA, DESNUTRICIÓN_SEVERA }
        public enumpzPesoLongitud pzPesoLongitud { get; set; }
        public string pzPesoLongitudStr
        {
            get
            {
                string str = pzPesoLongitud.ToString();
                return str.Replace("_", " ");
            }
        }
        public enum enumpzPesoEstatura : byte { NO_APLICA = 0, OBESIDAD, RIESGO_ALTO_DE_OBESIDAD, RIESGO_LEVE_DE_OBESIDAD, NORMAL, DESNUTRICIÓN_LEVE, DESNUTRICIÓN_MODERADA, DESNUTRICIÓN_SEVERA }
        public enumpzPesoEstatura pzPesoEstatura { get; set; }
        public string pzPesoEstaturaStr
        {
            get
            {
                string str = pzPesoEstatura.ToString();
                return str.Replace("_", " ");
            }
        }
        public INS_NNProximaEvaluacionSalud ProximaFechaEvaluacionSalud { get; set; }
        public virtual int? VacunasPendientes { get; set; }
        public virtual string VacunasAlDia
        {
            get
            {
                string n = "NO";
                if (VacunasPendientes == 0)
                {
                    n = "SI";
                }

                return n;
            }
        }
        public decimal IdUsuario { get; set; }
        public long IdEvaluacion { get; set; }

        public string UsuarioRegistra { get; set; }
        public string IdEstanciaEvaluacion { get; set; }

        //public virtual int VacunasRealizadas { get; set; }

        #endregion DATOS NNs

        #region DATOS INSCRIPCIÓN
        public string Sala { get; set; }
        public decimal IdEstancia { get; set; }
        public string Region { get; set; }
        public string MunicipioCentro { get; set; }
        public string Red { get; set; }
        public string Estancia { get; set; }
        public string Tipo { get; set; }
        public string TipoGestion { get; set; }
        public string EstanciaDisplay { get { return Estancia + " | " + IdEstancia.ToString() + " | " + Tipo + " | " + TipoGestion; } }

        public string FueraDelServicio { get; set; }
        public string Estatus { get { return (FueraDelServicio.ToUpper() == "SI" ? "FUERA DEL SERVICIO" : "INSCRITO"); } }

        #endregion DATOS INSCRIPCIÓN

        [Serializable]
        public class INS_MovimientosNN : NN
        {
            public int IdMovimiento { get; set; }
            public decimal IdEstancia { get; set; }
            public string Estancia { get; set; }
            public string AsistenciaHorario { get; set; }
            public string AsistenciaGrupo { get; set; }
            public string AsistenciaDia { get; set; }
            public string FueraDelServicio { get; set; }
            public DateTime FechaMovimiento { get; set; }
            public decimal IdUsuario { get; set; }
            public string Usuario { get; set; }
            public int IdTipoMovimiento { get; set; }
            public string TipoMovimiento { get; set; }
            public string RazonFueraDelServicio { get; set; }
        }

        #region PROXIMA FECHA EVALUACION
        [Serializable]
        public class INS_NNProximaEvaluacionSalud
        {
            public decimal IdNN { get; set; }
            public long IdEvaluacion { get; set; }
            public DateTime FechaProximaEvaluacion { get; set; }
            public bool FueNotificado { get; set; }
            public bool FueEvaluado { get; set; }
            public DateTime FechaSistema { get; set; }
        }

        #endregion

        //#region DOMUMENTOS
        //[Serializable]
        //[System.ComponentModel.DataAnnotations.Schema.TableAttribute("INS_NNDocumentos")]
        //public class INS_NNDocumentos : NN
        //{
        //    public int IdDocumento { get; set; }
        //    //public decimal IdNN { get; set; }
        //    public int IdTipoDocumento { get; set; }
        //    public string NombreArchivo { get; set; }
        //    public string NombreArchivoDisplay
        //    {

        //        get
        //        {
        //            if (string.IsNullOrEmpty(NombreArchivo))
        //                return string.Empty;
        //            else
        //            {
        //                string idnn = IdNN.ToString() + "_";
        //                return NombreArchivo.Replace(idnn, "");
        //            }
        //        }
        //    }
        //    public string RutaArchivo { get; set; }
        //    public DateTime FechaCreacion { get; set; }
        //    public decimal IdUsuarioCrea { get; set; }
        //    public DateTime FehaModificacion { get; set; }
        //    public decimal IdUsuarioModifica { get; set; }
        //    public int NoSecuencia { get; set; }
        //}
        //#endregion

        [Serializable]
        public class NNConsulta : NN
        {
            //--ASISTENCIA
            public decimal? PorcentajeAsistencias { get; set; }
            public int? Año { get; set; }
            public int? Mes { get; set; }

            //--VACUNAS
            public override int? VacunasPendientes { get; set; }
            public int? VacunasRealizadas { get; set; }
            public override string VacunasAlDia
            {
                get
                {
                    string n = "INCOMPLETO";
                    if (VacunasPendientes == 0)
                    {
                        n = "COMPLETO";
                    }

                    return n;
                }
            }

            //--INCIDENCIAS DE SALUD
            public int? IdIncidencia { get; set; }
            public string IncidenciaTipo { get; set; }
            public DateTime? FechaIncidencia { get; set; }
            public int? CantidadIncidencias { get; set; }

            //--CASOS DE PROTECCION
            public Decimal? IdCaso { get; set; }
            public DateTime? FechaAlerta { get; set; }
            public int? IdTipoCaso { get; set; }
            public string TipoCaso { get; set; }

            public int? CantidadCasos { get; set; }

            //--VISITAS DOMICILIARIAS
            public int? CantidadVisitas { get; set; }

            //--INDICE DE VULNERABILIDAD MANUALES
            public decimal? IdIndiceManual { get; set; }
            public decimal? PuntosManual { get; set; }
            public DateTime? FechaIndiceManual { get; set; }

            //--INDICE DE VULNERABILIDAD DIGITAL
            public decimal? IdIndiceDigital { get; set; }
            public decimal? PuntosDigital { get; set; }
            public DateTime? FechaIndiceDigital { get; set; }


            //--VERIFICAR SOLICITUD DE TRANSFERENCIA Y RETIRO
            public decimal? IdSolicitud { get; set; }

            public bool Aplicado { get; set; }



            //INDICE DE VULNERABILDIAD TODO
            public int IdFormulario { get; set; }

            public int IdMiembro { get; set; }

            public int TipoIndiceId
            {
                get
                {
                    int tipo = 0;
                    if (FechaIndiceManual != null && FechaIndiceDigital != null)
                    {
                        if (FechaIndiceManual > FechaIndiceDigital)
                        {
                            tipo = 2;

                        }
                        else
                        {
                            tipo = 1;

                        }
                    }
                    else
                    {
                        if (FechaIndiceManual != null)
                        {
                            tipo = 2;
                        }
                        else
                        {
                            tipo = 1;
                        }
                    }

                    return tipo;
                }

            }
            public string TipoIndiceNombre
            {
                get
                {
                    string TipoNombre = string.Empty;
                    if (TipoIndiceId == 1)
                    {
                        TipoNombre = "DIGITAL";
                    }
                    else
                    {
                        TipoNombre = "MANUAL";
                    }
                    return TipoNombre;
                }
            }

        }
        [Serializable]
        public class vi_INX_IndicesTodos
        {
            public int TipoIndice { get; set; }
            public decimal IdIndice { get; set; }
            public DateTime FechaIndice { get; set; }
            decimal IdFormulario { get; set; }
            decimal IdMiembro { get; set; }
            public int IdEsquema { get; set; }
            public string Edad { get; set; }
            public decimal ValorEsquema { get; set; }
            public decimal Puntos { get; set; }
            public int IdIndicador { get; set; }
            public string Indicador { get; set; }
            public string IndicadorStr { get => $"{Indicador} [{PuntosIndicador.ToString("N0")}/{ValorIndicador.ToString("N0")}]"; }
            public decimal ValorIndicador { get; set; }
            public decimal PuntosIndicador { get; set; }
            public int IdDato { get; set; }
            public string Dato { get; set; }
            public string DatoStr { get => $"{Dato}: {DatoLevantado} [Puntuación: {PuntosDato.ToString("N0")}/{ValorDato.ToString("N0")}]"; }
            public string DatoLevantado { get; set; }
            public decimal ValorDato { get; set; }
            public decimal PuntosDato { get; set; }
        }


    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.SAD
{
    [Serializable]
    public class SAD_Alerta : BaseModel
    {
        public int IdAlerta { get; set; }
        public decimal IdNN { get; set; }
        public decimal IdEstancia { get; set; }
        public decimal IdUsuario { get; set; }
        public DateTime FechaSistema { get; set; }
        public DateTime FechaAlerta { get; set; }
        public bool RecibeTerapia { get; set; }
        public bool TieneDiagnostico { get; set; }
        public string Comentario { get; set; }
        public int IdEstado { get; set; }
        
        [Serializable]
        public class vi_SAD_Alerta : SAD_Alerta
        {
            public NN NN { get; set; }
            public SAD_AlertaEstado AlertaEstado { get; set; }
            public INS.INS_Usuarios Usuario { get; set; }
            public TER.TER_Estancias  Estancia { get; set; }
            public List<SAD_AlertaTerapia> Terapias { get; set; }
            public List<SAD_AlertaSenalAlerta> SenalesALerta { get; set; }
            public List<SAD_AlertaDiscapacidad> Discapacidades { get; set; }
            public List<SAD_AlertaCentroTerapeutico> CentrosTerapeuticos { get; set; }
        }


        //Terapias
        [Serializable]
        public class SAD_AlertaTerapia : BaseModel
        {
            public int IdAlerta { get; set; }
            public int IdTerapia { get; set; }
            public string Comentario { get; set; }
            public bool Estado { get; set; }
            public SAD_Terapias Terapia { get; set; }
        }

        //Señales de alerta
        [Serializable]
        public class SAD_AlertaSenalAlerta : BaseModel
        {
            public int IdAlerta { get; set; }
            public int IdSenalAlerta { get; set; }
            public string Comentario { get; set; }
            public bool Estado { get; set; }
            public SAD_SenalAlerta SenalAlerta { get; set; }
        }

        //Discapacidad
        [Serializable]
        public class SAD_AlertaDiscapacidad : BaseModel
        {
            public int IdAlerta { get; set; }
            public int IdDiscapacidad { get; set; }
            public string Comentario { get; set; }
            public bool Estado { get; set; }
            public SAD_Discapacidad Discapacidad { get; set; }
        }

        //Centros Terapeuticos
        [Serializable]
        public class SAD_AlertaCentroTerapeutico : BaseModel
        {
            public int IdAlerta { get; set; }
            public byte IdCentroTerapeutico { get; set; }
            public string Comentario { get; set; }
            public bool Estado { get; set; }
            public FAM.FAM_CentrosTerapeuticos CentroTerapeutico { get; set; }
        }
    }
}

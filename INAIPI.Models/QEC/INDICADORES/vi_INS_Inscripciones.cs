﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.INDICADORES
{
    [Serializable]
    public class vi_INS_Inscripciones : BaseModel
    {
        public decimal IdInscripcion { get; set; }
        public decimal IdEstancia { get; set; }
        public string Estancia { get; set; }
        public string Tipo { get; set; }
        public string TipoGestion { get; set; }
        public decimal IdEstanciaPrincipal { get; set; }
        public decimal IdProvincia { get; set; }
        public decimal IdMunicipio { get; set; }
        public decimal IdDistrito { get; set; }
        public decimal IdSector { get; set; }
        public string Director { get; set; }
        public string Direccion { get; set; }
        public string Telefono { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }
        public decimal? IdRegion { get; set; }
        public string Region { get; set; }
        public decimal IdTerritorio { get; set; }
        public string Territorio { get; set; }
        public decimal IdRed { get; set; }
        public string Red { get; set; }
        public string ProvinciaId { get; set; }
        public string Provincia { get; set; }
        public string MunicipioId { get; set; }
        public string Municipio { get; set; }
        public decimal IdNN { get; set; }
        public decimal IdFIF { get; set; }
        public decimal IdNNFIF { get; set; }
        public string IdFIFPOS { get; set; }
        public string Nombre { get; set; }
        public string Nombre1 { get; set; }
        public string Nombre2 { get; set; }
        public string Apellido1 { get; set; }
        public string Apellido2 { get; set; }
        public bool ParticipoInauguracion { get; set; }
        public int CantidadFamiliaresNN { get; set; }
        public decimal IdSubRed { get; set; }
        public string Nombres { get; set; }
        public string Apellidos { get; set; }
        public string ParentescoJefeHogar { get; set; }
        public DateTime? FechaNacimiento { get; set; }
        public int? Edad { get; set; }
        public string EdadLiteral { get; set; }
        public string RangoEtario { get; set; }
        public string RangoEtarioCAIPI { get; set; }
        public string RangoEtarioCAIPI2 { get; set; }
        public string RangoEtarioCAIPISala { get; set; }
        public string RangoEtarioCAFI { get; set; }
        public string RangoEtarioCAFI2 { get; set; }
        public string RangoEtarioCAFISala { get; set; }
        public string RangoEtarioAbastecimiento { get; set; }
        public string Sexo { get; set; }
        public int NoOrden { get; set; }
        public decimal ViviendaNucleoNo { get; set; }
        public bool TieneActaNacimiento { get; set; }
        public string ActaNacimiento { get; set; }
        public string Seccion { get; set; }
        public string Barrio { get; set; }
        public string SubBarrio { get; set; }
        public string ViviendaCalle { get; set; }
        public string ViviendaCalleNo { get; set; }
        public string ViviendaEdificio { get; set; }
        public string ViviendaApartamento { get; set; }
        public string ViviendaPuntoReferencia { get; set; }
        public string ViviendaTelefono1 { get; set; }
        public string ViviendaTelefono2 { get; set; }
        public string Cedula { get; set; }
        public string NoActaNacimientoRazon { get; set; }
        public string Madre { get; set; }
        public string MadreNombre1 { get; set; }
        public string MadreNombre2 { get; set; }
        public string MadreApellido1 { get; set; }
        public string MadreApellido2 { get; set; }
        public string MadreCedula { get; set; }
        public string Padre { get; set; }
        public string PadreNombre1 { get; set; }
        public string PadreNombre2 { get; set; }
        public string PadreApellido1 { get; set; }
        public string PadreApellido2 { get; set; }
        public string PadreCedula { get; set; }
        public string Tutor { get; set; }
        public string TutorNombre1 { get; set; }
        public string TutorNombre2 { get; set; }
        public string TutorApellido1 { get; set; }
        public string TutorApellido2 { get; set; }
        public string TutorCedula { get; set; }
        public bool IngresoSinFicha { get; set; }
        public decimal IdIndice { get; set; }
        public decimal IdSeleccion { get; set; }
        public DateTime FechaInscripcion { get; set; }
        public decimal IdUsuario { get; set; }
        public string Sala { get; set; }
        public string SalaRango { get; set; }
        public string SalaSeccion { get; set; }
        public string AsistenciaDia { get; set; }
        public string AsistenciaHorario { get; set; }
        public string AsistenciaGrupo { get; set; }
        public string EstatusSeleccion { get; set; }
        public string EstatusInscripcion { get; set; }
        public decimal Puntos { get; set; }
        public string FueraDelServicio { get; set; }
        public string RazonFueraDelServicio { get; set; }
        public bool EnInscripcion { get; set; }
        public string EnTramite { get; set; }
        public string Estatus { get; set; }
        public decimal IdCuadranteSec { get; set; }
        public bool EnCampana { get; set; }
        public string EstatusDelServicio { get; set; }
        public string PathPerfilPhoto { get; set; }
        public bool TieneSeguroSalud { get; set; }
        public string SeguroNoAfiliado { get; set; }
        public int IdARS { get; set; }
        public string ARS { get; set; }
        public int Seguro_IdRegionalSalud { get; set; }
        public int Seguro_IdUNAP { get; set; }
        public string Seguro_Titular { get; set; }
        public string Seguro_TitularNSS { get; set; }
        public byte Seguro_Parentesco { get; set; }
        public int ActaMunicipio { get; set; }
        public int ActaOficialia { get; set; }
        public int ActaLibro { get; set; }
        public int ActaFolio { get; set; }
        public int ActaNo { get; set; }
        public int ActaAnoLibro { get; set; }
        public byte Seguro_TipoAfiliacio { get; set; }
        public bool Seguro_Actualizado { get; set; }
        public bool VerificadoSENASA { get; set; }
        public bool SaludEsDiscapacitado { get; set; }
        public string SaludDiscapacidad { get; set; }
        public string SaludDiagnostico { get; set; }
        public string SaludTratamiento { get; set; }
        public string SaludTipoSangre { get; set; }
        public string SaludRh { get; set; }
        public bool SaludEsAlergico { get; set; }
        public string SaludTipoAlergia { get; set; }
        public DateTime? FechaInicioServicio { get; set; }
        public int IdUltimoMovimiento { get; set; }
        public string RangoEtarioSize { get; set; }
        public short IdEstatusServicio { get; set; }
        public int? IdFormulario { get; set; }
        public byte? IdMiembro { get; set; }
        public string Diagnostico { get; set; }
        public int Documentos { get; set; }
        public decimal IdPais { get; set; }
        public int? IdEstatusNNSenasaUltimo { get; set; }
        public int? IdEvaluacionEDI { get; set; }
        public DateTime? FechaUltEvaluacionEDI { get; set; }
        public DateTime? UltimaFechaActualizacionTelefono { get; set; } 
        public int TotalPresencias { get; set; }
        public int TotalRegistrosAsistencia { get; set; }
        public decimal CantidadVisitasFamilia { get; set; } 
        public decimal Porcentaje { get; set; }  
        public string Georeferencia { get; set; }
        public decimal CantidadNN { get; set; }
        public string IdFamilia { get; set; }
        public decimal IdVisita { get; set; }
        public decimal Cantidad { get; set; }
        public decimal PorcAsistencia { get; set; } 
        public decimal DiasHabiles { get; set; }
        public decimal CantidadPresencias { get; set; }
        public decimal CantidadConsumo { get; set; }
        public decimal CantidadConsumoMenuCumplido { get; set; }
        public decimal CantidadConsumoMenuCumplidoPorc { get; set; }
        public decimal CantidadConsumoMenuConPropiedades { get; set; }
        public decimal CantidadConsumoMenuConPropiedadesPorc { get; set; } 
        public DateTime FechaVisita { get; set; }
        public DateTime fecha { get; set; }
        public DateTime  FechaUltimaEvaluacion { get; set; }
        public string NombreDimension { get; set; }
        public decimal CantidadIndicadores { get; set; }
        public string IndicadoresLogrados { get; set; }
        public decimal ProcLogrado { get; set; }
        public string CualidadesNN { get; set; }
        public string AspectosNecesitaApoyo { get; set; }
        public string NombreAgenteEducativo { get; set; }
        public string DimensionMotor { get; set; }
        public string DimensionLenguaje { get; set; } 
        public string DimensionCognitiva { get; set; }
        public string DimensionSocioefctiva { get; set; }
        public string EticaCiudadana { get; set; }
        public string COMUNICATIVA { get; set; }
        public string PensamientLogicoCC { get; set; }
        public string ResolucionProblemas { get; set; }
        public string CientificaTecnologica { get; set; }
        public string AmbientalSalud { get; set; }
        public string DesarrolloPersonalEspiritual { get; set; }
        public decimal CantidadEva { get; set; }
        


    }

}

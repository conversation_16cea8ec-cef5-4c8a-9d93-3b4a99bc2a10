﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Models.QEC.INS
{
    [Serializable]
    [SQLite.Table("INS_Usuarios")]
    public class INS_Usuarios : BaseModel
    {
        [SQLite.PrimaryKey]
        public decimal IdUsuario { get; set; }
        public string UserName { get; set; }
        public decimal IdEstancia { get; set; }
        public string Nombres { get; set; }
        public string Apellidos { get; set; }
        public string Cedula { get; set; }
        public string Password { get; set; }
        public string RutaAvatar { get; set; }
        public string Posicion { get; set; }
        public string Puesto { get; set; }
        public string Departamento { get; set; }
        public int Perfil { get; set; }
        public decimal IdTerritorio { get; set; }
        public decimal IdRed { get; set; }
        public byte IdAvatar { get; set; }
        public DateTime FechaCreacion { get; set; }
        public decimal IdUsuarioCrea { get; set; }
        public DateTime FehaModificacion { get; set; }
        public decimal IdUsuarioModifica { get; set; }
        public DataTable BotonesAccesos { get; set; }
        public DataTable CentrosAcceso { get; set; }
        public bool Inactivo { get; set; }
        public int IdPkSeccion { get; set; }
        [Dapper.Contrib.Extensions.Write(false)]
        public bool LoginApp { get; set; }
        [Dapper.Contrib.Extensions.Write(false)]
        public string ImgColaborador { get; set; }
        [Dapper.Contrib.Extensions.Write(false)]
        public string ImgColaboradorVirtual
        {
            get
            {
                if (string.IsNullOrEmpty(ImgColaborador))
                {
                    return "~/Images/dpp.png";
                }
                else
                {
                    string virt = ImgColaborador.Replace(@"\\serstr001\d\sistemas", "~/FileServer");
                    if (System.IO.File.Exists(ImgColaborador))
                    {
                        return virt;
                    }
                    else
                    {
                        return "~/Images/dpp.png";
                    }
                }
            }
        }
        [Dapper.Contrib.Extensions.Write(false)]
        public byte[] ImgDisplay { get; set; }
    }
}

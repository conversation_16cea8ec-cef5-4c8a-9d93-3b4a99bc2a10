<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.0.30" />
    <PackageReference Include="NAudio" Version="1.10.0" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    <PackageReference Include="Octane.Xam.VideoPlayer" Version="3.1.0" />
    <PackageReference Include="Plugin.Permissions" Version="6.0.1" />
    <PackageReference Include="WMPLib" Version="1.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xamarin.Essentials" Version="1.5.3.1" />
    <PackageReference Include="Xamarin.Forms" Version="4.5.0.657" />
    <PackageReference Include="Prism.DryIoc.Forms" Version="7.2.0.1367" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\SIGEPI.Common\SIGEPI.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Views\ContenidoPost.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Posts.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>

</Project>
﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:o="clr-namespace:Octane.Xamarin.Forms.VideoPlayer;assembly=Octane.Xamarin.Forms.VideoPlayer"
             prism:ViewModelLocator.AutowireViewModel="True"
             x:Class="INAIPI.Views.ContenidoPost"
             Title="{Binding Title}">
    <ScrollView>
        <StackLayout>
            <StackLayout>
                <WebView VerticalOptions="FillAndExpand" HorizontalOptions="StartAndExpand" Source="{Binding ContenidoHtml}" />
            </StackLayout>
            <!--<StackLayout>
                <o:VideoPlayer AutoPlay="False" FillMode="Resize" Repeat="False" Source="https://www.youtube.com/watch?v=JJ8NuGe_Fr8" />
            </StackLayout>-->
        </StackLayout>
    </ScrollView>
</ContentPage>
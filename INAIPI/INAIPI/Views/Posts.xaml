﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             prism:ViewModelLocator.AutowireViewModel="True"
             x:Class="INAIPI.Views.Posts"
             Title="{Binding Title}">
    <StackLayout>
        <StackLayout.Margin>
            <OnPlatform x:TypeArguments="Thickness">
                <On Platform="iOS" Value="20"></On>
                <On Platform="Android" Value="0"></On>
            </OnPlatform>
        </StackLayout.Margin>
        <ListView ItemsSource="{Binding Posts}" SelectedItem="{Binding PostSeleccionado}"
                  SeparatorVisibility="Default" IsPullToRefreshEnabled="True"
                  HasUnevenRows="True" IsRefreshing="{Binding IsBusy}" HorizontalOptions="FillAndExpand"
                  RefreshCommand="{Binding PostsRefreshCommand}">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <ViewCell>
                        <!--<StackLayout>-->
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="3*"></RowDefinition>
                                </Grid.RowDefinitions>
                                <Label Grid.Row="0" Text="{Binding TituloPost}" FontSize="Title" FontAttributes="Bold"
                                       HorizontalOptions="StartAndExpand" />
                                <Image Grid.Row="1" HeightRequest="250" Source="{Binding PathPortada}" Aspect="AspectFit" />
                                <Editor Grid.Row="2" Text="{Binding Contenido}" FontSize="Body" AutoSize="TextChanges"
                                        HorizontalOptions="StartAndExpand" IsReadOnly="True" />
                            </Grid>
                        <!--</StackLayout>-->
                    </ViewCell>
                </DataTemplate>
            </ListView.ItemTemplate>
            <ListView.Behaviors>
                <prism:EventToCommandBehavior Command="{Binding SeleccionarPostCommand}" EventName="ItemTapped"
                                              EventArgsConverter="{StaticResource EventItemTapped}" />
                <!--<OnPlatform x:TypeArguments="">
                    <On Platform="iOS" Value=""></On>
                </OnPlatform>-->
            </ListView.Behaviors>
        </ListView>
    </StackLayout>
</ContentPage>
﻿using Prism;
using Prism.Ioc;
using INAIPI.ViewModels;
using INAIPI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using Plugin.Permissions;
using Plugin.Permissions.Abstractions;
using System;

[assembly: XamlCompilation(XamlCompilationOptions.Compile)]
namespace INAIPI
{
    public partial class App
    {
        /* 
         * The Xamarin Forms XAML Previewer in Visual Studio uses System.Activator.CreateInstance.
         * This imposes a limitation in which the App class must have a default constructor. 
         * App(IPlatformInitializer initializer = null) cannot be handled by the Activator.
         */
        public App() : this(null) { }

        public App(IPlatformInitializer initializer) : base(initializer) { }

        protected override async void OnInitialized()
        {
            InitializeComponent();
            RequestPermission();

            await NavigationService.NavigateAsync("NavigationPage/MainPage");
        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            containerRegistry.RegisterForNavigation<NavigationPage>();
            containerRegistry.RegisterForNavigation<MainPage, MainPageViewModel>();
            containerRegistry.RegisterForNavigation<Posts, PostsViewModel>();
            containerRegistry.RegisterForNavigation<ContenidoPost, ContenidoPostViewModel>();
        }

        private async void RequestPermission()
        {
            try
            {
                var statusStorage = await CrossPermissions.Current.CheckPermissionStatusAsync<StoragePermission>();
                var statusLocation = await CrossPermissions.Current.CheckPermissionStatusAsync<LocationPermission>();
                if ((statusStorage != PermissionStatus.Granted) || (statusLocation != PermissionStatus.Granted))
                {
                    if (await CrossPermissions.Current.ShouldShowRequestPermissionRationaleAsync(Permission.Storage) || await CrossPermissions.Current.ShouldShowRequestPermissionRationaleAsync(Permission.Location))
                    {
                        await App.Current.MainPage.DisplayAlert("Permisos", "Es necesario el almacenaje y la ubicación en el dispositivo para el correcto funcionamiento de la aplicación.", "OK");
                    }

                    var result1 = await CrossPermissions.Current.RequestPermissionAsync<StoragePermission>();
                    var result2 = await CrossPermissions.Current.RequestPermissionAsync<LocationPermission>();
                }
            }
            catch (Exception)
            {
                return;
            }
        }
    }
}

﻿<?xml version="1.0" encoding="utf-8" ?>
<prism:PrismApplication xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="clr-namespace:Prism.DryIoc;assembly=Prism.DryIoc.Forms"
             xmlns:helpers="clr-namespace:INAIPI.Helpers"
             x:Class="INAIPI.App">
    <Application.Resources>
        <ResourceDictionary>

            <!-- Ejemplo de implementacion: key=API_KEY -->
            <x:String x:Key="YoutubeAPIKey">AIzaSyDeqgJBwKa2jBVPzZodMErQADfHT2wiwJw</x:String>
            <x:String x:Key="INAIPIRD">UCpcDzQH0nLx_nrHI4jZNN9Q</x:String>
            
            <!-- ============================== Converters ============================== -->
            <helpers:DenyBoolConverter x:Key="DenyBool" />
            <helpers:ItemTappedEventArgsConverter x:Key="EventItemTapped" />
            <!-- ============================== Converters ============================== -->
        </ResourceDictionary>
    </Application.Resources>
</prism:PrismApplication>
﻿using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using SIGEPI.Common.Models.QEC.INAIPIAPP;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace INAIPI.ViewModels
{
    public class PostsViewModel : ViewModelBase
    {
        public PostsViewModel(INavigationService navigationService)
            : base(navigationService)
        {
            Title = "Artículos";
        }

        private ObservableCollection<APP_Post> _posts;
        public ObservableCollection<APP_Post> Posts
        {
            get => _posts;
            set => SetProperty(ref _posts, value);
        }

        private APP_Post _postSeleccionado;
        public APP_Post PostSeleccionado
        {
            get => _postSeleccionado;
            set => SetProperty(ref _postSeleccionado, value);
        }

        private DelegateCommand _postsRefreshCommand;
        public DelegateCommand PostsRefreshCommand => _postsRefreshCommand ?? (_postsRefreshCommand = new DelegateCommand(PostsRefresh));

        private DelegateCommand _seleccionarPostCommand;
        public DelegateCommand SeleccionarPostCommand => _seleccionarPostCommand ?? (_seleccionarPostCommand = new DelegateCommand(VerContenido));

        private async void PostsRefresh()
        {
            IsBusy = true;

            await Task.Run(() =>
            {
                var lista = new List<APP_Post>();

                for (int i = 1; i <= 5; i++)
                {
                    var obj = new APP_Post
                    {
                        Contenido = $"({i}) Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
                        Estatus = new APP_Post.APP_PostEstatus { IdEstatus = (byte)i, NombreEstatus = $"Publicado {i}" },
                        IdEstatus = (byte)i,
                        FechaModificacion = DateTime.Now,
                        FechaSistema = DateTime.Now.AddHours(-1),
                        Html = true,
                        IdPostTipo = 1,
                        ContenidoHtml = $"<p style='box-sizing: border-box; margin-top: 0px; font-family: &quot; Open Sans&quot;, apple-system, blinkmacsystemfont, &quot; Segoe UI&quot;, &quot; Helvetica Neue&quot;, arial, sans-serif; font-size: 17px; text-align: left; '><strong style='box-sizing: border-box; '>Lavarse las manos es una de las mejores formas de protegerse y de proteger a su familia para que no se enfermen.</strong>&nbsp;<strong style='box-sizing: border-box; '>Sepa cuándo y cómo se debe lavar las manos para mantenerse sano.</strong></p><h4 style='box-sizing: border-box; margin-top: 2rem; margin-bottom: 0.75rem; font-weight: 500; line-height: 1.3; font-size: 1.375rem; font-family: Merriweather, serif; text-align: left; '>Lávese las manos a menudo para mantenerse sano</h4><p style='box-sizing: border-box; margin-top: 0px; font-family: &quot; Open Sans&quot;, apple-system, blinkmacsystemfont, &quot; Segoe UI&quot;, &quot; Helvetica Neue&quot;, arial, sans-serif; font-size: 17px; text-align: left; '>Usted puede ayudar a que tanto usted como sus seres queridos se mantengan sanos al lavarse las manos a menudo, especialmente durante los siguientes momentos claves en que tienen más probabilidades de contraer y propagar microbios:</p><hr />",
                        PathPortada = "https://cnnespanol2.files.wordpress.com/2019/11/nincc83o-genio-laurent-simons-belgica.jpg?quality=100&strip=info&w=460&h=260&crop=1",
                        NombrePortada = $"({i}) La niña",
                        TituloPost = "La niña vió a su hermano nacer.",
                        IdPost = i,
                        Multimedias = new List<APP_Post.APP_PostMultimedia> { new APP_Post.APP_PostMultimedia { IdMultimedia = i, Url = "https://www.youtube.com/watch?v=JJ8NuGe_Fr8" } }
                    };

                    lista.Add(obj);
                }

                Posts = new ObservableCollection<APP_Post>(lista);
            });

            IsBusy = false;
        }

        private async void VerContenido()
        {
            IsBusy = true;
            var parametros = new NavigationParameters();
            parametros.Add("postSeleccionado", PostSeleccionado);

            await NavigationService.NavigateAsync($"{nameof(Views.ContenidoPost)}", parametros);

            IsBusy = false;
        }

        public override void OnNavigatedTo(INavigationParameters parameters)
        {
            base.OnNavigatedTo(parameters);
        }
    }
}

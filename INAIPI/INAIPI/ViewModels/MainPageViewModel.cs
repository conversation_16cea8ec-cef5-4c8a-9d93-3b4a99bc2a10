﻿using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace INAIPI.ViewModels
{
    public class MainPageViewModel : ViewModelBase
    {
        public MainPageViewModel(INavigationService navigationService)
            : base(navigationService)
        {
            Title = "Main Page";
        }

        private DelegateCommand _irPostsCommand;
        public DelegateCommand IrPostsCommand => _irPostsCommand ?? (_irPostsCommand = new DelegateCommand(IrPosts));

        private async void IrPosts()
        {
            await NavigationService.NavigateAsync(nameof(Views.Posts));
        }
    }
}

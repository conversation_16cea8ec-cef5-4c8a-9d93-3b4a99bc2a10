﻿using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using SIGEPI.Common.Models.QEC.INAIPIAPP;
using System;
using System.Collections.Generic;
using System.Linq;
using Xamarin.Forms;

namespace INAIPI.ViewModels
{
    public class ContenidoPostViewModel : ViewModelBase
    {
        public ContenidoPostViewModel(INavigationService navigationService)
            : base(navigationService)
        {
            Title = "Contenido";
        }

        private APP_Post _postSeleccionado;
        public APP_Post PostSeleccionado
        {
            get => _postSeleccionado;
            set => SetProperty(ref _postSeleccionado, value);
        }

        private HtmlWebViewSource _contenidoHtml;
        public HtmlWebViewSource ContenidoHtml
        {
            get => _contenidoHtml;
            set => SetProperty(ref _contenidoHtml, value);
        }

        public override void OnNavigatedTo(INavigationParameters parameters)
        {
            if (parameters.ContainsKey("postSeleccionado"))
            {
                PostSeleccionado = parameters.GetValue<APP_Post>("postSeleccionado");
                ContenidoHtml = new HtmlWebViewSource();
                ContenidoHtml.Html = PostSeleccionado?.ContenidoHtml ?? "";
            }

            base.OnNavigatedTo(parameters);
        }
    }
}

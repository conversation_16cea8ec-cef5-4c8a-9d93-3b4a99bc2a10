﻿using System.Collections.Generic;
using Newtonsoft.Json;
using Xamarin.Essentials;

namespace INAIPI.Services
{
    public static class LocalData
    {
        public static class UserData
        {
            public static string Token
            {
                get => Preferences.Get("LocalData_UserData_Token", string.Empty);
                set => Preferences.Set("LocalData_UserData_Token", value);
            }

        }   
    }
}

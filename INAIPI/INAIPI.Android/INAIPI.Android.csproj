﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{D3026962-19E2-475B-8C25-DFA834702619}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>INAIPI.Droid</RootNamespace>
    <AssemblyName>INAIPI.Android</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkVersion>v9.0</TargetFrameworkVersion>
    <AndroidApplication>True</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.designer.cs</AndroidResgenFile>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <AndroidResgenClass>Resource</AndroidResgenClass>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <MonoAndroidResourcePrefix>Resources</MonoAndroidResourcePrefix>
    <MonoAndroidAssetsPrefix>Assets</MonoAndroidAssetsPrefix>
    <AndroidStoreUncompressedFileExtensions />
    <MandroidI18n />
    <JavaMaximumHeapSize />
    <JavaOptions />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>None</AndroidLinkMode>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidManagedSymbols>true</AndroidManagedSymbols>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Mono.Android" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Octane.Xam.VideoPlayer">
      <Version>3.1.0</Version>
    </PackageReference>
    <PackageReference Include="Plugin.CurrentActivity">
      <Version>*******</Version>
    </PackageReference>
    <PackageReference Include="Plugin.Permissions">
      <Version>6.0.1</Version>
    </PackageReference>
    <PackageReference Include="System.IO.Pipelines">
      <Version>5.0.1</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Forms" Version="4.5.0.657" />
    <PackageReference Include="Xamarin.Android.Support.CustomTabs" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.Design" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.v4" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.v7.AppCompat" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.v7.CardView" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.v7.MediaRouter" Version="********" />
    <PackageReference Include="Prism.DryIoc.Forms" Version="7.2.0.1367" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="MainActivity.cs" />
    <Compile Include="Resources\Resource.Designer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-hdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\AndroidManifest.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\Tabbar.axml" />
    <AndroidResource Include="Resources\layout\Toolbar.axml" />
    <AndroidResource Include="Resources\values\styles.xml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\INAIPI\INAIPI.csproj">
      <Project>{A682884C-CB44-4A89-B752-27BB634E0ABE}</Project>
      <Name>INAIPI</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
</Project>
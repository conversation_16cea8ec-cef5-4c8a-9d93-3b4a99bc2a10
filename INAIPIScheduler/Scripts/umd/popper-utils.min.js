/*
 Copyright (C) <PERSON> 2018
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */(function(a,b){'object'==typeof exports&&'undefined'!=typeof module?b(exports):'function'==typeof define&&define.amd?define(['exports'],b):b(a.PopperUtils={})})(this,function(a){'use strict';function b(a,b){if(1!==a.nodeType)return[];var c=getComputedStyle(a,null);return b?c[b]:c}function c(a){return'HTML'===a.nodeName?a:a.parentNode||a.host}function d(a){if(!a)return document.body;switch(a.nodeName){case'HTML':case'BODY':return a.ownerDocument.body;case'#document':return a.body;}var e=b(a),f=e.overflow,g=e.overflowX,h=e.overflowY;return /(auto|scroll|overlay)/.test(f+h+g)?a:d(c(a))}function e(a){if(!a)return document.documentElement;for(var c=S(10)?document.body:null,d=a.offsetParent;d===c&&a.nextElementSibling;)d=(a=a.nextElementSibling).offsetParent;var f=d&&d.nodeName;return f&&'BODY'!==f&&'HTML'!==f?-1!==['TD','TABLE'].indexOf(d.nodeName)&&'static'===b(d,'position')?e(d):d:a?a.ownerDocument.documentElement:document.documentElement}function f(a){var b=a.nodeName;return'BODY'!==b&&('HTML'===b||e(a.firstElementChild)===a)}function g(a){return null===a.parentNode?a:g(a.parentNode)}function h(a,b){if(!a||!a.nodeType||!b||!b.nodeType)return document.documentElement;var c=a.compareDocumentPosition(b)&Node.DOCUMENT_POSITION_FOLLOWING,d=c?a:b,i=c?b:a,j=document.createRange();j.setStart(d,0),j.setEnd(i,0);var k=j.commonAncestorContainer;if(a!==k&&b!==k||d.contains(i))return f(k)?k:e(k);var l=g(a);return l.host?h(l.host,b):h(a,g(b).host)}function j(a){var b=1<arguments.length&&arguments[1]!==void 0?arguments[1]:'top',c='top'===b?'scrollTop':'scrollLeft',d=a.nodeName;if('BODY'===d||'HTML'===d){var e=a.ownerDocument.documentElement,f=a.ownerDocument.scrollingElement||e;return f[c]}return a[c]}function k(a,b){var c=2<arguments.length&&void 0!==arguments[2]&&arguments[2],d=j(b,'top'),e=j(b,'left'),f=c?-1:1;return a.top+=d*f,a.bottom+=d*f,a.left+=e*f,a.right+=e*f,a}function l(a,b){var c='x'===b?'Left':'Top',d='Left'==c?'Right':'Bottom';return parseFloat(a['border'+c+'Width'],10)+parseFloat(a['border'+d+'Width'],10)}function m(a,b,c,d){return Q(b['offset'+a],b['scroll'+a],c['client'+a],c['offset'+a],c['scroll'+a],S(10)?c['offset'+a]+d['margin'+('Height'===a?'Top':'Left')]+d['margin'+('Height'===a?'Bottom':'Right')]:0)}function n(){var a=document.body,b=document.documentElement,c=S(10)&&getComputedStyle(b);return{height:m('Height',a,b,c),width:m('Width',a,b,c)}}function o(a){return T({},a,{right:a.left+a.width,bottom:a.top+a.height})}function p(a){var c={};try{if(S(10)){c=a.getBoundingClientRect();var d=j(a,'top'),e=j(a,'left');c.top+=d,c.left+=e,c.bottom+=d,c.right+=e}else c=a.getBoundingClientRect()}catch(a){}var f={left:c.left,top:c.top,width:c.right-c.left,height:c.bottom-c.top},g='HTML'===a.nodeName?n():{},h=g.width||a.clientWidth||f.right-f.left,i=g.height||a.clientHeight||f.bottom-f.top,k=a.offsetWidth-h,m=a.offsetHeight-i;if(k||m){var p=b(a);k-=l(p,'x'),m-=l(p,'y'),f.width-=k,f.height-=m}return o(f)}function q(a,c){var e=2<arguments.length&&void 0!==arguments[2]&&arguments[2],f=S(10),g='HTML'===c.nodeName,h=p(a),i=p(c),j=d(a),l=b(c),m=parseFloat(l.borderTopWidth,10),n=parseFloat(l.borderLeftWidth,10);e&&'HTML'===c.nodeName&&(i.top=Q(i.top,0),i.left=Q(i.left,0));var q=o({top:h.top-i.top-m,left:h.left-i.left-n,width:h.width,height:h.height});if(q.marginTop=0,q.marginLeft=0,!f&&g){var r=parseFloat(l.marginTop,10),s=parseFloat(l.marginLeft,10);q.top-=m-r,q.bottom-=m-r,q.left-=n-s,q.right-=n-s,q.marginTop=r,q.marginLeft=s}return(f&&!e?c.contains(j):c===j&&'BODY'!==j.nodeName)&&(q=k(q,c)),q}function r(a){var b=1<arguments.length&&arguments[1]!==void 0&&arguments[1],c=a.ownerDocument.documentElement,d=q(a,c),e=Q(c.clientWidth,window.innerWidth||0),f=Q(c.clientHeight,window.innerHeight||0),g=b?0:j(c),h=b?0:j(c,'left'),i={top:g-d.top+d.marginTop,left:h-d.left+d.marginLeft,width:e,height:f};return o(i)}function s(a){var d=a.nodeName;return'BODY'===d||'HTML'===d?!1:!('fixed'!==b(a,'position'))||s(c(a))}function t(a){if(!a||!a.parentElement||S())return document.documentElement;for(var c=a.parentElement;c&&'none'===b(c,'transform');)c=c.parentElement;return c||document.documentElement}function u(a,b,e,f){var g=4<arguments.length&&void 0!==arguments[4]&&arguments[4],i={top:0,left:0},j=g?t(a):h(a,b);if('viewport'===f)i=r(j,g);else{var k;'scrollParent'===f?(k=d(c(b)),'BODY'===k.nodeName&&(k=a.ownerDocument.documentElement)):'window'===f?k=a.ownerDocument.documentElement:k=f;var l=q(k,j,g);if('HTML'===k.nodeName&&!s(j)){var m=n(),o=m.height,p=m.width;i.top+=l.top-l.marginTop,i.bottom=o+l.top,i.left+=l.left-l.marginLeft,i.right=p+l.left}else i=l}return i.left+=e,i.top+=e,i.right-=e,i.bottom-=e,i}function v(a){var b=a.width,c=a.height;return b*c}function w(a,b,c,d,e){var f=5<arguments.length&&arguments[5]!==void 0?arguments[5]:0;if(-1===a.indexOf('auto'))return a;var g=u(c,d,f,e),h={top:{width:g.width,height:b.top-g.top},right:{width:g.right-b.right,height:g.height},bottom:{width:g.width,height:g.bottom-b.bottom},left:{width:b.left-g.left,height:g.height}},i=Object.keys(h).map(function(a){return T({key:a},h[a],{area:v(h[a])})}).sort(function(c,a){return a.area-c.area}),j=i.filter(function(a){var b=a.width,d=a.height;return b>=c.clientWidth&&d>=c.clientHeight}),k=0<j.length?j[0].key:i[0].key,l=a.split('-')[1];return k+(l?'-'+l:'')}function x(a,b){return Array.prototype.find?a.find(b):a.filter(b)[0]}function y(a,b,c){if(Array.prototype.findIndex)return a.findIndex(function(a){return a[b]===c});var d=x(a,function(a){return a[b]===c});return a.indexOf(d)}function z(a){var b;if('HTML'===a.nodeName){var c=n(),d=c.width,e=c.height;b={width:d,height:e,left:0,top:0}}else b={width:a.offsetWidth,height:a.offsetHeight,left:a.offsetLeft,top:a.offsetTop};return o(b)}function A(a){var b=getComputedStyle(a),c=parseFloat(b.marginTop)+parseFloat(b.marginBottom),d=parseFloat(b.marginLeft)+parseFloat(b.marginRight),e={width:a.offsetWidth+d,height:a.offsetHeight+c};return e}function B(a){var b={left:'right',right:'left',bottom:'top',top:'bottom'};return a.replace(/left|right|bottom|top/g,function(a){return b[a]})}function C(a,b,c){c=c.split('-')[0];var d=A(a),e={width:d.width,height:d.height},f=-1!==['right','left'].indexOf(c),g=f?'top':'left',h=f?'left':'top',i=f?'height':'width',j=f?'width':'height';return e[g]=b[g]+b[i]/2-d[i]/2,e[h]=c===h?b[h]-d[j]:b[B(h)],e}function D(a,b,c){var d=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null,e=d?t(b):h(b,c);return q(c,e,d)}function E(a){for(var b=[!1,'ms','Webkit','Moz','O'],c=a.charAt(0).toUpperCase()+a.slice(1),d=0;d<b.length;d++){var e=b[d],f=e?''+e+c:a;if('undefined'!=typeof document.body.style[f])return f}return null}function F(a){return a&&'[object Function]'==={}.toString.call(a)}function G(a,b){return a.some(function(a){var c=a.name,d=a.enabled;return d&&c===b})}function H(a,b,c){var d=x(a,function(a){var c=a.name;return c===b}),e=!!d&&a.some(function(a){return a.name===c&&a.enabled&&a.order<d.order});if(!e){var f='`'+b+'`';console.warn('`'+c+'`'+' modifier is required by '+f+' modifier in order to work, be sure to include it before '+f+'!')}return e}function I(a){return''!==a&&!isNaN(parseFloat(a))&&isFinite(a)}function J(a){var b=a.ownerDocument;return b?b.defaultView:window}function K(a,b){return J(a).removeEventListener('resize',b.updateBound),b.scrollParents.forEach(function(a){a.removeEventListener('scroll',b.updateBound)}),b.updateBound=null,b.scrollParents=[],b.scrollElement=null,b.eventsEnabled=!1,b}function L(a,b,c){var d=void 0===c?a:a.slice(0,y(a,'name',c));return d.forEach(function(a){a['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');var c=a['function']||a.fn;a.enabled&&F(c)&&(b.offsets.popper=o(b.offsets.popper),b.offsets.reference=o(b.offsets.reference),b=c(b,a))}),b}function M(a,b){Object.keys(b).forEach(function(c){var d=b[c];!1===d?a.removeAttribute(c):a.setAttribute(c,b[c])})}function N(a,b){Object.keys(b).forEach(function(c){var d='';-1!==['width','height','top','right','bottom','left'].indexOf(c)&&I(b[c])&&(d='px'),a.style[c]=b[c]+d})}function O(a,b,c,e){var f='BODY'===a.nodeName,g=f?a.ownerDocument.defaultView:a;g.addEventListener(b,c,{passive:!0}),f||O(d(g.parentNode),b,c,e),e.push(g)}function P(a,b,c,e){c.updateBound=e,J(a).addEventListener('resize',c.updateBound,{passive:!0});var f=d(a);return O(f,'scroll',c.updateBound,c.scrollParents),c.scrollElement=f,c.eventsEnabled=!0,c}for(var Q=Math.max,R={},S=function(){var a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:'all';return(a=a.toString(),R.hasOwnProperty(a))?R[a]:('11'===a?R[a]=-1!==navigator.userAgent.indexOf('Trident'):'10'===a?R[a]=-1!==navigator.appVersion.indexOf('MSIE 10'):'all'===a?R[a]=-1!==navigator.userAgent.indexOf('Trident')||-1!==navigator.userAgent.indexOf('MSIE'):void 0,R.all=R.all||Object.keys(R).some(function(a){return R[a]}),R[a])},T=Object.assign||function(a){for(var b,c=1;c<arguments.length;c++)for(var d in b=arguments[c],b)Object.prototype.hasOwnProperty.call(b,d)&&(a[d]=b[d]);return a},U='undefined'!=typeof window&&'undefined'!=typeof document,V=['Edge','Trident','Firefox'],W=0,X=0;X<V.length;X+=1)if(U&&0<=navigator.userAgent.indexOf(V[X])){W=1;break}var i=U&&window.Promise,Y=i?function(a){var b=!1;return function(){b||(b=!0,window.Promise.resolve().then(function(){b=!1,a()}))}}:function(a){var b=!1;return function(){b||(b=!0,setTimeout(function(){b=!1,a()},W))}};a.computeAutoPlacement=w,a.debounce=Y,a.findIndex=y,a.getBordersSize=l,a.getBoundaries=u,a.getBoundingClientRect=p,a.getClientRect=o,a.getOffsetParent=e,a.getOffsetRect=z,a.getOffsetRectRelativeToArbitraryNode=q,a.getOuterSizes=A,a.getParentNode=c,a.getPopperOffsets=C,a.getReferenceOffsets=D,a.getScroll=j,a.getScrollParent=d,a.getStyleComputedProperty=b,a.getSupportedPropertyName=E,a.getWindowSizes=n,a.isFixed=s,a.isFunction=F,a.isModifierEnabled=G,a.isModifierRequired=H,a.isNumeric=I,a.removeEventListeners=K,a.runModifiers=L,a.setAttributes=M,a.setStyles=N,a.setupEventListeners=P,a['default']={computeAutoPlacement:w,debounce:Y,findIndex:y,getBordersSize:l,getBoundaries:u,getBoundingClientRect:p,getClientRect:o,getOffsetParent:e,getOffsetRect:z,getOffsetRectRelativeToArbitraryNode:q,getOuterSizes:A,getParentNode:c,getPopperOffsets:C,getReferenceOffsets:D,getScroll:j,getScrollParent:d,getStyleComputedProperty:b,getSupportedPropertyName:E,getWindowSizes:n,isFixed:s,isFunction:F,isModifierEnabled:G,isModifierRequired:H,isNumeric:I,removeEventListeners:K,runModifiers:L,setAttributes:M,setStyles:N,setupEventListeners:P},Object.defineProperty(a,'__esModule',{value:!0})});
//# sourceMappingURL=popper-utils.min.js.map

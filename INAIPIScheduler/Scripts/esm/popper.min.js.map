{"version": 3, "file": "popper.min.js", "sources": ["../../src/utils/debounce.js", "../../src/utils/isFunction.js", "../../src/utils/getStyleComputedProperty.js", "../../src/utils/getParentNode.js", "../../src/utils/getScrollParent.js", "../../src/utils/isIE.js", "../../src/utils/getOffsetParent.js", "../../src/utils/isOffsetContainer.js", "../../src/utils/getRoot.js", "../../src/utils/findCommonOffsetParent.js", "../../src/utils/getScroll.js", "../../src/utils/includeScroll.js", "../../src/utils/getBordersSize.js", "../../src/utils/getWindowSizes.js", "../../src/utils/getClientRect.js", "../../src/utils/getBoundingClientRect.js", "../../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../../src/utils/isFixed.js", "../../src/utils/getFixedPositionOffsetParent.js", "../../src/utils/getBoundaries.js", "../../src/utils/computeAutoPlacement.js", "../../src/utils/getReferenceOffsets.js", "../../src/utils/getOuterSizes.js", "../../src/utils/getOppositePlacement.js", "../../src/utils/getPopperOffsets.js", "../../src/utils/find.js", "../../src/utils/findIndex.js", "../../src/utils/runModifiers.js", "../../src/methods/update.js", "../../src/utils/isModifierEnabled.js", "../../src/utils/getSupportedPropertyName.js", "../../src/methods/destroy.js", "../../src/utils/getWindow.js", "../../src/utils/setupEventListeners.js", "../../src/methods/enableEventListeners.js", "../../src/utils/removeEventListeners.js", "../../src/methods/disableEventListeners.js", "../../src/utils/isNumeric.js", "../../src/utils/setStyles.js", "../../src/utils/setAttributes.js", "../../src/modifiers/applyStyle.js", "../../src/modifiers/computeStyle.js", "../../src/utils/isModifierRequired.js", "../../src/modifiers/arrow.js", "../../src/utils/getOppositeVariation.js", "../../src/methods/placements.js", "../../src/utils/clockwise.js", "../../src/modifiers/flip.js", "../../src/modifiers/keepTogether.js", "../../src/modifiers/offset.js", "../../src/modifiers/preventOverflow.js", "../../src/modifiers/shift.js", "../../src/modifiers/hide.js", "../../src/modifiers/inner.js", "../../src/modifiers/index.js", "../../src/methods/defaults.js", "../../src/index.js"], "sourcesContent": ["const isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\nconst longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nlet timeoutDuration = 0;\nfor (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const css = getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Tells if you are running Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @argument {number} version to check\n * @returns {Boolean} isIE\n */\nconst cache = {};\n\nexport default function (version = 'all') {\n  version = version.toString();\n  if(cache.hasOwnProperty(version)){\n    return cache[version];\n  }\n  switch (version) {\n    case '11':\n      cache[version] = navigator.userAgent.indexOf('Trident') !== -1;\n      break;\n    case '10':\n      cache[version] = navigator.appVersion.indexOf('MSIE 10') !== -1;\n      break;\n    case 'all':\n      cache[version] = navigator.userAgent.indexOf('Trident') !== -1 || navigator.userAgent.indexOf('MSIE') !== -1;\n      break;\n  }\n\n  //Set IE\n  cache.all = cache.all || Object.keys(cache).some(key => cache[key]);\n  return cache[version];\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`], 10) +\n    parseFloat(styles[`border${sideB}Width`], 10)\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? html[`offset${axis}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]\n      : 0\n  );\n}\n\nexport default function getWindowSizes() {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  const width =\n    sizes.width || element.clientWidth || result.right - result.left;\n  const height =\n    sizes.height || element.clientHeight || result.bottom - result.top;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth, 10);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth, 10);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && parent.nodeName === 'HTML') {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop, 10);\n    const marginLeft = parseFloat(styles.marginLeft, 10);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes();\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const styles = getComputedStyle(element);\n  const x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  const y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "import computeAutoPlacement from '../utils/computeAutoPlacement';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nexport default function update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  let data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {},\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(\n    this.state,\n    this.popper,\n    this.reference,\n    this.options.positionFixed\n  );\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(\n    this.options.placement,\n    data.offsets.reference,\n    this.popper,\n    this.reference,\n    this.options.modifiers.flip.boundariesElement,\n    this.options.modifiers.flip.padding\n  );\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(\n    this.popper,\n    data.offsets.reference,\n    data.placement\n  );\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "import isModifierEnabled from '../utils/isModifierEnabled';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * Destroy the popper\n * @method\n * @memberof Popper\n */\nexport default function destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicity asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import setupEventListeners from '../utils/setupEventListeners';\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nexport default function enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(\n      this.reference,\n      this.options,\n      this.state,\n      this.scheduleUpdate\n    );\n  }\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import removeEventListeners from '../utils/removeEventListeners';\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger onUpdate callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nexport default function disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  const placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference,\n    options.modifiers.flip.boundariesElement,\n    options.modifiers.flip.padding\n  );\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport find from '../utils/find';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundingClientRect from '../utils/getBoundingClientRect';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeStyle(data, options) {\n  const { x, y } = options;\n  const { popper } = data.offsets;\n\n  // Remove this legacy support in Popper.js v2\n  const legacyGpuAccelerationOption = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'applyStyle'\n  ).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn(\n      'WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!'\n    );\n  }\n  const gpuAcceleration =\n    legacyGpuAccelerationOption !== undefined\n      ? legacyGpuAccelerationOption\n      : options.gpuAcceleration;\n\n  const offsetParent = getOffsetParent(data.instance.popper);\n  const offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  const styles = {\n    position: popper.position,\n  };\n\n  // floor sides to avoid blurry text\n  const offsets = {\n    left: Math.floor(popper.left),\n    top: Math.floor(popper.top),\n    bottom: Math.floor(popper.bottom),\n    right: Math.floor(popper.right),\n  };\n\n  const sideA = x === 'bottom' ? 'top' : 'bottom';\n  const sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  let left, top;\n  if (sideA === 'bottom') {\n    top = -offsetParentRect.height + offsets.bottom;\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    left = -offsetParentRect.width + offsets.right;\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = `translate3d(${left}px, ${top}px, 0)`;\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    const invertTop = sideA === 'bottom' ? -1 : 1;\n    const invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = `${sideA}, ${sideB}`;\n  }\n\n  // Attributes\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = { ...attributes, ...data.attributes };\n  data.styles = { ...styles, ...data.styles };\n  data.arrowStyles = { ...data.offsets.arrow, ...data.arrowStyles };\n\n  return data;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\nimport getStyleComputedProperty from '../utils/getStyleComputedProperty';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjuction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  const css = getStyleComputedProperty(data.instance.popper);\n  const popperMarginSide = parseFloat(css[`margin${sideCapitalized}`], 10);\n  const popperBorderSide = parseFloat(css[`border${sideCapitalized}Width`], 10);\n  let sideValue =\n    center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '', // make sure to unset any eventual altSide value from the DOM node\n  };\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-right` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nexport default [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n", "import placements from '../methods/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement,\n    data.positionFixed\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = data.offsets.popper;\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    const flippedVariation =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = {\n        ...data.offsets.popper,\n        ...getPopperOffsets(\n          data.instance.popper,\n          data.offsets.reference,\n          data.placement\n        ),\n      };\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const { popper, reference } = data.offsets;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\nimport find from '../utils/find';\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nexport function toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  const split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  const value = +split[1];\n  const unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    let element;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    const rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    let size;\n    if (unit === 'vh') {\n      size = Math.max(\n        document.documentElement.clientHeight,\n        window.innerHeight || 0\n      );\n    } else {\n      size = Math.max(\n        document.documentElement.clientWidth,\n        window.innerWidth || 0\n      );\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nexport function parseOffset(\n  offset,\n  popperOffsets,\n  referenceOffsets,\n  basePlacement\n) {\n  const offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  const useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  const fragments = offset.split(/(\\+|\\-)/).map(frag => frag.trim());\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  const divider = fragments.indexOf(\n    find(fragments, frag => frag.search(/,|\\s/) !== -1)\n  );\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn(\n      'Offsets separated by white space(s) are deprecated, use a comma (,) instead.'\n    );\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  const splitRegex = /\\s*,\\s*|\\s+/;\n  let ops = divider !== -1\n    ? [\n        fragments\n          .slice(0, divider)\n          .concat([fragments[divider].split(splitRegex)[0]]),\n        [fragments[divider].split(splitRegex)[1]].concat(\n          fragments.slice(divider + 1)\n        ),\n      ]\n    : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map((op, index) => {\n    // Most of the units rely on the orientation of the popper\n    const measurement = (index === 1 ? !useHeight : useHeight)\n      ? 'height'\n      : 'width';\n    let mergeWithPrevious = false;\n    return (\n      op\n        // This aggregates any `+` or `-` sign that aren't considered operators\n        // e.g.: 10 + +5 => [10, +, +5]\n        .reduce((a, b) => {\n          if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n            a[a.length - 1] = b;\n            mergeWithPrevious = true;\n            return a;\n          } else if (mergeWithPrevious) {\n            a[a.length - 1] += b;\n            mergeWithPrevious = false;\n            return a;\n          } else {\n            return a.concat(b);\n          }\n        }, [])\n        // Here we convert the string values into number values (in px)\n        .map(str => toValue(str, measurement, popperOffsets, referenceOffsets))\n    );\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach((op, index) => {\n    op.forEach((frag, index2) => {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, { offset }) {\n  const { placement, offsets: { popper, reference } } = data;\n  const basePlacement = placement.split('-')[0];\n\n  let offsets;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n", "import getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  let boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement,\n    data.positionFixed\n  );\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = data.offsets.popper;\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side = ['left', 'top'].indexOf(placement) !== -1\n      ? 'primary'\n      : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const { reference, popper } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[basePlacement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport computeStyle from './computeStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift,\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unitless, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the height.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > More on this [reading this issue](https://github.com/FezVrasta/popper.js/issues/373)\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * An scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper this makes sure the popper has always a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier, can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near eachothers\n   * without leaving any gap between the two. Expecially useful when the arrow is\n   * enabled and you want to assure it to point to its reference element.\n   * It cares only about the first axis, you can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether,\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjuction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations).\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position,\n     * the popper will never be placed outside of the defined boundaries\n     * (except if keepTogether is enabled)\n     */\n    boundariesElement: 'viewport',\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide,\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right',\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define you own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: undefined,\n  },\n};\n\n/**\n * The `dataObject` is an object containing all the informations used by Popper.js\n * this object get passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper.\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements.\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n", "import modifiers from '../modifiers/index';\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overriden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass as 3rd argument an object with the same\n * structure of this object, example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nexport default {\n  /**\n   * Popper's placement\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated, this callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport isFunction from './utils/isFunction';\n\n// Methods\nimport update from './methods/update';\nimport destroy from './methods/destroy';\nimport enableEventListeners from './methods/enableEventListeners';\nimport disableEventListeners from './methods/disableEventListeners';\nimport Defaults from './methods/defaults';\nimport placements from './methods/placements';\n\nexport default class Popper {\n  /**\n   * Create a new Popper.js instance\n   * @class Popper\n   * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n   * @param {HTMLElement} popper - The HTML element used as popper.\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys({\n      ...Popper.Defaults.modifiers,\n      ...options.modifiers,\n    }).forEach(name => {\n      this.options.modifiers[name] = {\n        // If it's a built-in modifier, use it as base\n        ...(Popper.Defaults.modifiers[name] || {}),\n        // If there are custom options, override and merge with default ones\n        ...(options.modifiers ? options.modifiers[name] : {}),\n      };\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers)\n      .map(name => ({\n        name,\n        ...this.options.modifiers[name],\n      }))\n      // sort the modifiers by order\n      .sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n  update() {\n    return update.call(this);\n  }\n  destroy() {\n    return destroy.call(this);\n  }\n  enableEventListeners() {\n    return enableEventListeners.call(this);\n  }\n  disableEventListeners() {\n    return disableEventListeners.call(this);\n  }\n\n  /**\n   * Schedule an update, it will run on the next UI update available\n   * @method scheduleUpdate\n   * @memberof Popper\n   */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n   * Collection of utilities useful when writing custom modifiers.\n   * Starting from version 1.7, this method is available only if you\n   * include `popper-utils.js` before `popper.js`.\n   *\n   * **DEPRECATION**: This way to access PopperUtils is deprecated\n   * and will be removed in v2! Use the PopperUtils module directly instead.\n   * Due to the high instability of the methods contained in Utils, we can't\n   * guarantee them to follow semver. Use them at your own risk!\n   * @static\n   * @private\n   * @type {Object}\n   * @deprecated since version 1.8\n   * @member Utils\n   * @memberof Popper\n   */\n  static Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\n\n  static placements = placements;\n\n  static Defaults = Defaults;\n}\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "longerTimeoutBrowsers", "timeoutDuration", "i", "length", "navigator", "userAgent", "indexOf", "called", "Promise", "resolve", "then", "scheduled", "supportsMicroTasks", "functionToCheck", "getType", "toString", "call", "element", "nodeType", "css", "getComputedStyle", "property", "nodeName", "parentNode", "host", "body", "ownerDocument", "getStyleComputedProperty", "overflow", "overflowX", "overflowY", "test", "getScrollParent", "getParentNode", "cache", "version", "hasOwnProperty", "appVersion", "all", "Object", "keys", "some", "documentElement", "noOffsetParent", "isIE", "offsetParent", "nextElement<PERSON><PERSON>ling", "getOffsetParent", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "getRoot", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "isOffsetContainer", "element1root", "findCommonOffsetParent", "side", "upperSide", "html", "scrollingElement", "subtract", "scrollTop", "getScroll", "scrollLeft", "modifier", "top", "bottom", "left", "right", "sideA", "axis", "sideB", "parseFloat", "styles", "Math", "max", "computedStyle", "getSize", "offsets", "width", "height", "rect", "getBoundingClientRect", "result", "sizes", "getWindowSizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getBordersSize", "getClientRect", "fixedPosition", "isIE10", "runIsIE", "isHTML", "parent", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "includeScroll", "excludeScroll", "relativeOffset", "getOffsetRectRelativeToArbitraryNode", "innerWidth", "innerHeight", "offset", "isFixed", "parentElement", "el", "boundaries", "getFixedPositionOffsetParent", "boundariesElement", "getViewportOffsetRectRelativeToArtbitraryNode", "boundariesNode", "popper", "padding", "placement", "getBoundaries", "rects", "refRect", "sorted<PERSON>reas", "map", "getArea", "sort", "b", "area", "a", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "key", "variation", "split", "commonOffsetParent", "x", "marginBottom", "y", "marginRight", "hash", "replace", "popperRect", "getOuterSizes", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "referenceOffsets", "getOppositePlacement", "Array", "prototype", "find", "arr", "findIndex", "cur", "match", "obj", "modifiersToRun", "ends", "modifiers", "slice", "for<PERSON>ach", "warn", "fn", "enabled", "isFunction", "data", "reference", "state", "isDestroyed", "getReferenceOffsets", "options", "positionFixed", "computeAutoPlacement", "flip", "originalPlacement", "getPopperOffsets", "position", "runModifiers", "isCreated", "onUpdate", "onCreate", "name", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "isModifierEnabled", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "getSupportedPropertyName", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "defaultView", "isBody", "target", "addEventListener", "passive", "push", "updateBound", "scrollElement", "scrollParents", "eventsEnabled", "setupEventListeners", "scheduleUpdate", "removeEventListener", "removeEventListeners", "n", "isNaN", "isFinite", "unit", "isNumeric", "value", "attributes", "setAttribute", "instance", "arrowElement", "arrowStyles", "floor", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "prefixedProperty", "invertTop", "invertLeft", "arrow", "requesting", "isRequired", "requested", "isModifierRequired", "querySelector", "isVertical", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "min", "round", "validPlacements", "placements", "counter", "index", "concat", "reverse", "BEHAVIORS", "flipped", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "clockwise", "COUNTERCLOCKWISE", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariation", "flipVariations", "getOppositeVariation", "str", "size", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "mergeWithPrevious", "op", "reduce", "toValue", "index2", "basePlacement", "parseOffset", "priority", "check", "escapeWithReference", "shiftvariation", "shiftOffsets", "bound", "hide", "subtractLength", "<PERSON><PERSON>", "requestAnimationFrame", "update", "debounce", "bind", "De<PERSON>ults", "j<PERSON>y", "modifierOptions", "onLoad", "enableEventListeners", "destroy", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;GAGA,IAAK,GAHCA,GAA8B,WAAlB,QAAOC,OAAP,EAAqD,WAApB,QAAOC,SAGrD,CAFCC,8BAED,CADDC,EAAkB,CACjB,CAAIC,EAAI,CAAb,CAAgBA,EAAIF,EAAsBG,MAA1C,CAAkDD,GAAK,CAAvD,IACML,GAAsE,CAAzDO,YAAUC,SAAVD,CAAoBE,OAApBF,CAA4BJ,IAA5BI,EAA4D,GACzD,CADyD,OAM/E,aAAsC,IAChCG,YACG,WAAM,SAAA,QAKJC,QAAQC,UAAUC,KAAK,UAAM,KAAA,IAApC,EALW,CAAb,EAYF,aAAiC,IAC3BC,YACG,WAAM,SAAA,YAGE,UAAM,KAAA,IAAjB,IAHS,CAAb,EAWF,GAAMC,GAAqBf,GAAaC,OAAOU,OAA/C,GAYgBI,KAZhB,CC9BA,aAAoD,OAGhDC,IAC2C,mBAA3CC,MAAQC,QAARD,CAAiBE,IAAjBF,ICJJ,eAAoE,IACzC,CAArBG,KAAQC,qBAINC,GAAMC,mBAA0B,IAA1BA,QACLC,GAAWF,IAAXE,GCNT,aAA+C,OACpB,MAArBJ,KAAQK,QADiC,GAItCL,EAAQM,UAARN,EAAsBA,EAAQO,KCDvC,aAAiD,IAE3C,SACKzB,UAAS0B,YAGVR,EAAQK,cACT,WACA,aACIL,GAAQS,aAART,CAAsBQ,SAC1B,kBACIR,GAAQQ,YAIwBE,KAAnCC,IAAAA,SAAUC,IAAAA,UAAWC,IAAAA,UAfkB,MAgB3C,yBAAwBC,IAAxB,CAA6BH,KAA7B,CAhB2C,GAoBxCI,EAAgBC,IAAhBD,ECvBT,GAAME,KAAN,GAEe,UAA2B,IAAjBC,0DAAU,MAAO,SAC9BA,EAAQpB,QAARoB,EAD8B,CAErCD,EAAME,cAANF,GAFqC,EAG/BA,IAH+B,EAMjC,IANiC,UAOwB,CAAC,CAA5C9B,aAAUC,SAAVD,CAAoBE,OAApBF,CAA4B,SAA5BA,CAPmB,CASjC,IATiC,UAUyB,CAAC,CAA7CA,aAAUiC,UAAVjC,CAAqBE,OAArBF,CAA6B,SAA7BA,CAVmB,CAYjC,KAZiC,UAawB,CAAC,CAA5CA,aAAUC,SAAVD,CAAoBE,OAApBF,CAA4B,SAA5BA,GAAyF,CAAC,CAAzCA,aAAUC,SAAVD,CAAoBE,OAApBF,CAA4B,MAA5BA,CAb9B,UAkBlCkC,IAAMJ,EAAMI,GAANJ,EAAaK,OAAOC,IAAPD,IAAmBE,IAAnBF,CAAwB,kBAAOL,KAA/B,CAAAK,CAlBe,CAmBjCL,IAnBiC,EAF1C,CCEA,aAAiD,IAC3C,SACKnC,UAAS2C,gBAF6B,OAKzCC,GAAiBC,EAAK,EAALA,EAAW7C,SAAS0B,IAApBmB,CAA2B,KAG9CC,EAAe5B,EAAQ4B,YARoB,CAUxCA,OAAmC5B,EAAQ6B,kBAVH,IAW9B,CAAC7B,EAAUA,EAAQ6B,kBAAnB,EAAuCD,gBAGlDvB,GAAWuB,GAAgBA,EAAavB,SAdC,MAgB3C,IAA0B,MAAbA,IAAb,EAAiD,MAAbA,IAhBO,CAuBM,CAAC,CAApD,kBAAgBhB,OAAhB,CAAwBuC,EAAavB,QAArC,GACuD,QAAvDK,OAAuC,UAAvCA,CAxB6C,CA0BtCoB,IA1BsC,GAiBtC9B,EAAUA,EAAQS,aAART,CAAsByB,eAAhCzB,CAAkDlB,SAAS2C,6BCxBnB,IACzCpB,GAAaL,EAAbK,SADyC,MAEhC,MAAbA,IAF6C,GAMlC,MAAbA,MAAuByB,EAAgB9B,EAAQ+B,iBAAxBD,KANwB,ECKnD,aAAsC,OACZ,KAApBE,KAAK1B,UAD2B,GAE3B2B,EAAQD,EAAK1B,UAAb2B,ECGX,eAAmE,IAE7D,IAAa,CAACC,EAASjC,QAAvB,EAAmC,EAAnC,EAAgD,CAACkC,EAASlC,eACrDnB,UAAS2C,mBAIZW,GACJF,EAASG,uBAATH,IACAI,KAAKC,4BACDC,EAAQJ,MACRK,EAAML,MAGNM,EAAQ5D,SAAS6D,WAAT7D,KACR8D,WAAgB,EAf2C,GAgB3DC,SAAY,EAhB+C,IAiBzDC,GAA4BJ,EAA5BI,2BAILZ,OACCC,KADDD,EAEDM,EAAMO,QAANP,UAEIQ,QAIGlB,QAIHmB,GAAehB,KAjC4C,MAkC7DgB,GAAa1C,IAlCgD,CAmCxD2C,EAAuBD,EAAa1C,IAApC2C,GAnCwD,CAqCxDA,IAAiCjB,KAAkB1B,IAAnD2C,ECzCX,aAAyD,IAAdC,0DAAO,MAC1CC,EAAqB,KAATD,KAAiB,WAAjBA,CAA+B,aAC3C9C,EAAWL,EAAQK,YAER,MAAbA,MAAoC,MAAbA,KAAqB,IACxCgD,GAAOrD,EAAQS,aAART,CAAsByB,gBAC7B6B,EAAmBtD,EAAQS,aAART,CAAsBsD,gBAAtBtD,UAClBsD,YAGFtD,MCPT,eAAuE,IAAlBuD,4CAAAA,eAC7CC,EAAYC,IAAmB,KAAnBA,EACZC,EAAaD,IAAmB,MAAnBA,EACbE,EAAWJ,EAAW,CAAC,CAAZA,CAAgB,WAC5BK,KAAOJ,MACPK,QAAUL,MACVM,MAAQJ,MACRK,OAASL,MCRhB,eAAqD,IAC7CM,GAAiB,GAATC,KAAe,MAAfA,CAAwB,MAChCC,EAAkB,MAAVF,IAAmB,OAAnBA,CAA6B,eAGzCG,YAAWC,oBAAAA,CAAXD,CAA0C,EAA1CA,EACAA,WAAWC,oBAAAA,CAAXD,CAA0C,EAA1CA,qBCd8C,OACzCE,MAAKC,GAALD,CACL7D,YAAAA,CADK6D,CAEL7D,YAAAA,CAFK6D,CAGLhB,YAAAA,CAHKgB,CAILhB,YAAAA,CAJKgB,CAKLhB,YAAAA,CALKgB,CAML1C,EAAK,EAALA,EACI0B,YAAAA,EACAkB,YAAgC,QAATN,KAAoB,KAApBA,CAA4B,OAAnDM,CADAlB,CAEAkB,YAAgC,QAATN,KAAoB,QAApBA,CAA+B,QAAtDM,CAHJ5C,CAII,CAVC0C,EAcT,YAAyC,IACjC7D,GAAO1B,SAAS0B,KAChB6C,EAAOvE,SAAS2C,gBAChB8C,EAAgB5C,EAAK,EAALA,GAAYxB,0BAE3B,QACGqE,EAAQ,QAARA,OADH,OAEEA,EAAQ,OAARA,OAFF,gnBCfT,aAA+C,sBAGpCC,EAAQX,IAARW,CAAeA,EAAQC,aACtBD,EAAQb,GAARa,CAAcA,EAAQE,SCGlC,aAAuD,IACjDC,SAKA,IACEjD,EAAK,EAALA,EAAU,GACL3B,EAAQ6E,qBAAR7E,EADK,IAENwD,GAAYC,IAAmB,KAAnBA,EACZC,EAAaD,IAAmB,MAAnBA,IACdG,MAJO,GAKPE,OALO,GAMPD,SANO,GAOPE,QAPP,QAUS/D,EAAQ6E,qBAAR7E,EAXX,CAcA,QAAQ,KAEF8E,GAAS,MACPF,EAAKd,IADE,KAERc,EAAKhB,GAFG,OAGNgB,EAAKb,KAALa,CAAaA,EAAKd,IAHZ,QAILc,EAAKf,MAALe,CAAcA,EAAKhB,GAJd,EAQTmB,EAA6B,MAArB/E,KAAQK,QAARL,CAA8BgF,GAA9BhF,IACR0E,EACJK,EAAML,KAANK,EAAe/E,EAAQiF,WAAvBF,EAAsCD,EAAOf,KAAPe,CAAeA,EAAOhB,KACxDa,EACJI,EAAMJ,MAANI,EAAgB/E,EAAQkF,YAAxBH,EAAwCD,EAAOjB,MAAPiB,CAAgBA,EAAOlB,IAE7DuB,EAAiBnF,EAAQoF,WAARpF,GACjBqF,EAAgBrF,EAAQsF,YAARtF,MAIhBmF,KAAiC,IAC7Bf,GAAS1D,QACG6E,IAAuB,GAAvBA,CAFiB,IAGlBA,IAAuB,GAAvBA,CAHkB,GAK5Bb,QAL4B,GAM5BC,gBAGFa,qBCzD6F,OAajFnB,KAAKC,GAb4E,CAAvBmB,2CAAAA,cAAuB,CAC9FC,EAASC,EAAQ,EAARA,CADqF,CAE9FC,EAA6B,MAApBC,KAAOxF,QAF8E,CAG9FyF,EAAejB,IAH+E,CAI9FkB,EAAalB,IAJiF,CAK9FmB,EAAejF,IAL+E,CAO9FqD,EAAS1D,IAPqF,CAQ9FuF,EAAiB9B,WAAWC,EAAO6B,cAAlB9B,CAAkC,EAAlCA,CAR6E,CAS9F+B,EAAkB/B,WAAWC,EAAO8B,eAAlB/B,CAAmC,EAAnCA,CAT4E,CAYjGsB,GAAqC,MAApBI,KAAOxF,QAZyE,KAavFuD,IAAMS,EAAS0B,EAAWnC,GAApBS,CAAyB,CAAzBA,CAbiF,GAcvFP,KAAOO,EAAS0B,EAAWjC,IAApBO,CAA0B,CAA1BA,CAdgF,KAgBhGI,GAAUe,EAAc,KACrBM,EAAalC,GAAbkC,CAAmBC,EAAWnC,GAA9BkC,EADqB,MAEpBA,EAAahC,IAAbgC,CAAoBC,EAAWjC,IAA/BgC,EAFoB,OAGnBA,EAAapB,KAHM,QAIlBoB,EAAanB,MAJK,CAAda,OAMNW,UAAY,IACZC,WAAa,EAMjB,MAAmB,IACfD,GAAYhC,WAAWC,EAAO+B,SAAlBhC,CAA6B,EAA7BA,EACZiC,EAAajC,WAAWC,EAAOgC,UAAlBjC,CAA8B,EAA9BA,IAEXP,KAAOqC,GAJM,GAKbpC,QAAUoC,GALG,GAMbnC,MAAQoC,GANK,GAObnC,OAASmC,GAPI,GAUbC,WAVa,GAWbC,oBAIRV,GAAU,EAAVA,CACIG,EAAO9C,QAAP8C,GADJH,CAEIG,OAAqD,MAA1BG,KAAa3F,cAElCgG,uBCnDwF,OAGtFhC,KAAKC,GAHiF,CAAvBgC,2CAAAA,cAAuB,CAC9FjD,EAAOrD,EAAQS,aAART,CAAsByB,eADiE,CAE9F8E,EAAiBC,MAF6E,CAG9F9B,EAAQL,EAAShB,EAAK4B,WAAdZ,CAA2BxF,OAAO4H,UAAP5H,EAAqB,CAAhDwF,CAHsF,CAI9FM,EAASN,EAAShB,EAAK6B,YAAdb,CAA4BxF,OAAO6H,WAAP7H,EAAsB,CAAlDwF,CAJqF,CAM9Fb,EAAY,EAAmC,CAAnC,CAAiBC,IANiE,CAO9FC,EAAa,EAA2C,CAA3C,CAAiBD,IAAgB,MAAhBA,CAPgE,CAS9FkD,EAAS,KACRnD,EAAY+C,EAAe3C,GAA3BJ,CAAiC+C,EAAeJ,SADxC,MAEPzC,EAAa6C,EAAezC,IAA5BJ,CAAmC6C,EAAeH,UAF3C,QAAA,SAAA,CATqF,OAgB7FZ,MCTT,aAAyC,IACjCnF,GAAWL,EAAQK,SADc,MAEtB,MAAbA,MAAoC,MAAbA,IAFY,MAKe,OAAlDK,OAAkC,UAAlCA,CALmC,GAQhCkG,EAAQ5F,IAAR4F,ECTT,aAA8D,IAEvD,IAAY,CAAC5G,EAAQ6G,aAArB,EAAsClF,UAClC7C,UAAS2C,gBAH0C,OAKxDqF,GAAK9G,EAAQ6G,aAL2C,CAMrDC,GAAoD,MAA9CpG,OAA6B,WAA7BA,CAN+C,IAOrDoG,EAAGD,oBAEHC,IAAMhI,SAAS2C,gBCCxB,mBAME,IADAgE,4CAAAA,eAIIsB,EAAa,CAAEnD,IAAK,CAAP,CAAUE,KAAM,CAAhB,EACXlC,EAAe6D,EAAgBuB,IAAhBvB,CAAuDvC,UAGlD,UAAtB+D,OACWC,WAGV,IAECC,GACsB,cAAtBF,IAHD,IAIgBlG,EAAgBC,IAAhBD,CAJhB,CAK+B,MAA5BoG,KAAe9G,QALlB,KAMkB+G,EAAO3G,aAAP2G,CAAqB3F,eANvC,GAQ8B,QAAtBwF,IARR,GASgBG,EAAO3G,aAAP2G,CAAqB3F,eATrC,IAAA,IAcGgD,GAAU+B,YAOgB,MAA5BW,KAAe9G,QAAf8G,EAAsC,CAACP,KAAuB,OACtC5B,IAAlBL,IAAAA,OAAQD,IAAAA,QACLd,KAAOa,EAAQb,GAARa,CAAcA,EAAQ0B,SAFwB,GAGrDtC,OAASc,EAASF,EAAQb,GAH2B,GAIrDE,MAAQW,EAAQX,IAARW,CAAeA,EAAQ2B,UAJsB,GAKrDrC,MAAQW,EAAQD,EAAQX,IALrC,mBAaSA,UACAF,SACAG,WACAF,yBCxEuB,IAAjBa,KAAAA,MAAOC,IAAAA,aACjBD,KAYT,qBAOE,IADA2C,0DAAU,KAEwB,CAAC,CAA/BC,KAAUjI,OAAViI,CAAkB,MAAlBA,cAIEP,GAAaQ,WAObC,EAAQ,KACP,OACIT,EAAWrC,KADf,QAEK+C,EAAQ7D,GAAR6D,CAAcV,EAAWnD,GAF9B,CADO,OAKL,OACEmD,EAAWhD,KAAXgD,CAAmBU,EAAQ1D,KAD7B,QAEGgD,EAAWpC,MAFd,CALK,QASJ,OACCoC,EAAWrC,KADZ,QAEEqC,EAAWlD,MAAXkD,CAAoBU,EAAQ5D,MAF9B,CATI,MAaN,OACG4D,EAAQ3D,IAAR2D,CAAeV,EAAWjD,IAD7B,QAEIiD,EAAWpC,MAFf,CAbM,EAmBR+C,EAAcpG,OAAOC,IAAPD,IACjBqG,GADiBrG,CACb,6BAEAkG,WACGI,EAAQJ,IAARI,GAJU,CAAAtG,EAMjBuG,IANiBvG,CAMZ,oBAAUwG,GAAEC,IAAFD,CAASE,EAAED,IANT,CAAAzG,EAQd2G,EAAgBP,EAAYQ,MAAZR,CACpB,eAAGhD,KAAAA,MAAOC,IAAAA,aACRD,IAAS0C,EAAOnC,WAAhBP,EAA+BC,GAAUyC,EAAOlC,YAF9B,CAAAwC,EAKhBS,EAA2C,CAAvBF,GAAc/I,MAAd+I,CACtBA,EAAc,CAAdA,EAAiBG,GADKH,CAEtBP,EAAY,CAAZA,EAAeU,IAEbC,EAAYf,EAAUgB,KAAVhB,CAAgB,GAAhBA,EAAqB,CAArBA,QAEXa,IAAqBE,OAAAA,CAA8B,EAAnDF,EC1DT,iBAA4F,IAAtB1C,0DAAgB,KAC9E8C,EAAqB9C,EAAgBuB,IAAhBvB,CAAuDvC,aAC3EsD,UCTT,aAA+C,IACvCpC,GAASjE,oBACTqI,EAAIrE,WAAWC,EAAO+B,SAAlBhC,EAA+BA,WAAWC,EAAOqE,YAAlBtE,EACnCuE,EAAIvE,WAAWC,EAAOgC,UAAlBjC,EAAgCA,WAAWC,EAAOuE,WAAlBxE,EACpCW,EAAS,OACN9E,EAAQoF,WAARpF,EADM,QAELA,EAAQsF,YAARtF,EAFK,WCJjB,aAAwD,IAChD4I,GAAO,CAAE9E,KAAM,OAAR,CAAiBC,MAAO,MAAxB,CAAgCF,OAAQ,KAAxC,CAA+CD,IAAK,QAApD,QACN0D,GAAUuB,OAAVvB,CAAkB,wBAAlBA,CAA4C,kBAAWsB,KAAvD,CAAAtB,ECIT,iBAA8E,GAChEA,EAAUgB,KAAVhB,CAAgB,GAAhBA,EAAqB,CAArBA,CADgE,IAItEwB,GAAaC,KAGbC,EAAgB,OACbF,EAAWpE,KADE,QAEZoE,EAAWnE,MAFC,EAMhBsE,EAAmD,CAAC,CAA1C,oBAAkB5J,OAAlB,IACV6J,EAAWD,EAAU,KAAVA,CAAkB,OAC7BE,EAAgBF,EAAU,MAAVA,CAAmB,MACnCG,EAAcH,EAAU,QAAVA,CAAqB,QACnCI,EAAuB,EAAsB,OAAtB,CAAW,qBAGtCC,KACAA,KAAgC,CADhCA,CAEAR,KAA0B,OACxBxB,MAEAgC,KAAkCR,KAGlCQ,EAAiBC,IAAjBD,IChCN,eAAyC,OAEnCE,OAAMC,SAAND,CAAgBE,IAFmB,CAG9BC,EAAID,IAAJC,GAH8B,CAOhCA,EAAIzB,MAAJyB,IAAkB,CAAlBA,ECLT,iBAAoD,IAE9CH,MAAMC,SAAND,CAAgBI,gBACXD,GAAIC,SAAJD,CAAc,kBAAOE,SAArB,CAAAF,KAIHG,GAAQJ,IAAU,kBAAOK,SAAjB,CAAAL,QACPC,GAAItK,OAAJsK,ICLT,iBAA4D,IACpDK,GAAiBC,aAEnBC,EAAUC,KAAVD,CAAgB,CAAhBA,CAAmBN,IAAqB,MAArBA,GAAnBM,WAEWE,QAAQ,WAAY,CAC7BzG,EAAS,UAATA,CAD6B,UAEvB0G,KAAK,wDAFkB,IAI3BC,GAAK3G,EAAS,UAATA,GAAwBA,EAAS2G,GACxC3G,EAAS4G,OAAT5G,EAAoB6G,IALS,KAS1B/F,QAAQ2C,OAAS5B,EAAciF,EAAKhG,OAALgG,CAAarD,MAA3B5B,CATS,GAU1Bf,QAAQiG,UAAYlF,EAAciF,EAAKhG,OAALgG,CAAaC,SAA3BlF,CAVM,GAYxB8E,MAZwB,CAAnC,KCPF,YAAiC,KAE3B,KAAKK,KAAL,CAAWC,gBAIXH,GAAO,UACC,IADD,UAAA,eAAA,cAAA,WAAA,WAAA,IAUNhG,QAAQiG,UAAYG,EACvB,KAAKF,KADkBE,CAEvB,KAAKzD,MAFkByD,CAGvB,KAAKH,SAHkBG,CAIvB,KAAKC,OAAL,CAAaC,aAJUF,IAUpBvD,UAAY0D,EACf,KAAKF,OAAL,CAAaxD,SADE0D,CAEfP,EAAKhG,OAALgG,CAAaC,SAFEM,CAGf,KAAK5D,MAHU4D,CAIf,KAAKN,SAJUM,CAKf,KAAKF,OAAL,CAAaZ,SAAb,CAAuBe,IAAvB,CAA4BhE,iBALb+D,CAMf,KAAKF,OAAL,CAAaZ,SAAb,CAAuBe,IAAvB,CAA4B5D,OANb2D,IAUZE,kBAAoBT,EAAKnD,YAEzByD,cAAgB,KAAKD,OAAL,CAAaC,gBAG7BtG,QAAQ2C,OAAS+D,EACpB,KAAK/D,MADe+D,CAEpBV,EAAKhG,OAALgG,CAAaC,SAFOS,CAGpBV,EAAKnD,SAHe6D,IAKjB1G,QAAQ2C,OAAOgE,SAAW,KAAKN,OAAL,CAAaC,aAAb,CAA6B,OAA7B,CAAuC,aAG/DM,EAAa,KAAKnB,SAAlBmB,IAIF,KAAKV,KAAL,CAAWW,eAITR,QAAQS,kBAHRZ,MAAMW,kBACNR,QAAQU,cC7DjB,eAAmE,OAC1DtB,GAAU1I,IAAV0I,CACL,eAAGuB,KAAAA,KAAMlB,IAAAA,cAAcA,IAAWkB,KAD7B,CAAAvB,ECAT,aAA2D,KAIpD,GAHCwB,+BAGD,CAFCC,EAAYvL,EAASwL,MAATxL,CAAgB,CAAhBA,EAAmByL,WAAnBzL,GAAmCA,EAAS+J,KAAT/J,CAAe,CAAfA,CAEhD,CAAInB,EAAI,EAAGA,EAAIyM,EAASxM,OAAQD,IAAK,IAClC6M,GAASJ,KACTK,EAAUD,QAAAA,MAC4B,WAAxC,QAAOhN,UAAS0B,IAAT1B,CAAckN,KAAdlN,mBAIN,MCVT,YAAkC,aAC3B6L,MAAMC,eAGPqB,EAAkB,KAAK/B,SAAvB+B,CAAkC,YAAlCA,SACG7E,OAAO8E,gBAAgB,oBACvB9E,OAAO4E,MAAMZ,SAAW,QACxBhE,OAAO4E,MAAMpI,IAAM,QACnBwD,OAAO4E,MAAMlI,KAAO,QACpBsD,OAAO4E,MAAMjI,MAAQ,QACrBqD,OAAO4E,MAAMnI,OAAS,QACtBuD,OAAO4E,MAAMG,WAAa,QAC1B/E,OAAO4E,MAAMI,EAAyB,WAAzBA,GAAyC,SAGxDC,wBAID,KAAKvB,OAAL,CAAawB,sBACVlF,OAAO9G,WAAWiM,YAAY,KAAKnF,QAEnC,KCzBT,aAA2C,IACnC3G,GAAgBT,EAAQS,oBACvBA,GAAgBA,EAAc+L,WAA9B/L,CAA4C5B,0BCJwB,IACrE4N,GAAmC,MAA1BzG,KAAa3F,SACtBqM,EAASD,EAASzG,EAAavF,aAAbuF,CAA2BwG,WAApCC,KACRE,qBAAkC,CAAEC,UAAF,EAHkC,MAOvE7L,EAAgB2L,EAAOpM,UAAvBS,QAPuE,GAa7D8L,QAShB,mBAKE,GAEMC,aAFN,MAGqBH,iBAAiB,SAAUhC,EAAMmC,YAAa,CAAEF,UAAF,EAHnE,IAMMG,GAAgBhM,gBAGpB,SACA4J,EAAMmC,YACNnC,EAAMqC,iBAEFD,kBACAE,mBCpCR,YAA+C,CACxC,KAAKtC,KAAL,CAAWsC,aAD6B,QAEtCtC,MAAQuC,EACX,KAAKxC,SADMwC,CAEX,KAAKpC,OAFMoC,CAGX,KAAKvC,KAHMuC,CAIX,KAAKC,cAJMD,CAF8B,ECA/C,eAA+D,aAExCE,oBAAoB,SAAUzC,EAAMmC,eAGnDE,cAAc5C,QAAQ,WAAU,GAC7BgD,oBAAoB,SAAUzC,EAAMmC,YAD7C,KAKMA,YAAc,OACdE,mBACAD,cAAgB,OAChBE,mBCZR,YAAgD,CAC1C,KAAKtC,KAAL,CAAWsC,aAD+B,wBAEvB,KAAKE,eAFkB,MAGvCxC,MAAQ0C,EAAqB,KAAK3C,SAA1B2C,CAAqC,KAAK1C,KAA1C0C,CAH+B,ECFhD,aAAqC,OACtB,EAANC,MAAY,CAACC,MAAMpJ,aAANoJ,CAAbD,EAAqCE,YCE9C,gBAAmD,QAC1CjM,QAAa6I,QAAQ,WAAQ,IAC9BqD,GAAO,GAIP,CAAC,CADH,oDAAsDpO,OAAtD,KAEAqO,EAAUtJ,IAAVsJ,CANgC,KAQzB,IARyB,IAU1B1B,SAAc5H,MAVxB,GCHF,gBAA2D,QAClD7C,QAAiB6I,QAAQ,WAAe,IACvCuD,GAAQC,KACVD,MAFyC,GAKnCzB,kBALmC,GAGnC2B,eAAmBD,KAH/B,GCKF,cAAyC,WAK7BnD,EAAKqD,QAALrD,CAAcrD,OAAQqD,EAAKrG,WAIvBqG,EAAKqD,QAALrD,CAAcrD,OAAQqD,EAAKmD,YAGrCnD,EAAKsD,YAALtD,EAAqBnJ,OAAOC,IAAPD,CAAYmJ,EAAKuD,WAAjB1M,EAA8BpC,WAC3CuL,EAAKsD,aAActD,EAAKuD,eAgBtC,sBAME,IAEM1E,GAAmBuB,QAA8CC,EAAQC,aAAtDF,EAKnBvD,EAAY0D,EAChBF,EAAQxD,SADQ0D,OAKhBF,EAAQZ,SAARY,CAAkBG,IAAlBH,CAAuB7D,iBALP+D,CAMhBF,EAAQZ,SAARY,CAAkBG,IAAlBH,CAAuBzD,OANP2D,WASX6C,aAAa,sBAIF,CAAEzC,SAAUN,EAAQC,aAARD,CAAwB,OAAxBA,CAAkC,UAA9C,KCzDpB,gBAAoD,OA6B1CzG,KAAK4J,KA7BqC,CAC1CzF,EAASsC,EAATtC,CAD0C,CACvCE,EAAMoC,EAANpC,CADuC,CAE1CtB,EAAWqD,EAAKhG,OAALgG,CAAXrD,MAF0C,CAK5C8G,EAA8BxE,EAClCe,EAAKqD,QAALrD,CAAcP,SADoBR,CAElC,kBAA8B,YAAlB/F,KAAS8H,IAFa,CAAA/B,EAGlCyE,eARgD,CAS9CD,UAT8C,UAUxC7D,KACN,gIAX8C,IAoD9CvG,GAAMF,EAtCJuK,EACJD,WAEIpD,EAAQqD,eAFZD,GAIItM,EAAeE,EAAgB2I,EAAKqD,QAALrD,CAAcrD,MAA9BtF,EACfsM,EAAmBvJ,KAGnBT,EAAS,UACHgD,EAAOgE,QADJ,EAKT3G,EAAU,MACRJ,EAAW+C,EAAOtD,IAAlBO,CADQ,KAETA,EAAW+C,EAAOxD,GAAlBS,CAFS,QAGNA,EAAW+C,EAAOvD,MAAlBQ,CAHM,OAIPA,EAAW+C,EAAOrD,KAAlBM,CAJO,EAOVL,EAAc,QAANwE,KAAiB,KAAjBA,CAAyB,SACjCtE,EAAc,OAANwE,KAAgB,MAAhBA,CAAyB,QAKjC2F,EAAmBjC,EAAyB,WAAzBA,OAYX,QAAVpI,IACI,CAACoK,EAAiBzJ,MAAlB,CAA2BF,EAAQZ,OAEnCY,EAAQb,MAEF,OAAVM,IACK,CAACkK,EAAiB1J,KAAlB,CAA0BD,EAAQV,MAElCU,EAAQX,KAEbqK,kDAEc,OACA,IACThC,WAAa,gBACf,IAECmC,GAAsB,QAAVtK,IAAqB,CAAC,CAAtBA,CAA0B,EACtCuK,EAAuB,OAAVrK,IAAoB,CAAC,CAArBA,CAAyB,OAC5BN,GAJX,MAKWE,GALX,GAMEqI,WAAgBnI,MAAAA,MAInB4J,GAAa,eACFnD,EAAKnD,SADH,WAKdsG,kBAAiCnD,EAAKmD,cACtCxJ,cAAyBqG,EAAKrG,UAC9B4J,iBAAmBvD,EAAKhG,OAALgG,CAAa+D,MAAU/D,EAAKuD,eCrFtD,kBAIE,IACMS,GAAa/E,IAAgB,eAAG+B,KAAAA,WAAWA,MAA9B,CAAA/B,EAEbgF,EACJ,CAAC,EAAD,EACAxE,EAAU1I,IAAV0I,CAAe,WAAY,OAEvBvG,GAAS8H,IAAT9H,MACAA,EAAS4G,OADT5G,EAEAA,EAASvB,KAATuB,CAAiB8K,EAAWrM,KAJhC,CAAA8H,KAQE,GAAa,IACTuE,qBAEEpE,cACHsE,4BAAAA,8DAAAA,iBCrBT,gBAA6C,UAEvC,CAACC,GAAmBnE,EAAKqD,QAALrD,CAAcP,SAAjC0E,CAA4C,OAA5CA,CAAqD,cAArDA,cAIDb,GAAejD,EAAQ9K,WAGC,QAAxB,iBACayK,EAAKqD,QAALrD,CAAcrD,MAAdqD,CAAqBoE,aAArBpE,IAGX,qBAMA,CAACA,EAAKqD,QAALrD,CAAcrD,MAAdqD,CAAqB1H,QAArB0H,mBACKJ,KACN,sEAMA/C,GAAYmD,EAAKnD,SAALmD,CAAenC,KAAfmC,CAAqB,GAArBA,EAA0B,CAA1BA,IACYA,EAAKhG,QAA3B2C,IAAAA,OAAQsD,IAAAA,UACVoE,EAAsD,CAAC,CAA1C,oBAAkBzP,OAAlB,IAEb0P,EAAMD,EAAa,QAAbA,CAAwB,QAC9BE,EAAkBF,EAAa,KAAbA,CAAqB,OACvC3L,EAAO6L,EAAgBC,WAAhBD,GACPE,EAAUJ,EAAa,MAAbA,CAAsB,MAChCK,EAASL,EAAa,QAAbA,CAAwB,QACjCM,EAAmBrG,QAQrB2B,OAAuCtD,IA5CA,KA6CpC3C,QAAQ2C,WACXA,MAAgBsD,MAAhBtD,CA9CuC,EAiDvCsD,OAAqCtD,IAjDE,KAkDpC3C,QAAQ2C,WACXsD,OAAqCtD,IAnDE,IAqDtC3C,QAAQ2C,OAAS5B,EAAciF,EAAKhG,OAALgG,CAAarD,MAA3B5B,CArDqB,IAwDrC6J,GAAS3E,KAAkBA,KAAiB,CAAnCA,CAAuC0E,EAAmB,EAInElP,EAAMQ,EAAyB+J,EAAKqD,QAALrD,CAAcrD,MAAvC1G,EACN4O,EAAmBnL,WAAWjE,YAAAA,CAAXiE,CAA4C,EAA5CA,EACnBoL,EAAmBpL,WAAWjE,oBAAAA,CAAXiE,CAAiD,EAAjDA,EACrBqL,EACFH,EAAS5E,EAAKhG,OAALgG,CAAarD,MAAbqD,GAAT4E,cAGUhL,KAAKC,GAALD,CAASA,KAAKoL,GAALpL,CAAS+C,MAAT/C,GAATA,CAA8D,CAA9DA,IAEP0J,iBACAtJ,QAAQ+J,kBACHnK,KAAKqL,KAALrL,WACG,SC7Ef,cAAwD,IACpC,KAAdgE,WACK,QAF6C,MAG7B,OAAdA,IAH2C,CAI7C,KAJ6C,GCwBxD,yKAAA,CC5BMsH,GAAkBC,GAAWzF,KAAXyF,CAAiB,CAAjBA,CD4BxB,CChBA,cAA8D,IAAjBC,4CAAAA,eACrCC,EAAQH,GAAgBtQ,OAAhBsQ,IACRhG,EAAMgG,GACTxF,KADSwF,CACHG,EAAQ,CADLH,EAETI,MAFSJ,CAEFA,GAAgBxF,KAAhBwF,CAAsB,CAAtBA,GAFEA,QAGLE,GAAUlG,EAAIqG,OAAJrG,EAAVkG,MCZHI,IAAY,MACV,MADU,WAEL,WAFK,kBAGE,kBAHF,EAalB,gBAA4C,IAEtChE,EAAkBxB,EAAKqD,QAALrD,CAAcP,SAAhC+B,CAA2C,OAA3CA,cAIAxB,EAAKyF,OAALzF,EAAgBA,EAAKnD,SAALmD,GAAmBA,EAAKS,8BAKtCnE,GAAaQ,EACjBkD,EAAKqD,QAALrD,CAAcrD,MADGG,CAEjBkD,EAAKqD,QAALrD,CAAcC,SAFGnD,CAGjBuD,EAAQzD,OAHSE,CAIjBuD,EAAQ7D,iBAJSM,CAKjBkD,EAAKM,aALYxD,EAQfD,EAAYmD,EAAKnD,SAALmD,CAAenC,KAAfmC,CAAqB,GAArBA,EAA0B,CAA1BA,EACZ0F,EAAoB5G,KACpBlB,EAAYoC,EAAKnD,SAALmD,CAAenC,KAAfmC,CAAqB,GAArBA,EAA0B,CAA1BA,GAAgC,GAE5C2F,YAEItF,EAAQuF,cACTJ,IAAUK,OACD,gBAETL,IAAUM,YACDC,gBAETP,IAAUQ,mBACDD,yBAGA1F,EAAQuF,mBAGdjG,QAAQ,aAAiB,IAC7B9C,OAAsB8I,EAAUlR,MAAVkR,GAAqBN,EAAQ,aAI3CrF,EAAKnD,SAALmD,CAAenC,KAAfmC,CAAqB,GAArBA,EAA0B,CAA1BA,CALqB,GAMblB,IANa,IAQ3BP,GAAgByB,EAAKhG,OAALgG,CAAarD,OAC7BsJ,EAAajG,EAAKhG,OAALgG,CAAaC,UAG1BuD,EAAQ5J,KAAK4J,MACb0C,EACW,MAAdrJ,MACC2G,EAAMjF,EAAcjF,KAApBkK,EAA6BA,EAAMyC,EAAW5M,IAAjBmK,CAD9B3G,EAEc,OAAdA,MACC2G,EAAMjF,EAAclF,IAApBmK,EAA4BA,EAAMyC,EAAW3M,KAAjBkK,CAH7B3G,EAIc,KAAdA,MACC2G,EAAMjF,EAAcnF,MAApBoK,EAA8BA,EAAMyC,EAAW9M,GAAjBqK,CAL/B3G,EAMc,QAAdA,MACC2G,EAAMjF,EAAcpF,GAApBqK,EAA2BA,EAAMyC,EAAW7M,MAAjBoK,EAEzB2C,EAAgB3C,EAAMjF,EAAclF,IAApBmK,EAA4BA,EAAMlH,EAAWjD,IAAjBmK,EAC5C4C,EAAiB5C,EAAMjF,EAAcjF,KAApBkK,EAA6BA,EAAMlH,EAAWhD,KAAjBkK,EAC9C6C,EAAe7C,EAAMjF,EAAcpF,GAApBqK,EAA2BA,EAAMlH,EAAWnD,GAAjBqK,EAC1C8C,EACJ9C,EAAMjF,EAAcnF,MAApBoK,EAA8BA,EAAMlH,EAAWlD,MAAjBoK,EAE1B+C,EACW,MAAd1J,SACc,OAAdA,OADAA,EAEc,KAAdA,OAFAA,EAGc,QAAdA,QAGGwH,EAAsD,CAAC,CAA1C,oBAAkBzP,OAAlB,IACb4R,EACJ,CAAC,CAACnG,EAAQoG,cAAV,GACEpC,GAA4B,OAAdzG,IAAdyG,KACCA,GAA4B,KAAdzG,IAAdyG,GADDA,EAEC,IAA6B,OAAdzG,IAAf,GAFDyG,EAGC,IAA6B,KAAdzG,IAAf,GAJH,EAtC+B,CA4C7BsI,OA5C6B,MA8C1BT,UA9C0B,EAgD3BS,IAhD2B,MAiDjBP,EAAUN,EAAQ,CAAlBM,CAjDiB,QAqDjBe,KArDiB,IAwD1B7J,UAAYA,GAAae,EAAY,KAAZA,CAA8B,EAA3Cf,CAxDc,GA4D1B7C,QAAQ2C,YACRqD,EAAKhG,OAALgG,CAAarD,OACb+D,EACDV,EAAKqD,QAALrD,CAAcrD,MADb+D,CAEDV,EAAKhG,OAALgG,CAAaC,SAFZS,CAGDV,EAAKnD,SAHJ6D,EA9D0B,GAqExBE,EAAaZ,EAAKqD,QAALrD,CAAcP,SAA3BmB,GAA4C,MAA5CA,CArEwB,CAAnC,KCrDF,cAA2C,OACXZ,EAAKhG,QAA3B2C,IAAAA,OAAQsD,IAAAA,UACVpD,EAAYmD,EAAKnD,SAALmD,CAAenC,KAAfmC,CAAqB,GAArBA,EAA0B,CAA1BA,EACZwD,EAAQ5J,KAAK4J,MACba,EAAsD,CAAC,CAA1C,oBAAkBzP,OAAlB,IACb8D,EAAO2L,EAAa,OAAbA,CAAuB,SAC9BK,EAASL,EAAa,MAAbA,CAAsB,MAC/B1F,EAAc0F,EAAa,OAAbA,CAAuB,eAEvC1H,MAAe6G,EAAMvD,IAANuD,MACZxJ,QAAQ2C,UACX6G,EAAMvD,IAANuD,EAA2B7G,MAE3BA,KAAiB6G,EAAMvD,IAANuD,MACdxJ,QAAQ2C,UAAiB6G,EAAMvD,IAANuD,KCLlC,oBAA2E,OA6B9D5J,KAAKC,GA7ByD,CAEnEgE,EAAQ8I,EAAItH,KAAJsH,CAAU,2BAAVA,CAF2D,CAGnEzD,EAAQ,CAACrF,EAAM,CAANA,CAH0D,CAInEmF,EAAOnF,EAAM,CAANA,CAJ4D,IAOrE,eAIsB,CAAtBmF,KAAKpO,OAALoO,CAAa,GAAbA,EAAyB,IACvBzN,iBAEG,mBAGA,QACA,qBAKD4E,GAAOY,WACNZ,MAAoB,GAApBA,EAbT,CAcO,GAAa,IAAT6I,MAA0B,IAATA,IAArB,CAAoC,IAErC4D,YACS,IAAT5D,KACKpJ,EACLvF,SAAS2C,eAAT3C,CAAyBoG,YADpBb,CAELxF,OAAO6H,WAAP7H,EAAsB,CAFjBwF,EAKAA,EACLvF,SAAS2C,eAAT3C,CAAyBmG,WADpBZ,CAELxF,OAAO4H,UAAP5H,EAAqB,CAFhBwF,EAKFgN,EAAO,GAAPA,EAdF,UAiCT,oBAKE,IACM5M,SAKA6M,EAAyD,CAAC,CAA9C,oBAAkBjS,OAAlB,IAIZkS,EAAY5K,EAAO2B,KAAP3B,CAAa,SAAbA,EAAwBgB,GAAxBhB,CAA4B,kBAAQ6K,GAAKC,IAALD,EAApC,CAAA7K,EAIZ+K,EAAUH,EAAUlS,OAAVkS,CACd7H,IAAgB,kBAAgC,CAAC,CAAzB8H,KAAKG,MAALH,CAAY,MAAZA,CAAxB,CAAA9H,CADc6H,EAIZA,MAA0D,CAAC,CAArCA,QAAmBlS,OAAnBkS,CAA2B,GAA3BA,CAlB1B,UAmBUlH,KACN,+EApBJ,IA0BMuH,GAAa,cACfC,EAAkB,CAAC,CAAbH,KASN,GATMA,CACN,CACEH,EACGpH,KADHoH,CACS,CADTA,IAEGxB,MAFHwB,CAEU,CAACA,KAAmBjJ,KAAnBiJ,IAAqC,CAArCA,CAAD,CAFVA,CADF,CAIE,CAACA,KAAmBjJ,KAAnBiJ,IAAqC,CAArCA,CAAD,EAA0CxB,MAA1C,CACEwB,EAAUpH,KAAVoH,CAAgBG,EAAU,CAA1BH,CADF,CAJF,WAWEM,EAAIlK,GAAJkK,CAAQ,aAAe,IAErBzI,GAAc,CAAW,CAAV0G,KAAc,EAAdA,EAAD,EAChB,QADgB,CAEhB,QACAgC,WAEFC,GAGGC,MAHHD,CAGU,aAAU,OACQ,EAApB/J,KAAEA,EAAE9I,MAAF8I,CAAW,CAAbA,GAAoD,CAAC,CAA3B,aAAW3I,OAAX,GADd,IAEZ2I,EAAE9I,MAAF8I,CAAW,IAFC,KAAA,SAMZA,EAAE9I,MAAF8I,CAAW,KANC,KAAA,IAUPA,EAAE+H,MAAF/H,GAbb,CAAA+J,KAiBGpK,GAjBHoK,CAiBO,kBAAOE,YAjBd,CAAAF,CAPE,CAAAF,IA6BFzH,QAAQ,aAAe,GACtBA,QAAQ,aAAkB,CACvBsD,IADuB,SAEP8D,GAA2B,GAAnBO,KAAGG,EAAS,CAAZH,EAAyB,CAAC,CAA1BA,CAA8B,CAAtCP,CAFO,CAA7B,EADF,KAmBF,gBAAiD,IAI3C/M,GAJiCkC,IAAAA,OAC7BW,EAA8CmD,EAA9CnD,YAA8CmD,EAAnChG,QAAW2C,IAAAA,OAAQsD,IAAAA,UAChCyH,EAAgB7K,EAAUgB,KAAVhB,CAAgB,GAAhBA,EAAqB,CAArBA,WAGlBoG,EAAU,EAAVA,EACQ,CAAC,EAAD,CAAU,CAAV,EAEA0E,YAGU,MAAlBD,QACKvO,KAAOa,EAAQ,CAARA,IACPX,MAAQW,EAAQ,CAARA,GACY,OAAlB0N,QACFvO,KAAOa,EAAQ,CAARA,IACPX,MAAQW,EAAQ,CAARA,GACY,KAAlB0N,QACFrO,MAAQW,EAAQ,CAARA,IACRb,KAAOa,EAAQ,CAARA,GACa,QAAlB0N,SACFrO,MAAQW,EAAQ,CAARA,IACRb,KAAOa,EAAQ,CAARA,KAGX2C,WCrLP,gBAAuD,IACjDH,GACF6D,EAAQ7D,iBAAR6D,EAA6BhJ,EAAgB2I,EAAKqD,QAALrD,CAAcrD,MAA9BtF,EAK3B2I,EAAKqD,QAALrD,CAAcC,SAAdD,IAPiD,KAQ/B3I,IAR+B,KAW/CiF,GAAaQ,EACjBkD,EAAKqD,QAALrD,CAAcrD,MADGG,CAEjBkD,EAAKqD,QAALrD,CAAcC,SAFGnD,CAGjBuD,EAAQzD,OAHSE,GAKjBkD,EAAKM,aALYxD,IAOXR,YAlB6C,IAoB/C3E,GAAQ0I,EAAQuH,SAClBjL,EAASqD,EAAKhG,OAALgG,CAAarD,OAEpBkL,EAAQ,oBACO,IACb3E,GAAQvG,WAEVA,MAAoBL,IAApBK,EACA,CAAC0D,EAAQyH,wBAEDlO,KAAKC,GAALD,CAAS+C,IAAT/C,CAA4B0C,IAA5B1C,YAPA,CAAA,sBAWS,IACb6E,GAAyB,OAAd5B,KAAwB,MAAxBA,CAAiC,MAC9CqG,EAAQvG,WAEVA,MAAoBL,IAApBK,EACA,CAAC0D,EAAQyH,wBAEDlO,KAAKoL,GAALpL,CACN+C,IADM/C,CAEN0C,MACiB,OAAdO,KAAwBF,EAAO1C,KAA/B4C,CAAuCF,EAAOzC,MADjDoC,CAFM1C,aAlBA,WA4BR+F,QAAQ,WAAa,IACnBjH,GAA8C,CAAC,CAAxC,kBAAgB9D,OAAhB,IAET,WAFS,CACT,mBAEqBiT,QAJ3B,KAOK7N,QAAQ2C,WC7Df,cAAoC,IAC5BE,GAAYmD,EAAKnD,UACjB6K,EAAgB7K,EAAUgB,KAAVhB,CAAgB,GAAhBA,EAAqB,CAArBA,EAChBkL,EAAiBlL,EAAUgB,KAAVhB,CAAgB,GAAhBA,EAAqB,CAArBA,OAGH,OACYmD,EAAKhG,QAA3BiG,IAAAA,UAAWtD,IAAAA,OACb0H,EAA0D,CAAC,CAA9C,oBAAkBzP,OAAlB,IACb8D,EAAO2L,EAAa,MAAbA,CAAsB,MAC7B1F,EAAc0F,EAAa,OAAbA,CAAuB,SAErC2D,EAAe,cACF/H,KADE,YAGTA,KAAkBA,IAAlBA,CAA2CtD,KAHlC,IAOhB3C,QAAQ2C,cAAyBqL,eChB1C,cAAmC,IAC7B,CAAC7D,GAAmBnE,EAAKqD,QAALrD,CAAcP,SAAjC0E,CAA4C,MAA5CA,CAAoD,iBAApDA,cAICnH,GAAUgD,EAAKhG,OAALgG,CAAaC,UACvBgI,EAAQhJ,EACZe,EAAKqD,QAALrD,CAAcP,SADFR,CAEZ,kBAA8B,iBAAlB/F,KAAS8H,IAFT,CAAA/B,EAGZ3C,cAGAU,EAAQ5D,MAAR4D,CAAiBiL,EAAM9O,GAAvB6D,EACAA,EAAQ3D,IAAR2D,CAAeiL,EAAM3O,KADrB0D,EAEAA,EAAQ7D,GAAR6D,CAAciL,EAAM7O,MAFpB4D,EAGAA,EAAQ1D,KAAR0D,CAAgBiL,EAAM5O,KACtB,IAEI2G,OAAKkI,gBAIJA,OANL,GAOK/E,WAAW,uBAAyB,EAZ3C,KAaO,IAEDnD,OAAKkI,gBAIJA,OANA,GAOA/E,WAAW,mCC/BpB,cAAoC,IAC5BtG,GAAYmD,EAAKnD,UACjB6K,EAAgB7K,EAAUgB,KAAVhB,CAAgB,GAAhBA,EAAqB,CAArBA,IACQmD,EAAKhG,QAA3B2C,IAAAA,OAAQsD,IAAAA,UACVzB,EAAuD,CAAC,CAA9C,oBAAkB5J,OAAlB,IAEVuT,EAA4D,CAAC,CAA5C,kBAAgBvT,OAAhB,aAEhB4J,EAAU,MAAVA,CAAmB,OACxByB,MACCkI,EAAiBxL,EAAO6B,EAAU,OAAVA,CAAoB,QAA3B7B,CAAjBwL,CAAwD,CADzDlI,IAGGpD,UAAYiC,OACZ9E,QAAQ2C,OAAS5B,OCSxB,OAAe,OASN,OAEE,GAFF,WAAA,MAAA,CATM,QAwDL,OAEC,GAFD,WAAA,MAAA,QAUE,CAVF,CAxDK,iBAsFI,OAER,GAFQ,WAAA,MAAA,yCAAA,SAmBN,CAnBM,mBAyBI,cAzBJ,CAtFJ,cA2HC,OAEL,GAFK,WAAA,MAAA,CA3HD,OA8IN,OAEE,GAFF,WAAA,MAAA,SAQI,WARJ,CA9IM,MAoKP,OAEG,GAFH,WAAA,MAAA,UAaM,MAbN,SAkBK,CAlBL,mBAyBe,UAzBf,CApKO,OAuMN,OAEE,GAFF,WAAA,MAAA,CAvMM,MA0NP,OAEG,GAFH,WAAA,MAAA,CA1NO,cAkPC,OAEL,GAFK,WAAA,MAAA,mBAAA,GAkBT,QAlBS,GAwBT,OAxBS,CAlPD,YA4RD,OAEH,GAFG,WAAA,MAAA,UAAA,uBAAA,CA5RC,CAAf,ICde,WAKF,QALE,iBAAA,iBAAA,mBAAA,UAgCH,UAAM,CAhCH,CAAA,UA0CH,UAAM,CA1CH,CAAA,aAAA,CDcf,CEpBqBqN,6BAS0B,YAAd/H,qEAAc,MAyF7CqC,eAAiB,iBAAM2F,uBAAsB,EAAKC,MAA3BD,CAzFsB,CAAA,MAEtCC,OAASC,EAAS,KAAKD,MAAL,CAAYE,IAAZ,CAAiB,IAAjB,CAATD,CAF6B,MAKtClI,aAAe+H,EAAOK,WALgB,MAQtCvI,MAAQ,eAAA,aAAA,iBAAA,CAR8B,MAetCD,UAAYA,GAAaA,EAAUyI,MAAvBzI,CAAgCA,EAAU,CAAVA,CAAhCA,EAf0B,MAgBtCtD,OAASA,GAAUA,EAAO+L,MAAjB/L,CAA0BA,EAAO,CAAPA,CAA1BA,EAhB6B,MAmBtC0D,QAAQZ,YAnB8B,QAoBpC3I,UACFsR,EAAOK,QAAPL,CAAgB3I,UAChBY,EAAQZ,YACVE,QAAQ,WAAQ,GACZU,QAAQZ,kBAEP2I,EAAOK,QAAPL,CAAgB3I,SAAhB2I,QAEA/H,EAAQZ,SAARY,CAAoBA,EAAQZ,SAARY,GAApBA,IARR,EApB2C,MAiCtCZ,UAAY5I,OAAOC,IAAPD,CAAY,KAAKwJ,OAAL,CAAaZ,SAAzB5I,EACdqG,GADcrG,CACV,8BAEA,EAAKwJ,OAAL,CAAaZ,SAAb,IAHU,CAAA5I,EAMduG,IANcvG,CAMT,oBAAU0G,GAAE5F,KAAF4F,CAAUF,EAAE1F,KANb,CAAAd,CAjC0B,MA6CtC4I,UAAUE,QAAQ,WAAmB,CACpCgJ,EAAgB7I,OAAhB6I,EAA2B5I,EAAW4I,EAAgBC,MAA3B7I,CADS,IAEtB6I,OACd,EAAK3I,UACL,EAAKtD,OACL,EAAK0D,UAEL,EAAKH,MAPX,EA7C2C,MA0DtCoI,QA1DsC,IA4DrC9F,GAAgB,KAAKnC,OAAL,CAAamC,cA5DQ,QA+DpCqG,sBA/DoC,MAkEtC3I,MAAMsC,0DAKJ,OACA8F,GAAOhT,IAAPgT,CAAY,IAAZA,mCAEC,OACDQ,GAAQxT,IAARwT,CAAa,IAAbA,gDAEc,OACdD,GAAqBvT,IAArBuT,CAA0B,IAA1BA,iDAEe,OACfjH,GAAsBtM,IAAtBsM,CAA2B,IAA3BA,UFtEX,CEpBqBwG,GAoHZW,KApHYX,CAoHJ,CAAmB,WAAlB,QAAOhU,OAAP,CAAyC4U,MAAzC,CAAgC5U,MAAjC,EAAkD6U,YApH9Cb,GAsHZjD,UAtHYiD,IAAAA,GAwHZK,QAxHYL"}
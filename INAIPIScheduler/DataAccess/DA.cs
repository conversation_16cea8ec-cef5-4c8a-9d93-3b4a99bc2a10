﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Web;

namespace INAIPIScheduler.DataAccess
{
    public class MainDA<T> : INAIPI.Core.BaseDA<T> where T : INAIPI.Core.BaseModel
    {
        private const string ProjectConecctionString = "SqlCon";
        public MainDA()
            : base(new INAIPI.DataAcces.DAQEC(ProjectConecctionString))
        {

        }
    }
}
﻿using INAIPI.Core;
using INAIPIScheduler.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace INAIPIScheduler.DataAccess
{
    public class SCH_SchedulerDA : MainDA<SCH_Tareas>
    {
        #region TAREAS
        public Resultado InsertarTarea(SCH_Tareas tareas)
        {
            using (SqlConnection adoCon = DA.Conectar())
            {
                using (SqlTransaction adoTrans = adoCon.BeginTransaction())
                {
                    List<SqlParameter> Parametros = new List<SqlParameter>
                    {
                        new SqlParameter { ParameterName = "@Accion", Value = "INSERTAR_TAREAS" }
                        ,new SqlParameter { ParameterName = "@Tarea", Value = tareas.Tarea }
                        ,new SqlParameter { ParameterName = "@Descripcion", Value = tareas.Descripcion }
                        ,new SqlParameter { ParameterName = "@EjecutarEnClase", Value = tareas.EjecutarEnClase }
                        ,new SqlParameter { ParameterName = "@EjecutarMetodo", Value = tareas.EjecutarMetodo }
                        ,new SqlParameter { ParameterName = "@Inactivo", Value = tareas.Inactivo }
                    };

                    DA.EjecutarQuery("sp_SCH_Tareas", CommandType.StoredProcedure, Parametros, adoCon, adoTrans);

                    if (DA.strError.Length > 0)
                    {
                        adoTrans.Rollback();
                        return new Resultado(false, DA.strError);
                    }

                    adoTrans.Commit();
                }

                adoCon.Close();
            }

            return new Resultado(true, "");
        }

        public List<SCH_Tareas> ListarTareas()
        {
            List<SqlParameter> Parametros = new List<SqlParameter>
            {
                new SqlParameter { ParameterName = "@Accion", Value = "LISTAR_TAREAS" }
            };

            try
            {
                return DA.EjecutarQuery<SCH_Tareas>("sp_SCH_Tareas", CommandType.StoredProcedure, Parametros).ToList();
            }
            catch (Exception)
            {
                return null;
            }
        }

        public List<SCH_Tareas> ListarTareasActivas()
        {
            List<SqlParameter> Parametros = new List<SqlParameter>
            {
                new SqlParameter { ParameterName = "@Accion", Value = "LISTAR_TAREAS_ACTIVAS" }
            };

            try
            {
                return DA.EjecutarQuery<SCH_Tareas>("sp_SCH_Tareas", CommandType.StoredProcedure, Parametros).ToList();
            }
            catch (Exception)
            {
                return null;
            }
        }

        public SCH_Tareas ConsultarTareasConProgramaciones(int IdTarea, bool ObtenerProgramaciones = false)
        {
            List<SqlParameter> Parametros = new List<SqlParameter>
            {
                new SqlParameter { ParameterName = "@Accion", Value = "CONSULTAR_TAREA" }
                ,new SqlParameter { ParameterName = "@IdTarea", Value = IdTarea }
            };

            try
            {
                SCH_Tareas Tarea = DA.EjecutarQuery<SCH_Tareas>("sp_SCH_Tareas", CommandType.StoredProcedure, Parametros).FirstOrDefault();

                if (ObtenerProgramaciones)
                {
                    Tarea.Programaciones = ListarProgramacionesXTarea(IdTarea);
                }

                return Tarea;
            }
            catch (Exception)
            {
                return null;
            }
        }
        #endregion

        #region PROGRAMACION
        public Resultado InsertarProgramacion(SCH_Programacion programacion)
        {
            using (SqlConnection adoCon = DA.Conectar())
            {
                using (SqlTransaction adoTrans = adoCon.BeginTransaction())
                {
                    List<SqlParameter> Parametros = new List<SqlParameter>
                    {
                        new SqlParameter { ParameterName = "@Accion", Value = "INSERTAR_PROGRAMACION" }
                        ,new SqlParameter { ParameterName = "@IdTarea", Value = programacion.IdTarea }
                        ,new SqlParameter { ParameterName = "@Frecuencia", Value = programacion.Frecuencia }
                        ,new SqlParameter { ParameterName = "@RepetirCada", Value = programacion.RepetirCada }
                        ,new SqlParameter { ParameterName = "@FechaInicio", Value = programacion.FechaInicio }
                        ,new SqlParameter { ParameterName = "@HoraEjecucion", Value = programacion.HoraEjecucion }
                        ,new SqlParameter { ParameterName = "@FechaUltimaEjecucion", Value = programacion.FechaUltimaEjecucion }
                        ,new SqlParameter { ParameterName = "@CantidaEjecucion", Value = programacion.CantidadEjecucion }
                        ,new SqlParameter { ParameterName = "@EjecutarAhora", Value = programacion.EjecutarAhora }
                        ,new SqlParameter { ParameterName = "@Inactivo", Value = programacion.Inactivo }
                        ,new SqlParameter { ParameterName = "@IntervaloEjecucion", Value = programacion.IntervaloEjecucion }
                    };

                    DA.EjecutarQuery("sp_SCH_Programacion", CommandType.StoredProcedure, Parametros, adoCon, adoTrans);

                    if (DA.strError.Length > 0)
                    {
                        adoTrans.Rollback();
                        return new Resultado(false, DA.strError);
                    }

                    adoTrans.Commit();
                }

                adoCon.Close();
            }

            return new Resultado(true, "");
        }

        public List<SCH_Programacion> ListarProgramacion()
        {
            List<SqlParameter> Parametros = new List<SqlParameter>
            {
                new SqlParameter { ParameterName = "@Accion", Value = "LISTAR_PROGRAMACION" }
            };

            try
            {
                return DA.EjecutarQuery<SCH_Programacion>("sp_SCH_Programacion", CommandType.StoredProcedure, Parametros).ToList();
            }
            catch (Exception)
            {
                return null;
            }
        }

        public List<SCH_Programacion> ListarProgramacionesActivas()
        {
            List<SqlParameter> Parametros = new List<SqlParameter>
            {
                new SqlParameter { ParameterName = "@Accion", Value = "LISTAR_PROGRAMACIONES_ACTIVAS" }
            };

            try
            {
                return DA.EjecutarQuery<SCH_Programacion>("sp_SCH_Programacion", CommandType.StoredProcedure, Parametros).ToList();
            }
            catch (Exception)
            {
                return null;
            }
        }

        public List<SCH_Programacion> ListarProgramacionesXTarea(int IdTarea)
        {
            List<SqlParameter> Parametros = new List<SqlParameter>
            {
                new SqlParameter { ParameterName = "@Accion", Value = "LISTAR_PROGRAMACION_X_TAREA" }
                ,new SqlParameter { ParameterName = "@IdTarea", Value = IdTarea }
            };

            try
            {
                return DA.EjecutarQuery<SCH_Programacion>("sp_SCH_Programacion", CommandType.StoredProcedure, Parametros).ToList();
            }
            catch (Exception)
            {
                return null;
            }
        }

        public Resultado ActualizarFechaUltimaEjecucion(long idProgramacion)
        {
            using (SqlConnection adoConect = DA.Conectar())
            {
                using (SqlTransaction adoTrans = adoConect.BeginTransaction())
                {
                    List<SqlParameter> Parametros = new List<SqlParameter>
                    {
                        new SqlParameter { ParameterName = "@Accion", Value = "ACTUALIZAR_FEC_ULTIMA_EJECUCION" }
                        ,new SqlParameter { ParameterName = "@IdProgramacion", Value = idProgramacion }
                    };

                    DA.EjecutarQuery("sp_SCH_Programacion", CommandType.StoredProcedure, Parametros, adoConect, adoTrans);
                    if (DA.strError.Length > 0)
                    {
                        adoTrans.Rollback();
                        return new Resultado(false, DA.strError);
                    }

                    adoTrans.Commit();
                }

                adoConect.Close();
            }

            return new Resultado(true, "");
        }

        public Resultado ActualizarEjecutarAhora(long idProgramacion, bool ejecutarahora)
        {
            using (SqlConnection adoConect = DA.Conectar())
            {
                using (SqlTransaction adoTrans = adoConect.BeginTransaction())
                {
                    List<SqlParameter> Parametros = new List<SqlParameter>
                    {
                        new SqlParameter { ParameterName = "@Accion", Value = "EJECUTAR_AHORA_A_FALSE" }
                        ,new SqlParameter { ParameterName = "@IdProgramacion", Value = idProgramacion }
                        ,new SqlParameter { ParameterName = "@EjecutarAhora", Value = ejecutarahora }
                    };

                    DA.EjecutarQuery("sp_SCH_Programacion", CommandType.StoredProcedure, Parametros, adoConect, adoTrans);
                    if (DA.strError.Length > 0)
                    {
                        adoTrans.Rollback();
                        return new Resultado(false, DA.strError);
                    }

                    adoTrans.Commit();
                }

                adoConect.Close();
            }

            return new Resultado(true, "");
        }
        #endregion

        #region PROGRAMACION LOG
        public Resultado InsertarEstatusProgramacion(SCH_ProgramacionLog log)
        {
            Resultado result = new Resultado();

            using (SqlConnection adoConect = DA.Conectar())
            {
                using (SqlTransaction adoTrans = adoConect.BeginTransaction())
                {
                    List<SqlParameter> Parametros = new List<SqlParameter>
                    {
                        new SqlParameter { ParameterName = "@Accion", Value = "INSERTAR_ESTATUS_PROGRAMACIONLOG" }
                        ,new SqlParameter { ParameterName = "@IdProgramacion", Value = log.IdProgramacion }
                        ,new SqlParameter { ParameterName = "@IdEstatus", Value = log.IdEstatus }
                        ,new SqlParameter { ParameterName = "@FueEjecucionAhora", Value = log.FueEjecucionAhora }
                        ,new SqlParameter { ParameterName = "@MensajeError", Value = log.MensajeError }
                    };

                    DataTable dt = DA.EjecutarQuery("sp_SCH_ProgramacionLog", CommandType.StoredProcedure, Parametros, adoConect, adoTrans);
                    if (DA.strError.Length > 0)
                    {
                        adoTrans.Rollback();
                        result.TodoBien = false;
                        result.strError = DA.strError;
                        return result;
                    }
                    else
                    {
                        if (dt.Rows.Count > 0)
                        {
                            long.TryParse(dt.Rows[0]["IdProgramacionLog"].ToString(), out long idProgramacionLog);
                            result.TodoBien = true;
                            result.strError = "";
                            result.ID = idProgramacionLog;
                        }
                    }

                    adoTrans.Commit();
                }

                adoConect.Close();
            }

            return result;
        }

        public Resultado CambiarEstatusLog(SCH_ProgramacionLog log, long idProgramacionLog)
        {
            using (SqlConnection adoConect = DA.Conectar())
            {
                using (SqlTransaction adoTrans = adoConect.BeginTransaction())
                {
                    List<SqlParameter> Parametros = new List<SqlParameter>
                    {
                        new SqlParameter { ParameterName = "@Accion", Value = "CAMBIAR_ESTATUS_LOG" }
                        ,new SqlParameter { ParameterName = "@IdEstatus", Value = log.IdEstatus }
                        ,new SqlParameter { ParameterName = "@MensajeError", Value = log.MensajeError }
                        ,new SqlParameter { ParameterName = "@IdProgramacionLog", Value = idProgramacionLog }
                    };

                    DA.EjecutarQuery("sp_SCH_ProgramacionLog", CommandType.StoredProcedure, Parametros, adoConect, adoTrans);
                    if (DA.strError.Length > 0)
                    {
                        adoTrans.Rollback();
                        return new Resultado(false, DA.strError);
                    }

                    adoTrans.Commit();
                }

                adoConect.Close();
            }

            return new Resultado(true, "");
        }
        #endregion
    }
}
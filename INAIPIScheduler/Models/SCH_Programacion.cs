﻿using INAIPI.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Timers;

namespace INAIPIScheduler.Models
{
    [Serializable]
    public class SCH_Programacion : BaseModel
    {
        INAIPIScheduler.DataAccess.SCH_SchedulerDA da = new DataAccess.SCH_SchedulerDA();

        public SCH_Programacion()
        {
            IntervaloEjecucion = 1000;
            timer.Elapsed += Timer_Elapsed;
        }

        private void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            //if (EjecutarAhora)
            //{
            //    IniciarEjecutarAhora();
            //}
            //else
            //{
            switch (Frecuencia)
            {
                case enumFrecuencia.Una_Vez:
                    EjecutarUnaVez();
                    break;
                case enumFrecuencia.Diariamente:
                    EjecutarDiariamente();
                    break;
                case enumFrecuencia.Semanalmente:
                    EjecutarSemanalmente();
                    break;
                case enumFrecuencia.Mensualmente:
                    EjecutarMensualmente();
                    break;
                default:
                    break;
            }
            //}
        }

        private void IniciarEjecutarAhora()
        {
            OnEjecucionIniciada(new ResultadoEventArgs<SCH_Programacion> { Resultado = new Resultado<SCH_Programacion>(true, Objeto: this) });
            EjecutarAhora = false;
            da.ActualizarEjecutarAhora(IdProgramacion, EjecutarAhora);
        }

        private void EjecutarMensualmente()
        {
            throw new NotImplementedException();
        }

        private void EjecutarSemanalmente()
        {
            throw new NotImplementedException();
        }

        private void EjecutarUnaVez()
        {
            throw new NotImplementedException();
        }

        private void EjecutarDiariamente()
        {
            DateTime hoy = DateTime.Now;

            if (FechaUltimaEjecucion?.Date != hoy.Date && HoraEjecucion <= hoy.TimeOfDay)
            {
                FechaUltimaEjecucion = hoy;
                OnEjecucionIniciada(new ResultadoEventArgs<SCH_Programacion> { Resultado = new Resultado<SCH_Programacion>(true, Objeto: this) });
            }
        }

        Timer timer = new Timer(1000);

        #region Evento
        public event ResultadoEventHandler<SCH_Programacion> EjecucionIniciada;
        protected virtual void OnEjecucionIniciada(ResultadoEventArgs<SCH_Programacion> e)
        {
            if (EjecucionIniciada != null)
                EjecucionIniciada(this, e);
        }
        #endregion

        public void IniciarTimer()
        {
            if (EjecutarAhora)
            {
                IniciarEjecutarAhora();
            }

            timer.Interval = IntervaloEjecucion;
            timer.Start();
        }

        public void DetenerTimer()
        {
            timer.Stop();
        }

        public long IdProgramacion { get; set; }
        public int IdTarea { get; set; }
        public enum enumFrecuencia : byte { Una_Vez = 1, Diariamente, Semanalmente, Mensualmente }
        public enumFrecuencia Frecuencia { get; set; }
        public byte RepetirCada { get; set; }
        public DateTime FechaInicio { get; set; }
        public TimeSpan HoraEjecucion { get; set; }
        public DateTime? FechaUltimaEjecucion { get; set; }
        public int CantidadEjecucion { get; set; }
        public bool EjecutarAhora { get; set; }
        public bool Inactivo { get; set; }
        public double IntervaloEjecucion { get; set; }
    }

    [Serializable]
    public class SCH_ProgramacionLog : SCH_Programacion
    {
        public long IdProgramacionLog { get; set; }
        //public int IdProgramacion { get; set; }
        public DateTime InicioEjecucion { get; set; }
        public DateTime? FinEjecucion { get; set; }
        public byte IdEstatus { get; set; }
        public string MensajeError { get; set; }
        public bool FueEjecucionAhora { get; set; }
    }
}
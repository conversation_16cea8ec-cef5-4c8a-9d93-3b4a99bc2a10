﻿using INAIPI.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Reflection;
using SI_INAIPI;
using System.Drawing;

namespace INAIPIScheduler.Models
{
    [Serializable]
    public class SCH_Tareas : BaseModel
    {
        DataAccess.SCH_SchedulerDA da = new DataAccess.SCH_SchedulerDA();

        /*------------------ PROPIEDADES -------------------*/
        public SCH_Tareas()
        {
            Programaciones = new List<SCH_Programacion>();
        }
        public int IdTarea { get; set; }
        public string Tarea { get; set; }
        public string Descripcion { get; set; }
        public DateTime FechaRegistro { get; set; }
        public string EjecutarEnClase { get; set; }
        public string EjecutarMetodo { get; set; }
        public bool Inactivo { get; set; }
        public List<SCH_Programacion> Programaciones { get; set; }
        public object clase;


        /*------------------ MÉTODOS ------------------*/
        public void Iniciar()
        {
            Programaciones.ForEach(p => { p.EjecucionIniciada += Ejecutar; p.IniciarTimer(); });
        }

        /*------------------ MÉTODOS ------------------*/
        public void Detener()
        {
            Programaciones?.ForEach(p => { p.EjecucionIniciada += Ejecutar; p.DetenerTimer(); });
        }

        private void Ejecutar(object sender, ResultadoEventArgs<SCH_Programacion> e)
        {
            Resultado result = new Resultado();
            SCH_ProgramacionLog log = new SCH_ProgramacionLog();

            /*-------------------- INSERTANDO EN EL LOG DE PROGRAMACIONES --------------------*/
            log.IdEstatus = 1;
            log.FueEjecucionAhora = e.Resultado.Objeto.EjecutarAhora;
            log.IdProgramacion = e.Resultado.Objeto.IdProgramacion;
            log.MensajeError = "";

            if (e.Resultado.TodoBien)
            {
                result = da.InsertarEstatusProgramacion(log);
            }

            /*-------------------- MODIFICANDO LA FECHA DE ULTIMA EJECUCION EN EL LOG --------------------*/
            if (e.Resultado.TodoBien)
            {
                da.ActualizarFechaUltimaEjecucion(e.Resultado.Objeto.IdProgramacion);
            }

            try
            {
                Resultado r = (Resultado)clase.GetType().GetMethod(EjecutarMetodo).Invoke(clase, new object[] { });
                if (r.TodoBien)
                {
                    /*-------------------- ACTUALIZANDO EL LOG SI LA TAREA SE EJECUTÓ CORRECTAMENTE --------------------*/
                    log.IdEstatus = 2;
                    log.MensajeError = r.strError;
                    long.TryParse(result.ID.ToString(), out long idProgramacionLog);

                    da.CambiarEstatusLog(log, idProgramacionLog);
                    r.LogWindows(Origen: "INAIPIScheduler.Models.SCH_Tareas.Ejecutar()");
                }
                else
                {
                    /*-------------------- ACTUALIZANDO EL LOG SI EL PROCESO ESTUVO BIEN PERO NO SE PUDO INSERTAR EN EL LOG --------------------*/
                    log.IdEstatus = 3;
                    log.MensajeError = r.strError;
                    long.TryParse(result.ID.ToString(), out long idProgramacionLog);

                    da.CambiarEstatusLog(log, idProgramacionLog);
                }
            }
            catch (Exception ex)
            {
                /*-------------------- ACTUALIZANDO EL LOG Y GUARDANDO EL MENSAJE DE LA EXCEPCIÓN SI EL PROCESO NO SE PUDO COMPLETAR --------------------*/
                log.IdEstatus = 3;
                log.MensajeError = ex.Message;
                long.TryParse(result.ID.ToString(), out long idProgramacionLog);

                da.CambiarEstatusLog(log, idProgramacionLog);
            }
        }
    }
}
﻿using INAIPI.Core;
using INAIPIScheduler.DataAccess;
using INAIPIScheduler.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Web.Http;
using System.Diagnostics;

namespace INAIPIScheduler.Controllers
{
    public class SCH_TareasController : ApiController
    {
        SCH_SchedulerDA da = new SCH_SchedulerDA();
        SCH_ProgramacionController ctrlProg = new SCH_ProgramacionController();

        List<SCH_Tareas> Tareas;

        public Resultado InsertarTarea(SCH_Tareas tareas)
        {
            return da.InsertarTarea(tareas);
        }

        public List<SCH_Tareas> ListarTareas()
        {
            return da.ListarTareas();
        }

        public List<SCH_Tareas> ListarTareasActivas()
        {
            return da.ListarTareasActivas();
        }

        public List<SCH_Tareas> CrearTareasParaEjecutar()
        {
            List<SCH_Tareas> Tareas = ListarTareasActivas();

            List<SCH_Programacion> Programaciones = ctrlProg.ListarProgramacionesActivas();

            if (Tareas != null)
            {
                Tareas.ForEach(tarea => tarea.Programaciones = Programaciones.Where(programacion => programacion.IdTarea == tarea.IdTarea).ToList());
            }

            return Tareas;
        }

        [HttpGet]
        public Resultado IniciarTareas()
        {
            string logSource = "SI_INAIPI";
            string logLog = "INAIPIScheduler.Controllers.SCH_TareasController.IniciarTareas()";

            if (!EventLog.SourceExists(logSource))
                EventLog.CreateEventSource(logSource, logLog);

            try
            {
                if (Tareas != null && Tareas.Count > 0)
                {
                    Tareas.ForEach(tarea => { tarea.Detener(); });
                }

                Tareas = null;

                Tareas = CrearTareasParaEjecutar();

                Tareas.ForEach((Tarea) =>
                {
                    try
                    {
                        Type Tipo = Type.GetType(Tarea.EjecutarEnClase + ", " + Tarea.EjecutarEnClase.Split('.')[0], true);
                        Tarea.clase = Activator.CreateInstance(Tipo);

                        Tarea.Iniciar();
                        EventLog.WriteEntry(logSource, "LA TAREA " + Tarea.Tarea + " [" + Tarea.IdTarea.ToString() + "] SE INICIO CORRECTAMENTE.", EventLogEntryType.SuccessAudit);
                    }
                    catch (Exception ex)
                    {
                        EventLog.WriteEntry(logSource, "ERROR INICIALIZANDO LA TAREA." + Tarea.Tarea + " [" + Tarea.IdTarea.ToString() + "] ERROR: " + ex.Message, EventLogEntryType.Error);
                        throw ex;
                    }
                });

                return new Resultado(true, "TAREAS INICIADAS EXITOSAMENTE.");
            }
            catch (Exception ex)
            {
                EventLog.WriteEntry(logSource, "ERROR INICIALIZANDO TAREA. ERROR: " + ex.Message, EventLogEntryType.Error);
                return new Resultado(false, ex.Message);
            }
        }
    }
}

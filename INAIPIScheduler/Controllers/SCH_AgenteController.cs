﻿using INAIPIScheduler.DataAccess;
using INAIPIScheduler.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Web.Http;

namespace INAIPIScheduler.Controllers
{
    public class SCH_AgenteController : ApiController
    {
        SCH_SchedulerDA da = new SCH_SchedulerDA();

        private DateTime fechaInicio;
        private int DiasDiferencia = 0;

        [HttpPost]
        public void IniciarTareas()
        {
            

        }

        public List<SCH_Tareas> Agente()
        {
            List<SCH_Tareas> tareas = da.ListarTareas();
            List<int> idsTareas = new List<int>();
            SCH_Tareas tarea = new SCH_Tareas();

            foreach (SCH_Tareas item in tareas)
            {
                int idTarea = item.IdTarea;

                idsTareas.Add(idTarea);
            }

            tareas.Clear();
            foreach (int idTarea in idsTareas)
            {
                tarea = da.ConsultarTareasConProgramaciones(idTarea, true);

                tareas.Add(tarea);
            }

            return tareas;
        }

        #region PROGRAMACION
        SCH_ProgramacionController ProgramacionCtrl = new SCH_ProgramacionController();
        #endregion

        #region TAREAS
        SCH_TareasController TareasCtrl = new SCH_TareasController();
        #endregion
    }
}

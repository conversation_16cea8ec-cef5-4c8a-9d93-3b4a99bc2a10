﻿using INAIPI.Core;
using INAIPIScheduler.DataAccess;
using INAIPIScheduler.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;

namespace INAIPIScheduler.Controllers
{
    public class SCH_ProgramacionController : ApiController
    {
        SCH_SchedulerDA da = new SCH_SchedulerDA();

        public Resultado InsertarProgramacion(SCH_Programacion programacion)
        {
            return da.InsertarProgramacion(programacion);
        }

        public List<SCH_Programacion> ListarProgramacion()
        {
            return da.ListarProgramacion();
        }

        public List<SCH_Programacion> ListarProgramacionesActivas()
        {
            return da.ListarProgramacionesActivas();
        }

        public List<SCH_Programacion> ListarProgramacionesXTarea(int IdTarea)
        {
            return da.ListarProgramacionesXTarea(IdTarea);
        }
    }
}

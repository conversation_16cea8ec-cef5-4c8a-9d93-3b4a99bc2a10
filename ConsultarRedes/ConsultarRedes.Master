﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="ConsultarRedes.master.cs" Inherits="ConsultarRedes.ConsultarRedes1" %>

<!DOCTYPE html>

<html>
<head runat="server">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
       
    <style>
        * {
            font-size: 12px;
            text-transform: uppercase; 
        }

        /*::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
            background-color: #CCC;
        }

        ::-webkit-scrollbar-thumb {
            background-color: #FFF;
            border: 1px solid #999;
            box-shadow: 0px 0px 2px #999;
            background-color: #EBEBEB;
        }*/


        .main {
            position: absolute;
            left: 0;
            right: 0;
            top: 0px;
            bottom: 0;
            border-top: 1px solid #ccc;
        }

        .left {
            position: absolute;
            left: 0;
            width: 250px;
            bottom: 0;
            top: 0;
            border-right: 1px solid #ccc;
            overflow: hidden;
            /*background-color:#0070cd;*/
            display:none;
        }

        .left-top {
            position: absolute;
            left: 0;
            top: 0;
            right: 5px;
            line-height: 40px;
            padding: 5px;
            border-right: 1px solid #ccc;
        }

        .left-content {
            position: absolute;
            left: 0;
            top: 35px;
            right: 0;
            bottom: 0;
            padding: 5px;
            overflow: hidden;
        }

        .right {
            position: absolute;
            left: 0px;
            bottom: 0;
            top: 0;
            border-left: 1px solid #ccc;
            right: 0;
        }

        .right-top {
            position: absolute;
            left: 0;
            top: 0;
            border-bottom: 1px solid #ccc;
            right: 0;
            line-height: 40px;
        }

        .right-content {
            position: absolute;
            left: 0;
            top: 40px;
            right: 0;
            bottom: 0;
            padding: 5px;
            overflow: hidden;
        }

        .right-content-content {
            position: absolute;
            left: 0;
            top: 0px;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        h3 {
            margin: 0;
            padding: 0;
            line-height: 40px;
            margin-left: 10px;
        }

        .data-grid {
            border-top: 1px solid #999;
        }

            .data-grid td {
                border-bottom: 1px solid #ccc;
                border-right: 1px solid #ccc;
                /*white-space: nowrap;*/
                padding: 2px;
                padding-left: 5px;
                padding-right: 10px;
            }

            /*.data-grid th {
                border-bottom: 1px solid #999;
                border-right: 1px solid #999;
                white-space: nowrap;
                padding: 4px;
                padding-left: 5px;
                padding-right: 10px;
                background-color: #aaaaaa;
                text-transform: uppercase;
            }*/

            /*.data-grid tr:nth-child(odd) {
                background: #e6e6e6;
            }*/

        /*.dxgvHeader_MetropolisBlue {
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;
            text-align: center;
            height: 25px;
            background: linear-gradient(#E4E4E4,#ccc);
            font-size: 12px;
            font-weight: normal;
        }*/

        /*#ContentPlaceHolder1_grdListadoExpedientes_DXMainTable tbody tr:nth-child(odd) {
            background: #fff;
        }*/
        /*tr:nth-child(even) {
            background: #E4E4E4;
        }*/
        /*.dropdown-menu{
            margin-top:-40px;
            border:1px solid #ccc;
        }*/
        /*.dropdown-menu li{
            border-bottom:1px solid #ccc;
          line-height:25px;
        }
        .dropdown-menu li a{
            padding:0; margin:0; text-decoration:none;
            display:block;
            padding-left:5px;
        }
        .dropdown-menu li a:hover{
            background-color:#0070cd; color:#fff;
        }*/
        /*#ContentPlaceHolder1_cbPrincipal_grdRedes_DXSearchPanel {
            background-color: #ccc;
        }

            #ContentPlaceHolder1_cbPrincipal_grdRedes_DXSearchPanel input {
                padding: 3px;
                width: 195px;
            }

        #ContentPlaceHolder1_cbPrincipal_grdFormularios_DXSearchPanel {
            background-color: #ccc;
        }

            #ContentPlaceHolder1_cbPrincipal_grdFormularios_DXSearchPanel input {
                padding: 3px;
                width: 195px;
            }*/
        /*#ContentPlaceHolder1_cbPrincipal_grdRedes_DXSearchPanel{
            width:100%;
        }
        #ContentPlaceHolder1_cbPrincipal_grdRedes_DXSearchPanel input{
            width:195px;
        }
        #ContentPlaceHolder1_cbPrincipal_grdRedes_DXSearchPanel{
            background-color:#ccc;
        }
        #ContentPlaceHolder1_cbPrincipal_grdFormularios_DXSearchPanel input{
           padding:3px;
        }
        #ContentPlaceHolder1_cbPrincipal_grdFormularios_DXSearchPanel{
           background-color:#ccc;
        }
        #ContentPlaceHolder1_cbPrincipal_grdFormularios_DXSearchPanel input{
           padding:3px;
           width:195px;
        }*/
        /*#ContentPlaceHolder1_cbPrincipal_grdFormularios {
            margin-left: -1px;
        }*/
        /*.ScreenHeight{
           
            height:700px;
        }
        #ContentPlaceHolder1_cbPrincipal_grdFormularios_DXSearchPanel{
            position:absolute;
            top:0; left:0; right:0;
        }
        #ContentPlaceHolder1_cbPrincipal_grdFormularios_DXHeaderTable{
           
        }*/
        /*#ContentPlaceHolder1_cbPrincipal_grdFormularios_DXHeadersRow0 td {
            text-transform: uppercase;
        }


        @media only screen and (max-width: 500px) {
            .left {
             
               width:auto;
               right:0;
            }
            .right{
                visibility:hidden;
            }
        }*/
        #header-principal {
    position: absolute;
    left: 0;
    right: 0;
    

    
}
        .logo {
    width: 200px;
    height: 75px;
    background-image: url('http://sgc.inaipi.gob.do/Images/logo-inaipi-alt.png');
    background-size: cover;
    /*border:1px solid #ccc;*/
    position: absolute;
}

.header-principal-titulo {
    width: 400px;
    height: 75px;
    /*border:1px solid #ccc;*/
    margin-left: 200px;
    position: absolute;
}

    .header-principal-titulo h1 {
        font-family: 'Segoe UI', Helvetica, sans-serif;
        font-weight: normal;
        font-size: 26px;
        font-variant: small-caps;
        color: #0081c2;
        line-height: 22px;
        padding-left: 10px;
        margin: 0;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .header-principal-titulo h2 {
        font-family: 'Segoe UI', Helvetica, sans-serif;
        font-weight: 200;
        font-size: 22px;
        font-variant: small-caps;
        color: #ff9f35;
        line-height: 22px;
        padding-left: 10px;
        margin: 0;
        padding: 0;
        outline: 0;
        font-weight: inherit;
        font-style: inherit;
        font-size: 100%;
        font-family: inherit;
        vertical-align: baseline;
        display: block;
        margin-left: 10px;
        /* text-shadow:0.5px 0.5px #000;*/
    }
    </style>

    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <form id="form1" runat="server">
         <div id="header-principal">
                        <div class="logo"></div>
                        <div class="header-principal-titulo">
                            <h1>SISTEMA DE INFORMACION</h1>
                            <h2>CONSULTA DE REDES</h2>
                        </div>
                        <div id="area-user">
                            <div id="area-user-info">
                                <div id="imgUsuario"></div>
                                
                                <div id="InfoDatosUsuario">
                                    <h3 style="margin-top: -20px" runat="server" id="LoginUser"></h3>
                                    <h4 runat="server" id="LoginDepartamento"></h4>
                                </div>
                            </div>
                            
                            <!--<i class="material-icons" style="font-size:px;color:red">error</i>-->
                            
                        </div>
                    </div>
        <div style="position: absolute; left: 0; right: 0; top: 80px; bottom: 0;">
            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
            </asp:ContentPlaceHolder>
        </div>
    </form>
</body>
</html>

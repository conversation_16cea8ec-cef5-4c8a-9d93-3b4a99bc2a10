﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <sectionGroup name="devExpress">
      <section name="themes" type="DevExpress.Web.ThemesConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="compression" type="DevExpress.Web.CompressionConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="settings" type="DevExpress.Web.SettingsConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="errors" type="DevExpress.Web.ErrorsConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="resources" type="DevExpress.Web.ResourcesConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="bootstrap" type="DevExpress.Web.Bootstrap.BootstrapConfigurationSection, DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <system.web>
    <globalization fileEncoding="utf-8" requestEncoding="utf-8" responseEncoding="utf-8" culture="es-DO" uiCulture="es-DO" />
    <customErrors mode="Off" />
    <authentication mode="Forms">
      <forms name="ryry" slidingExpiration="true" defaultUrl="~/Views/RRHH/SGH/Default.aspx" timeout="1" loginUrl="~/Views/RRHH/SGH/LogIn.aspx" />
    </authentication>
    <!--<authorization>
      <deny users="?" />
    </authorization>-->
    <!--<httpHandlers>
      <add name="AjaxFileUploadHandler"
           verb="*"
           path="AjaxFileUploadHandler.axd"
           type="AjaxControlToolkit.AjaxFileUploadHandler, AjaxControlToolkit"/>
    </httpHandlers>-->
    <pages enableEventValidation="false">
      <controls>
        <!--<add tagPrefix="ajax" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>-->
        <add tagPrefix="ajaxToolkit" assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" />
        <add tagPrefix="dx" namespace="DevExpress.Web" assembly="DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add tagPrefix="dx" namespace="DevExpress.Web.ASPxPivotGrid" assembly="DevExpress.Web.ASPxPivotGrid.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add tagPrefix="dx" namespace="DevExpress.Web.Bootstrap" assembly="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
      </controls>
    </pages>
    <compilation debug="true" targetFramework="4.5">
      <assemblies>
        <add assembly="DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.Data.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.Printing.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="System.Design, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="DevExpress.RichEdit.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="System.Data.Linq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="DevExpress.Web.ASPxThemes.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Web.ASPxPivotGrid.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.PivotGrid.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.XtraReports.v18.1.Web, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.XtraReports.v18.1.Web.WebForms, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.XtraReports.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.XtraCharts.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.Charts.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.Sparkline.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.XtraGauges.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.Web.Resources.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.Web.ASPxHtmlEditor.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
        <add assembly="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
      </assemblies>
    </compilation>
    <httpRuntime targetFramework="4.5" maxRequestLength="5000000" executionTimeout="180" />
    <httpModules>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
    </httpModules>
    <httpHandlers>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" validate="false" />
      <add verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    </httpHandlers>
    <sessionState mode="StateServer" stateConnectionString="tcpip=localhost:42424" />
  </system.web>
  <appSettings>
    <add key="ValidationSettings:UnobtrusiveValidationMode" value="none" />
    <add key="MainImgPath" value="~/Img" />
    <add key="NominaPath" value="~/Files/Nomina" />
    <add key="ImgPath" value="~/Files/Img" />
    <add key="ReclPath" value="~/Files/ImgRecl" />
    <add key="AvatarPath" value="~/Files/Avatar" />
    <add key="ReportePath" value="~/Reportes" />
    <add key="ImportPath" value="~/Files/Imports" />
    <add key="IdSeccionRL" value="**********" />
    <add key="MenuPrincipalUrl" value="http://sgc.inaipi.gob.do" />
    <!--<add key="MenuPrincipalUrl" value="http://localhost:7549" />-->
  </appSettings>
  <connectionStrings>
    <!--DB_PRUBA-->
    <!--<add name="MySqlConn" connectionString="server=inaipi.gob.do;User ID=inaipigob;Password=**********;Database=inaipigo_qec;default command timeout=300;" providerName="MySql.Data.MysqlClient"/>-->
    <!--<add name="SqlCon" connectionString="Data Source=SERTEST001;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=600;Max Pool Size=3000;" providerName="System.Data.SqlClient" />
    <add name="SqlConGESHUM" connectionString="Data Source=SERTEST001;Initial Catalog=GESHUM;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=0;Max Pool Size=3000;" providerName="System.Data.SqlClient" />-->
    <!--DB_PRODUCCION-->
    <add name="SqlConGESHUM" connectionString="Data Source=SERDB001;Initial Catalog=GESHUM;User ID=sa;Password=************;Connection Timeout=0;Max Pool Size=3000;" providerName="System.Data.SqlClient" />
    <add name="SqlCon" connectionString="Data Source=SERDB001;Initial Catalog=QEC;   User ID=sa;Password=************;Connection Timeout=600;Max Pool Size=3000;" providerName="System.Data.SqlClient" />
    <add name="Excel03ConString" connectionString="Provider=Microsoft.Jet.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES'" />
    <add name="Excel07ConString" connectionString="Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES'" />
    <!---->
  </connectionStrings>
  <location allowOverride="true">
    <system.webServer>
      <modules>
        <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
      </modules>
      <security>
        <requestFiltering>
          <requestLimits maxAllowedContentLength="500000000" />
        </requestFiltering>
      </security>
      <validation validateIntegratedModeConfiguration="false" />
      <handlers>
        <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" name="ASPxHttpHandlerModule" preCondition="integratedMode" />
        <add name="ASPxUploadProgressHandler" preCondition="integratedMode" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
      </handlers>
    </system.webServer>
  </location>
  <devExpress>
    <themes enableThemesAssembly="true" styleSheetTheme="" theme="" customThemeAssemblies="" baseColor="" font="" />
    <compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true" />
    <settings doctypeMode="Xhtml" rightToLeft="false" embedRequiredClientLibraries="false" ieCompatibilityVersion="edge" accessibilityCompliant="false" checkReferencesToExternalScripts="false" protectControlState="true" bootstrapMode="Bootstrap3" />
    <!--bootstrapMode="Bootstrap3"-->
    <errors callbackErrorRedirectUrl="" />
    <resources>
      <add type="ThirdParty" />
      <add type="DevExtreme" />
    </resources>
    <bootstrap mode="Bootstrap4" iconSet="Embedded" />
  </devExpress>
  <!--<system.webServer>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer>-->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AI.DependencyCollector" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.11.2.0" newVersion="2.11.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
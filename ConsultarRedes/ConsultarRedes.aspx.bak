﻿<%@ Page Title="" Language="C#" MasterPageFile="~/ConsultarRedes.Master" AutoEventWireup="true" CodeBehind="ConsultarRedes.aspx.cs" Inherits="ConsultarRedes.ConsultarRedes" %>

<%@ Register Assembly="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <script>
        //$(document).ready(function(){
        //    $("p").click(function(){
        //        $(this).hide();
        //    });
        //});
        function Responsivo() {
            if (screen.width < 500) {
                $(".left").hide();
                $(".right").show();
                $(".right").css('left', '0');
                $(".right").css('right', '0');
                $(".right").css('visibility', 'visible');
                $(".right").css('width', 'auto');
            }

        }
        function ExecuteSelectors() {

        }

        function OnInit(s, e) {

            ExecuteSelectors();
        }
        function OnEndCallback(s, e) {
            ////$("#PreviewDocumento").show();
            //$(".right").css('width', '100%');
            //$(".left").css('visibility', 'hidden');
            //$(".right").css('left', '0');
            //$(".right").css('right', '0');
            //$(".right").css('visibility', 'visible');
            //$("table").css('width', '100%');
            //$(".dxgvHSDC").css('width', '100%');
            //$("div").css('width', 'auto');
            //ExecuteSelectors();
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <dx:ASPxCallbackPanel ID="cbPrincipal" runat="server" ClientInstanceName="cbPrincipal" OnCallback="cbPrincipal_Callback">

        <ClientSideEvents Init="OnInit" EndCallback="OnEndCallback" />
        <PanelCollection>
            <dx:PanelContent>


                <div class="main">
                    <div class="left">
                        <div class="left-top">
                            <h3>Redes</h3>
                        </div>
                        <div class="left-content">

                            <dx:ASPxGridView ID="grdRedes" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" KeyFieldName="IdRed" HorizontalScrollBarMode="Auto" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">
                                <%--<ClientSideEvents RowDblClick="OpenRegistro()" />--%>
                                <ClientSideEvents RowClick="function(s, e) { cbPrincipal.PerformCallback(&#39;ABRIR-RED|&#39;+ s.GetRowKey(e.visibleIndex));Responsivo()}"></ClientSideEvents>

                                <SettingsPager Mode="ShowAllRecords" PageSize="15"></SettingsPager>
                                <Settings ShowFooter="true" VerticalScrollBarMode="Auto" VerticalScrollableHeight="500" VerticalScrollBarStyle="VirtualSmooth" />
                                <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                <SettingsSearchPanel AllowTextInputTimer="false" Visible="True"></SettingsSearchPanel>

                                <Columns>
                                    <dx:GridViewDataSpinEditColumn Name="gcIdRed" Caption="#" FieldName="IdRed" Width="40px" VisibleIndex="0" CellStyle-HorizontalAlign="Center">
                                    </dx:GridViewDataSpinEditColumn>
                                    <dx:GridViewDataTextColumn CellStyle-HorizontalAlign="Left" FieldName="Red" Name="gcRed" Caption="Red" VisibleIndex="1">
                                    </dx:GridViewDataTextColumn>

                                </Columns>

                            </dx:ASPxGridView>

                        </div>
                    </div>
                    <div class="right">
                        <div class="row">
                        <div class="col-8" style="padding-top:8px; padding-left:30px">
                            <%--<h3 id="TituloRed" style="text-transform: none;" runat="server">--%>
                            <%--<div style="position:absolute; left:5px; top:0; right:200px">--%>

                            <%--<dx:BootstrapComboBox Width="50%" EnableViewState="false" ViewStateMode="Disabled" ID="cbBuscarRed" runat="server" ValueField="IdRed" TextFormatString="{0} | {1}" DisplayFormatString="{0} | {1}">
                                <Fields>
                                    <dx:BootstrapListBoxField FieldName="Red"></dx:BootstrapListBoxField>
                                    <dx:BootstrapListBoxField FieldName="IdRed"></dx:BootstrapListBoxField>
                                </Fields>

                                <ClientSideEvents ValueChanged="function(s, e) {	cbPrincipal.PerformCallback('ABRIR-RED|');}" />
                            </dx:BootstrapComboBox>--%>

                            <dx:ASPxComboBox ID="cboRedes" ClientInstanceName="cboRedes" Caption="Red:" Width="100%" HelpText="Seleccione la red." HelpTextSettings-DisplayMode="Popup" AutoPostBack="false" EnableTheming="true" Theme="MaterialCompact" CallbackPageSize="50"
                                ClearButton-DisplayMode="Always" ValueField="IdRed" DisplayFormatString="{0} | {1}" runat="server">
                                <ClientSideEvents SelectedIndexChanged="function(s, e) { cbPrincipal.PerformCallback('ABRIR-RED|'); }" />
                                <CaptionSettings Position="Left" />
                                <CaptionStyle CssClass="Etiqueta"></CaptionStyle>
                                <Columns>
                                    <dx:ListBoxColumn FieldName="IdRed" Name="gcIdRed" Caption="Id Red"></dx:ListBoxColumn>
                                    <dx:ListBoxColumn FieldName="Red" Name="gcRed" Caption="Red"></dx:ListBoxColumn>
                                </Columns>
                                <ValidationSettings ErrorDisplayMode="ImageWithTooltip" ErrorText="ESTE CAMPO ES REQUERIDO."></ValidationSettings>
                            </dx:ASPxComboBox>
                        </div>
                           
                        <div class="col-4" style="position:relative; padding-top:8px">
                            <div style="position:absolute">
                            <dx:BootstrapButton CssClasses-Icon="far fa-file-excel" UseSubmitBehavior="false" ID="btnExportarExcel" runat="server" AutoPostBack="false" Text="Exportar a Excel" OnClick="btnExportarExcel_Click">
                                <SettingsBootstrap RenderOption="Success" />
                            </dx:BootstrapButton>
                        </div>
                    </div>
                        </div>
                    
                    <div class="right-content">
                        <div class="right-content-top">
                        </div>
                        <div class="right-content-content">


                            <dx:ASPxGridView ID="grdFormularios" runat="server" Width="100%" AutoGenerateColumns="False" Theme="MaterialCompact" KeyFieldName="IdExpediente" HorizontalScrollBarMode="Auto">

                                <%--<ClientSideEvents CustomButtonClick="function(s, e) {	cbPrincipal.PerformCallback('ABRIR-DESDECONSULTA|'+ s.GetRowKey(e.visibleIndex));}" RowDblClick="function(s, e) {	cbPrincipal.PerformCallback('ABRIR-DESDECONSULTA|'+ s.GetRowKey(e.visibleIndex));}"></ClientSideEvents>--%>
                                <SettingsContextMenu Enabled="True" EnableRowMenu="False"></SettingsContextMenu>

                                <SettingsBehavior EnableCustomizationWindow="true" AllowEllipsisInText="true" />
                                <%--<SettingsResizing ColumnResizeMode="Control" Visualization="Live" />--%>
                                <SettingsPager Mode="ShowPager" PageSize="50" Visible="false"></SettingsPager>
                                <Settings ShowFooter="true" ShowFilterRow="false" ShowHeaderFilterButton="true" HorizontalScrollBarMode="Auto" VerticalScrollBarMode="auto" VerticalScrollableHeight="450" VerticalScrollBarStyle="VirtualSmooth" />
                                <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                <SettingsSearchPanel AllowTextInputTimer="false" Visible="true"></SettingsSearchPanel>
                                <SettingsResizing Visualization="Postponed" ColumnResizeMode="Control" />

                                <Columns>
                                    <dx:GridViewDataTextColumn FieldName="IdFormulario" ShowInCustomizationForm="True" Name="gcIdFormulario" Caption="IdFormulario" VisibleIndex="0">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="IdRed" ShowInCustomizationForm="True" Name="gcIdRed" Caption="IdRed" VisibleIndex="5">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Red" ShowInCustomizationForm="True" Name="gcRed" Caption="Red" VisibleIndex="6">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Provincia" ShowInCustomizationForm="True" Name="gcProvincia" Caption="Provincia" VisibleIndex="7">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Municipio" ShowInCustomizationForm="True" Name="gcMunicipio" Caption="Municipio" VisibleIndex="8">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="DistritoMunicipal" ShowInCustomizationForm="True" Name="gcDistritoMunicipal" Caption="DistritoMunicipal" VisibleIndex="9">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Seccion" ShowInCustomizationForm="True" Name="gcSeccion" Caption="Seccion" VisibleIndex="10">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Barrio" ShowInCustomizationForm="True" Name="gcBarrio" Caption="Barrio" VisibleIndex="11">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Poligono" ShowInCustomizationForm="True" Name="gcPoligono" Caption="Poligono" VisibleIndex="12">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="AreaSupervision" ShowInCustomizationForm="True" Name="gcAreaSupervision" Caption="Area" VisibleIndex="13">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Segmento" ShowInCustomizationForm="True" Name="gcSegmento" Caption="Segmento" VisibleIndex="14">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Calle" ShowInCustomizationForm="True" Name="gcCalle" Caption="Calle" VisibleIndex="15">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="NumeroVivienda" ShowInCustomizationForm="True" Name="gcNumeroVivienda" Caption="# Vivienda" VisibleIndex="16">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Edificio" ShowInCustomizationForm="True" Name="gcEdificio" Caption="Edificio" VisibleIndex="17">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="SubBarrio" ShowInCustomizationForm="True" Name="gcSubBarrio" Caption="SubBarrio" VisibleIndex="18">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="EntreLaCalle" ShowInCustomizationForm="True" Name="gcEntreLaCalle" Caption="EntreLaCalle" VisibleIndex="19">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="YLaCalle" ShowInCustomizationForm="True" Name="gcYLaCalle" Caption="YLaCalle" VisibleIndex="20">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn FieldName="Georeferencia" ShowInCustomizationForm="True" Name="gcGeoreferencia" Caption="Georeferencia" VisibleIndex="21">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataCheckColumn FieldName="VivenMenores5" Name="gcVivenMenores5" Caption="&#191;Viven NN?" VisibleIndex="1"></dx:GridViewDataCheckColumn>
                                    <dx:GridViewDataCheckColumn FieldName="ViveEmbarazadas" Name="gcViveEmbarazadas" Caption="&#191;Viven Embarazadas?" VisibleIndex="2"></dx:GridViewDataCheckColumn>
                                    <dx:GridViewDataSpinEditColumn FieldName="CantidadNN" Name="gcCantidadNN" Caption="Cantidad de NN" VisibleIndex="3">
                                        <PropertiesSpinEdit DisplayFormatString="g"></PropertiesSpinEdit>
                                    </dx:GridViewDataSpinEditColumn>
                                    <dx:GridViewDataSpinEditColumn FieldName="CantidadEmbarazadas" Name="gcCantidadEmbarazadas" Caption="Cantidad Embarazadas" VisibleIndex="4">
                                        <PropertiesSpinEdit DisplayFormatString="g"></PropertiesSpinEdit>
                                    </dx:GridViewDataSpinEditColumn>

                                </Columns>
                                <TotalSummary>
                                    <dx:ASPxSummaryItem Visible="true" ShowInColumn="gcIdFormulario" DisplayFormat="0,0 Formulario (s)" SummaryType="Count" FieldName="IdFormulario" ShowInGroupFooterColumn="gcIdFormulario"></dx:ASPxSummaryItem>
                                    <dx:ASPxSummaryItem Visible="true" ShowInColumn="gcCantidadNN" DisplayFormat="0,0 NN" SummaryType="Sum" FieldName="CantidadNN" ShowInGroupFooterColumn="gcCantidadNN"></dx:ASPxSummaryItem>
                                    <dx:ASPxSummaryItem Visible="true" ShowInColumn="gcCantidadEmbarazadas" DisplayFormat="0,0 Embarazada (s)" SummaryType="Sum" FieldName="CantidadEmbarazadas" ShowInGroupFooterColumn="gcCantidadEmbarazadas"></dx:ASPxSummaryItem>
                                </TotalSummary>



                                <FormatConditions>
                                    <dx:GridViewFormatConditionHighlight ApplyToRow="True" Expression="[VivenMenores5] = 1 And [CantidadNN] = 0" FieldName="VivenMenores5,CantidadNN"></dx:GridViewFormatConditionHighlight>
                                    <dx:GridViewFormatConditionHighlight ApplyToRow="True" Expression="[ViveEmbarazadas] = 1 And [CantidaEmbarazadas] = 0" FieldName="ViveEmbarazadas"></dx:GridViewFormatConditionHighlight>
                                </FormatConditions>

                                <Styles>
                                    <Footer BackColor="#C8C8C8"></Footer>
                                </Styles>

                            </dx:ASPxGridView>
                            <dx:ASPxGridViewExporter ID="grdFormulariosExporter" GridViewID="grdFormularios" runat="server">
                            </dx:ASPxGridViewExporter>

                        </div>
                    </div>
                </div>
                </div>

            </dx:PanelContent>
        </PanelCollection>

    </dx:ASPxCallbackPanel>
</asp:Content>

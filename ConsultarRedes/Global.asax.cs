﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.SessionState;
using System.Data;

namespace ConsultarRedes
{
    public class Global : System.Web.HttpApplication
    {
        public struct INS
        {
            //public bool GetNN
            //{
            //    get { return Convert.ToBoolean(HttpContext.Current.Session["INS_GetNN"].ToString()); }
            //    set { HttpContext.Current.Session["INS_GetNN"] = value; }
            //}

            public int IdCentro
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["INS_IdCentro"].ToString()); }
                set { HttpContext.Current.Session["INS_IdCentro"] = value; }
            }

            public string Centro
            {
                get { return (string)HttpContext.Current.Session["INS_Centro"]; }
                set { HttpContext.Current.Session["INS_Centro"] = value; }
            }

            public int IdNN
            {
                get { return (HttpContext.Current.Session["INS_IdNN"] == null ? 0 : Convert.ToInt32(HttpContext.Current.Session["INS_IdNN"].ToString())); }
                set { HttpContext.Current.Session["INS_IdNN"] = value; }
            }

            public bool UpdateNN
            {
                get { return Convert.ToBoolean(HttpContext.Current.Session["INS_Update"].ToString()); }
                set { HttpContext.Current.Session["INS_Update"] = value; }
            }

            public DataRow INS_drInscripcion
            {
                get
                {
                    //return (DataRow)HttpContext.Current.Session["INS_drInscripcion"];
                    DataRow dr = null;

                    DataTable dt = (DataTable)HttpContext.Current.Session["INS_drInscripcion"];

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        dr = dt.Rows[0];
                    }

                    return dr;
                }
                set
                {
                    //HttpContext.Current.Session["INS_drInscripcion"] = value;
                    DataTable dt = new DataTable();
                    DataRow dr = value;

                    if (dr != null)
                    {
                        if (dr.Table != null && dr.Table.Rows.Count > 0)
                        {
                            dt = dr.Table.Copy();
                        }
                        else
                        {
                            if (dr.Table != null && dr.Table.Rows.Count > 0)
                            {
                                dt = dr.Table.Copy();
                            }
                            else
                            {
                                dt = dr.Table.Copy();
                                dt.Rows.Add(dr.ItemArray);
                            }
                        }
                        //dt.Rows.Add(dr);
                    }

                    HttpContext.Current.Session["INS_drInscripcion"] = dt;
                }
            }

            public DataRow INS_drNN
            {
                get
                {
                    //return (DataRow)HttpContext.Current.Session["INS_drNN"]; 
                    DataRow dr = null;

                    DataTable dt = (DataTable)HttpContext.Current.Session["INS_drNN"];

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        dr = dt.Rows[0];
                    }

                    return dr;
                }
                set
                {
                    //HttpContext.Current.Session["INS_drNN"] = value; 
                    DataTable dt = new DataTable();
                    DataRow dr = value;

                    if (dr != null)
                    {
                        if (dr.Table != null && dr.Table.Rows.Count > 0)
                        {
                            dt = dr.Table.Copy();
                        }
                        else
                        {
                            dt = dr.Table.Copy();
                            dt.Rows.Add(dr.ItemArray);
                        }
                    }

                    HttpContext.Current.Session["INS_drNN"] = dt;
                }
            }

            public DataRow INS_drSalud
            {
                get
                {
                    //return (DataRow)HttpContext.Current.Session["INS_drSalud"];
                    DataRow dr = null;

                    DataTable dt = (DataTable)HttpContext.Current.Session["INS_drSalud"];

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        dr = dt.Rows[0];
                    }

                    return dr;
                }
                set
                {
                    //HttpContext.Current.Session["INS_drSalud"] = value;
                    DataTable dt = new DataTable();
                    DataRow dr = value;

                    if (dr != null)
                    {
                        if (dr.Table != null && dr.Table.Rows.Count > 0)
                        {
                            dt = dr.Table.Copy();
                        }
                        else
                        {
                            dt = dr.Table.Copy();
                            dt.Rows.Add(dr.ItemArray);
                        }
                    }

                    HttpContext.Current.Session["INS_drSalud"] = dt;
                }
            }

            public DataRow drVivienda
            {
                get
                {
                    //return (DataRow)HttpContext.Current.Session["INS_drVivienda"];
                    DataRow dr = null;

                    DataTable dt = (DataTable)HttpContext.Current.Session["INS_drVivienda"];

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        dr = dt.Rows[0];
                    }

                    return dr;
                }
                set
                {
                    //HttpContext.Current.Session["INS_drVivienda"] = value;
                    DataTable dt = new DataTable();
                    DataRow dr = value;

                    if (dr != null)
                    {
                        if (dr.Table != null && dr.Table.Rows.Count > 0)
                        {
                            dt = dr.Table.Copy();
                        }
                        else
                        {
                            if (dr.Table != null && dr.Table.Rows.Count > 0)
                            {
                                dt = dr.Table.Copy();
                            }
                            else
                            {
                                dt = dr.Table.Copy();
                                dt.Rows.Add(dr.ItemArray);
                            }
                        }
                        //dt.Rows.Add(dr);
                    }

                    HttpContext.Current.Session["INS_drVivienda"] = dt;
                }
            }

            public DataRow drFamilia
            {
                get
                {
                    //return (DataRow)HttpContext.Current.Session["INS_drFamilia"];
                    DataRow dr = null;

                    DataTable dt = (DataTable)HttpContext.Current.Session["INS_drFamilia"];

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        dr = dt.Rows[0];
                    }

                    return dr;
                }
                set
                {
                    //HttpContext.Current.Session["INS_drFamilia"] = value;
                    DataTable dt = new DataTable();
                    DataRow dr = value;

                    if (dr != null)
                    {
                        if (dr.Table != null && dr.Table.Rows.Count > 0)
                        {
                            dt = dr.Table.Copy();
                        }
                        else
                        {
                            dt = dr.Table.Copy();
                            dt.Rows.Add(dr.ItemArray);
                        }
                    }

                    HttpContext.Current.Session["INS_drFamilia"] = dt;
                }
            }

            public DataRow drSeguridad
            {
                get
                {
                    //return (DataRow)HttpContext.Current.Session["INS_drSeguridad"];
                    DataRow dr = null;

                    DataTable dt = (DataTable)HttpContext.Current.Session["INS_drSeguridad"];

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        dr = dt.Rows[0];
                    }

                    return dr;
                }
                set
                {
                    //HttpContext.Current.Session["INS_drSeguridad"] = value;
                    DataTable dt = new DataTable();
                    DataRow dr = value;

                    if (dr != null)
                    {
                        if (dr.Table != null && dr.Table.Rows.Count > 0)
                        {
                            dt = dr.Table.Copy();
                        }
                        else
                        {
                            dt = dr.Table.Copy();
                            dt.Rows.Add(dr.ItemArray);
                        }
                    }

                    HttpContext.Current.Session["INS_drSeguridad"] = dt;
                }
            }

            public DataTable dtAutorizados
            {
                get { return (DataTable)HttpContext.Current.Session["INS_dtAutorizados"]; }
                set { HttpContext.Current.Session["INS_dtAutorizados"] = value; }
            }

            public DataRow drDocumentos
            {
                get
                {
                    DataRow dr = null;

                    DataTable dt = (DataTable)HttpContext.Current.Session["INS_drDocumentos"];

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        dr = dt.Rows[0];
                    }

                    return dr;
                }

                set
                {
                    DataTable dt = new DataTable();
                    DataRow dr = value;

                    if (dr != null)
                    {
                        if (dr.Table != null && dr.Table.Rows.Count > 0)
                        {
                            dt = dr.Table.Copy();
                        }
                        else
                        {
                            dt = dr.Table.Copy();
                            dt.Rows.Add(dr.ItemArray);
                        }
                    }

                    HttpContext.Current.Session["INS_drDocumentos"] = dt;
                }
            }

            public DataTable dtRegistroNacimiento
            {
                get { return (DataTable)HttpContext.Current.Session["INS_dtRegistroNacimiento"]; }
                set { HttpContext.Current.Session["INS_dtRegistroNacimiento"] = value; }
            }

        }

        public struct USER
        {
            public int IdUsuario
            {
                get { return Convert.ToInt32((HttpContext.Current.Session["User_IdUsuario"] ?? 0).ToString()); }
                set { HttpContext.Current.Session["User_IdUsuario"] = value; }
            }

            public string UserName
            {
                get { return HttpContext.Current.Session["User_UserName"].ToString(); }
                set { HttpContext.Current.Session["User_UserName"] = value; }
            }

            public int IdEstancia
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["User_IdEstancia"].ToString()); }
                set { HttpContext.Current.Session["User_IdEstancia"] = value; }
            }

            public string Nombres
            {
                get { return HttpContext.Current.Session["User_Nombres"].ToString(); }
                set { HttpContext.Current.Session["User_Nombres"] = value; }
            }

            public string Apellidos
            {
                get { return HttpContext.Current.Session["User_Apellidos"].ToString(); }
                set { HttpContext.Current.Session["User_Apellidos"] = value; }
            }

            public string Cedula
            {
                get { return (HttpContext.Current.Session["User_Cedula"] ?? "").ToString(); }
                set { HttpContext.Current.Session["User_Cedula"] = value; }
            }

            public string Password
            {
                get { return HttpContext.Current.Session["User_Password"].ToString(); }
                set { HttpContext.Current.Session["User_Password"] = value; }
            }

            public string Posicion
            {
                get { return HttpContext.Current.Session["User_Posicion"].ToString(); }
                set { HttpContext.Current.Session["User_Posicion"] = value; }
            }

            public int Perfil
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["User_Perfil"].ToString()); }
                set { HttpContext.Current.Session["User_Perfil"] = value; }
            }

            public int IdTerritorio
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["User_IdTerritorio"].ToString()); }
                set { HttpContext.Current.Session["User_IdTerritorio"] = value; }
            }

            public int IdRed
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["User_IdRed"].ToString()); }
                set { HttpContext.Current.Session["User_IdRed"] = value; }
            }

            public DataTable dtBotonesAccesos
            {
                get { return (DataTable)HttpContext.Current.Session["User_dtBotonesAccesos"]; }
                set { HttpContext.Current.Session["User_dtBotonesAccesos"] = value; }
            }

            public string Avatar
            {
                get { return HttpContext.Current.Session["User_Avatar"].ToString(); }
                set { HttpContext.Current.Session["User_Avatar"] = value; }
            }

            public bool InicioExitoso
            {
                get { return (bool)HttpContext.Current.Session["User_InicioExitoso"]; }
                set { HttpContext.Current.Session["User_InicioExitoso"] = value; }
            }

            public DataTable dtSEG_CentrosAcceso
            {
                get { return (DataTable)HttpContext.Current.Session["User_dtSEG_CentrosAcceso"]; }
                set { HttpContext.Current.Session["User_dtSEG_CentrosAcceso"] = value; }
            }

            public string Puesto
            {
                get { return HttpContext.Current.Session["User_Puesto"].ToString(); }
                set { HttpContext.Current.Session["User_Puesto"] = value; }
            }

            public string Departamento
            {
                get { return HttpContext.Current.Session["User_Departamento"].ToString(); }
                set { HttpContext.Current.Session["User_Departamento"] = value; }
            }

            public int IdPkSeccion
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["User_IdPkSeccion"].ToString()); }
                set { HttpContext.Current.Session["User_IdPkSeccion"] = value; }
            }

        }
        //Luis.Mejia
        //Estructura para las solicitudes 
        public struct SOL
        {
            //Luis.Mejia
            //Variable global para la solicitud de cambio de informacion
            public int DatosProtegidos
            {

                get { return (HttpContext.Current.Session["DatosProtegidos"] == null ? 0 : Convert.ToInt32(HttpContext.Current.Session["DatosProtegidos"].ToString())); }
                set { HttpContext.Current.Session["DatosProtegidos"] = value; }
            }

            public int DatosBloqueados
            {
                get { return (HttpContext.Current.Session["DatosBloqueados"] == null ? 0 : Convert.ToInt32(HttpContext.Current.Session["DatosBloqueados"].ToString())); }
                set { HttpContext.Current.Session["DatosBloqueados"] = value; }
            }
        }

        public struct SEL
        {
            public int IdIndice
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["SEL_IdIndice"].ToString()); }
                set { HttpContext.Current.Session["SEL_IdIndice"] = value; }
            }

            public int IdSeleccion
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["SEL_IdSeleccion"].ToString()); }
                set { HttpContext.Current.Session["SEL_IdSeleccion"] = value; }
            }

            public int IdInscripcion
            {
                get { return Convert.ToInt32(HttpContext.Current.Session["SEL_IdInscripcion"].ToString()); }
                set { HttpContext.Current.Session["SEL_IdInscripcion"] = value; }
            }

            public string IDs_FIFEnCAFIs
            {
                get { return HttpContext.Current.Session["SEL_IDs_FIFEnCAFIs"].ToString(); }
                set { HttpContext.Current.Session["SEL_IDs_FIFEnCAFIs"] = value; }
            }



            public struct MANT
            {
                public DataTable dtSelecciones
                {
                    get { return (DataTable)HttpContext.Current.Session["SEL_MANT_dtSelecciones"]; }
                    set { HttpContext.Current.Session["SEL_MANT_dtSelecciones"] = value; }
                }

                public int IdSeleccion
                {
                    get { return Convert.ToInt32(HttpContext.Current.Session["SEL_MANT_IdSeleccion"].ToString()); }
                    set { HttpContext.Current.Session["SEL_MANT_IdSeleccion"] = value; }
                }

                public DataTable dtNNsSeleccion
                {
                    get { return (DataTable)HttpContext.Current.Session["SEL_MANT_dtNNsSeleccion"]; }
                    set { HttpContext.Current.Session["SEL_MANT_dtNNsSeleccion"] = value; }
                }
            }
        }


        protected void Application_Start(object sender, EventArgs e)
        {
            DevExpress.Utils.UrlAccessSecurityLevelSetting.SecurityLevel = DevExpress.Utils.UrlAccessSecurityLevel.Unrestricted;
        }

        protected void Session_Start(object sender, EventArgs e)
        {
            SI_INAIPI.Models.Comun.Hoy = SI_INAIPI.Models.DA.FechaHora();

            Session.Timeout = 180;

            //if (Session["User_IdUsuario"] == null || Session["User_IdUsuario"].ToString().Length == 0)
            //{
            //    if (Session["User_InicioExitoso"] == null || !(bool)Session["User_InicioExitoso"])
            //    {
            //        Response.Redirect("~/Views/LogIn.aspx");
            //    }
            //}
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {

        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {

        }

        protected void Application_Error(object sender, EventArgs e)
        {

        }

        protected void Session_End(object sender, EventArgs e)
        {
            //Session["Usuario"] = null;
            //Response.Redirect("~/Views/LogIn.aspx");
        }


        protected void Application_End(object sender, EventArgs e)
        {

        }
    }
}
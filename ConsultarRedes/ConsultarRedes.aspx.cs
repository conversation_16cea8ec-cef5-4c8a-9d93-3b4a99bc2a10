﻿using SI_INAIPI.Controls;
using SI_INAIPI.Controls.LEV;
using SI_INAIPI.Models;
using SI_INAIPI.Models.LEV;
using SI_INAIPI.Models.TER;
using SI_INAIPI.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using INAIPI.Core;


namespace ConsultarRedes
{
    public partial class ConsultarRedes : SI_INAIPI.Views.BasePage
    {
        TER_RedesCtrl ctrlRedes = new TER_RedesCtrl();
        LEV_FormulariosCtrl ctrlFormularios = new LEV_FormulariosCtrl();

        private ICollection<TER_Red> ListadoRedes
        {
            get { return (List<TER_Red>)Session["ConsultarRedes_Redes"]; }
            set { Session["ConsultarRedes_Redes"] = value; }
        }
        private List<LEV_Formularios.vi_LEV_ConsultaFormularios> ListadoFormularios
        {
            get { return (List<LEV_Formularios.vi_LEV_ConsultaFormularios>)Session["ConsultarRedes_Formularios"]; }
            set { Session["ConsultarRedes_Formularios"] = value; }
        }
        private String NombreRed
        {
            get { return (String)Session["ConsultarRedes_NombreRed"]; }
            set { Session["ConsultarRedes_NombreRed"] = value; }
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            grdFormularios.CssClass = "ScreenHeight";

            if (!IsPostBack)
            {
                ListadoRedes = ctrlRedes.GetAllRedes();
                cboRedes.DataSource = ListadoRedes;
                cboRedes.DataBind(); 
            }



            if (IsPostBack)
            {
                grdFormularios.DataSource = ListadoFormularios;
                grdFormularios.DataBind();
            }
        }
        protected void Page_Init(object sender, EventArgs e)
        {
            cboRedes.DataSource = ListadoRedes;
            cboRedes.DataBind();
        }

        protected void cbPrincipal_Callback(object sender, DevExpress.Web.CallbackEventArgsBase e)
        {
            string[] parametro = e.Parameter.Split('|');

            int IdRed = 0;
            NombreRed = "";
            switch (parametro[0])
            {


                case "ABRIR-RED":
                    //if (parametro.Length > 1)
                    //{
                    //    int.TryParse(parametro[1].ToString(), out IdRed);

                    //}
                    string[] valor = cboRedes.Text.Split(';');

                    int.TryParse((cboRedes.Value ?? "0").ToString(), out IdRed);

                    ListadoFormularios = ctrlFormularios.ListarFormulariosPorRed(IdRed);
                    grdFormularios.DataSource = ListadoFormularios;
                    grdFormularios.DataBind();
                    //TituloRed.InnerText = ListadoFormularios.First().Red;

                    //TituloRed.InnerText = (from red in ListadoRedes where red.IdRed == IdRed select red.Red.ToUpper() + " [" + red.IdRed.ToString() + "], Cantidad de Registros Encontrados: " + ListadoFormularios?.Count.ToString("N0")).FirstOrDefault();
                    //TituloRed.InnerText = ListadoRedes.Select(red => red.Red + " [" + red.IdRed.ToString() + "]").FirstOrDefault();
                    //TituloRed.InnerText = (from red in ListadoRedes where red.IdRed == IdRed select red.Red.ToUpper() + " [" + red.IdRed.ToString() + "]").FirstOrDefault();
                    NombreRed = ListadoRedes.Select(red => red.Red + " [" + red.IdRed.ToString() + "]").FirstOrDefault();
                    break;


            }

        }

        protected void btnExportarExcel_Click(object sender, EventArgs e)
        {
            if (ListadoRedes != null)
            {
                //string NombreArchivo = TituloRed.InnerText;
                grdFormulariosExporter.WriteXlsxToResponse(NombreRed, true, new DevExpress.XtraPrinting.XlsxExportOptionsEx { ExportType = DevExpress.Export.ExportType.WYSIWYG });
            }
        }
    }
}
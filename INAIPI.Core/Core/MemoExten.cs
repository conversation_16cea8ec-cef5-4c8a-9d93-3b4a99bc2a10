﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public static class MemoExten
    {
        public static bool IsValidMemo(this string txtMemo)
        {
            bool Bien = true;

            if (txtMemo == "")
            {
                Bien = false;
            }
            else
            {
                string[] memo = txtMemo.Split('-');

                if (memo.Length != 4)
                {
                    Bien = false;
                }
                else
                {
                    Int64 ano = 0;
                    Int16 num = 0;

                    if (memo[2].ToString().Length >= 2 && memo[2].ToString().Length <= 4)
                    {
                        if (!Int16.TryParse(memo[2].ToString(), out num))
                        {
                            Bien = false;
                        }
                    }
                    else
                    {
                        Bien = false;
                    }

                    if (memo[3].ToString().Length >= 3 && memo[3].ToString().Length <= 10)
                    {
                        if (!Int64.TryParse(memo[3].ToString(), out ano))
                        {
                            Bien = false;
                        }
                    }
                    else
                    {
                        Bien = false;
                    }
                }

            }

            return Bien;
        }
    }
}

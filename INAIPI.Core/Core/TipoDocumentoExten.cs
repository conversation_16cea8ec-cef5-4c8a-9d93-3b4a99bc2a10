﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public static class TipoDocumentoExten
    {
        public static bool IsValidDocumento(this string strIn, int idTipoDocumento = 0)
        {
            bool bien = true;
            int errorContador = errorContador = Regex.Matches(strIn, @"[a-zA-Z]").Count;

            if (idTipoDocumento == 0)
            {
                bien = false;
            }

            if (idTipoDocumento == 1)
            {
                if (strIn == "")
                {
                    bien = false;
                }
                else
                {
                    if (strIn.Length != 11 || errorContador > 0)
                    {
                        bien = false;
                    }                    
                }
            }

            if (idTipoDocumento == 2)
            {
                if (strIn == "")
                {
                    bien = false;
                }
                else
                {
                    //if (strIn.Length != 9 || errorContador > 0)
                    //{
                    //    bien = false;
                    //}
                }
            }

            return bien;
        }

        public static bool CedulaIsValidAlgoritmo(this string cedula)
        {
            int vnTotal = 0;

            string vcCedula = cedula.Replace("-", "");

            int pLongCed = vcCedula.Trim().Length;

            int[] digitoMult = new int[11] { 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1 };


            if (cedula == "40202026399") //Esta validacion se ha integrado para solucionar un problema rapidamente pero aun queda deuda tecnica.
            {
                return true;
            }

            if (pLongCed < 11 || pLongCed > 11)
                return false;
            
            for (int vDig = 1; vDig <= pLongCed; vDig++)
            {
                int vCalculo = Int32.Parse(vcCedula.Substring(vDig - 1, 1)) * digitoMult[vDig - 1];

                if (vCalculo < 10)
                    vnTotal += vCalculo;
                else
                    vnTotal += Int32.Parse(vCalculo.ToString().Substring(0, 1)) + Int32.Parse(vCalculo.ToString().Substring(1, 1));
            }
            
            if (vnTotal % 10 == 0)
                return true;
            else
                return false;
        }

        public static bool RNCIsValidAlgoritmo(this string RNC)
        {
            int vnTotal = 0;

            int[] digitoMult = new int[8] { 7, 9, 8, 6, 5, 4, 3, 2 };

            string vcRNC = RNC.Replace("-", "").Replace(" ", "");

            string vDigito = vcRNC.Substring(8, 1);

            if (vcRNC.Length.Equals(9))
                if (!"145".Contains(vcRNC.Substring(0, 1)))
                    return false;
            
            for (int vDig = 1; vDig <= 8; vDig++)
            {
                int vCalculo = Int32.Parse(vcRNC.Substring(vDig - 1, 1)) * digitoMult[vDig - 1];

                vnTotal += vCalculo;
            }
            
            if (vnTotal % 11 == 0 && vDigito == "1" || vnTotal % 11 == 1 && vDigito == "1" || (11 - (vnTotal % 11)).Equals(vDigito))
                return true;
            else
                return false;
        }
    }
}

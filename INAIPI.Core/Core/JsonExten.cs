﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public static class CloneExten
    {
        public static T Clone<T>(this T objeto)
        {
            string serializado = JsonConvert.SerializeObject(objeto);
            return JsonConvert.DeserializeObject<T>(serializado);
        }

        public static TSalida Clone<TEntrada, TSalida>(this TEntrada Objeto)
        {
            string serializado = JsonConvert.SerializeObject(Objeto);
            return JsonConvert.DeserializeObject<TSalida>(serializado);
        }

        public static string ToJson<T>(this T objeto, bool formateado = false)
        {
            if (!formateado)
            {
                return JsonConvert.SerializeObject(objeto);
            }
            else
            {
                return JsonConvert.SerializeObject(objeto, Formatting.Indented);
            }
        }

        public static T JsonToModel<T>(this string objeto)
        {
            return JsonConvert.DeserializeObject<T>(objeto);
        }
        
        public static bool Compare<T>(this T objeto1, T objeto2)
        {
            return JsonConvert.SerializeObject(objeto1) == JsonConvert.SerializeObject(objeto2);
        }

    }
}

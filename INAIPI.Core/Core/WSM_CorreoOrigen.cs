﻿using INAIPI.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace IntranetApp.Models.COMUN
{
    [Serializable]
    [Dapper.Contrib.Extensions.Table("WSM_CorreoOrigen")]
    public class WSM_CorreoOrigen : BaseModel
    {
        [Dapper.Contrib.Extensions.ExplicitKey]
        public byte IdCorreoOrigen { get; set; }
        public string CorreoOrigen { get; set; }
        public string Password { get; set; }
        public int Puerto { get; set; }
        public string host { get; set; }
        public bool SSl { get; set; }
        public bool HTML { get; set; }
        public bool Credenciales { get; set; }
    }
}
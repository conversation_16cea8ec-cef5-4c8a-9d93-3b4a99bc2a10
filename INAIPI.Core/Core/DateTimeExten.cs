﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public static class DateTimeExten
    {
        public static int CalcularDiasEntreDosFechas(this DateTime date, DateTime fechaHasta)
        {
            DateTime FI = date;
            DateTime FF = fechaHasta;

            TimeSpan Tiempo = new TimeSpan(FF.Ticks - FI.Ticks);

            return  Tiempo.Days;
        }

        public static string CalcularTiempoStr(this DateTime date, DateTime fechaHasta)
        {
           int Dias =  Math.Abs( CalcularDiasEntreDosFechas(date, fechaHasta));
            return GetTiempoStr(Dias);

        }

        public static string GetTiempoStr(this int Dias)
        {
            //if (Dias == 365)
            //{
            //    Dias = 
            //} 
            string result = string.Empty;
            int ano = (int)(Dias / 365.25);
            double diasDeRestosdeAnos = (Dias % 365.25);
            int mes = (int)(diasDeRestosdeAnos / 30.4375);
            //int dia = (int)Math.Round((diasDeRestosdeAnos % 30.4375));
            int dia = (int)(diasDeRestosdeAnos % 30.4375);

            result += ano + (ano != 1 ? " AÑOS; " : " AÑO; ");
            result += mes + (mes != 1 ? " MESES; " : " MES; ");
            result += dia + (dia != 1 ? " DIAS" : " DIA");

            return result;
        }

        // La variable "Dias" debe llegar dividida entre 8 que son la cantidad de horas laborales.
        public static string GetTiempoLaboralStr(this decimal Dias)
        {
            string result = string.Empty;
            
            int dia = (int)Dias / 1;
            decimal horasSobran = (Dias % 1) ;

            int horas = (int)(horasSobran * 8 );

            decimal minutosSobran = (horasSobran * 8) - (int)(horasSobran * 8);
            
            int minutos = (int) Math.Round(minutosSobran * 60,2);
            
            result += dia + (dia != 1 ? " DIAS " : " DIA ");
            result += horas + (horas != 1 ? " HORAS " : " HORA ");
            result += minutos + (minutos != 1 ? " MINUTOS" : " MINUTO");
            
            return result;
        }

        public static double CalcularCantidadDiasEspacificos(DayOfWeek dia, DateTime fechaInicio, DateTime fechaFin)
        {
            TimeSpan ts = fechaFin - fechaInicio;
            double count = Math.Floor(ts.TotalDays / 7);
            double remainder = (ts.TotalDays % 7);
            double sinceLastDay = (fechaFin.DayOfWeek - dia);
            if (sinceLastDay < 0) sinceLastDay += 7;

            if (remainder >= sinceLastDay) count++;

            return count > 0 ? count : 0;
        }
    }
}

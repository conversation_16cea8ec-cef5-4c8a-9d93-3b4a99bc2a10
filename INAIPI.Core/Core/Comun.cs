﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data;
using System.Data.SqlClient;
using INAIPI.DataAcces;

namespace INAIPI.Core
{
    public static class Comun
    {
        public static DateTime Hoy
        {
            get { return Convert.ToDateTime(HttpContext.Current.Session["Comun_Hoy"]); }
            set { HttpContext.Current.Session["Comun_Hoy"] = value; }
        }

        public static void SetValorCombo(DropDownList oCombo, object Valor)
        {
            try
            {
                foreach (object oItem in oCombo.Items)
                {
                    if (Val(oItem.ToString()) == Valor.ToString())
                    {
                        oCombo.SelectedValue = oItem.ToString();
                        break;
                    }
                }
            }
            catch (Exception)
            {
                //MessageBox.Show(ex.Message);
            }
        }

        public static void SetTextoCombo(DropDownList oCombo, string Valor)
        {
            try
            {
                foreach (object oItem in oCombo.Items)
                {
                    if (oItem.ToString() == Valor)
                    {
                        oCombo.SelectedValue = oItem.ToString();
                        break;
                    }
                }
            }
            catch (Exception)
            {
                //MessageBox.Show(ex.Message);
            }
        }

        public static void SetInValueCombo(DropDownList oCombo, object Valor)
        {
            foreach (ListItem oItem in oCombo.Items)
            {
                if (oItem.Value == Valor.ToString())
                {
                    oCombo.SelectedValue = oItem.Value;
                    break;
                }
            }
        }

        public static string Val(string TextVal, string StartKey = "[", string EndKey = "]", string ReturnIfEmpty = "0")
        {
            string valor = "";
            bool Tomar = false;

            foreach (char chr in TextVal)
            {
                if (!Tomar)
                {
                    if (chr == Convert.ToChar(StartKey))
                    {
                        Tomar = true;
                        continue;
                    }
                }
                else
                {
                    if (chr == Convert.ToChar(EndKey))
                    {
                        Tomar = false;
                    }
                }

                if (Tomar)
                {
                    valor += chr.ToString();
                }
                else
                {
                    continue; //break;
                }
            }

            if (valor.Trim().Length > 0)
            {
                return valor;
            }
            else
            {
                return ReturnIfEmpty;
            }
        }

        public static HtmlGenericControl CrearDiv(string CssClass = "", string id = "")
        {
            HtmlGenericControl div = new HtmlGenericControl("div");
            if (CssClass.Length > 0)
            {
                div.Attributes.Add("class", CssClass);
            }

            if (id.Length > 0)
            {
                div.Attributes.Add("id", id);
            }

            return div;
        }

        public static HtmlGenericControl CrearHtml(string tag, string CssClass = "", string id = "", bool runat = false)
        {
            HtmlGenericControl html = new HtmlGenericControl(tag);
            if (CssClass.Length > 0)
            {
                html.Attributes.Add("class", CssClass);
            }

            if (id.Length > 0)
            {
                html.Attributes.Add("id", id);
            }

            if (runat)
            {
                html.Attributes.Add("runat", "server");
            }

            return html;
        }

        /// <summary>
        /// Retorna la posición de un registro dentro de una tabla de datos.
        /// </summary>
        /// <param name="dt">Tabla de datos</param>
        /// <param name="dr">Registro que se buscará</param>
        /// <returns></returns>
        public static int Posicion_Registro(DataTable dt, DataRow dr)
        {
            return dt.Rows.IndexOf(dr);
        }

        public static void FillCombosFecha(ref DropDownList cboDia, ref DropDownList cboMes, ref DropDownList cboAno, int CantAnos = 10)
        {
            cboDia.Items.Clear();
            cboMes.Items.Clear();
            cboAno.Items.Clear();

            cboDia.Items.Add(new ListItem { Text = "D", Value = "" });
            for (int Dia = 1; Dia <= 31; Dia++)
            {
                cboDia.Items.Add(new ListItem { Text = Dia.ToString("00"), Value = Dia.ToString() });
            }

            cboMes.Items.Add(new ListItem { Text = "M", Value = "" });
            for (int Mes = 1; Mes <= 12; Mes++)
            {
                cboMes.Items.Add(new ListItem { Text = Mes.ToString("00"), Value = Mes.ToString() });
            }

            cboAno.Items.Add(new ListItem { Text = "A", Value = "" });

            int AnoActual = DateTime.Today.Year;
            for (int Ano = AnoActual; Ano >= (AnoActual - CantAnos); Ano--)
            {
                cboAno.Items.Add(new ListItem { Text = Ano.ToString("0000"), Value = Ano.ToString() });
            }
        }

        //public static void MsgBox(Page page, string MsgBoxHeader, string MsgBoxText, string TargetUrl)
        //{

        //    //(page.Master.FindControl("lblMsgBoxHeader") as Label).Text = "NN INSCRITO";
        //    //(page.Master.FindControl("lblMsgBoxText") as Label).Text = "ESTE NN YA FUE MARCADO COMO INSCRITO.";
        //    Label lblMsgBoxHeader = (Label)page.Master.FindControl("lblMsgBoxHeader");
        //    lblMsgBoxHeader.Text = MsgBoxHeader;

        //    Label lblMsgBoxText = (Label)page.Master.FindControl("lblMsgBoxText");
        //    lblMsgBoxText.Text = MsgBoxText;

        //    page.Response.Redirect(TargetUrl);

        //}

        public enum Msjtype : int
        {
            Success = 1,
            Info = 2,
            Warning = 3,
            Error = 4
        }

        public static void MsgBox(HtmlGenericControl divMsj, string Mensaje, Msjtype Tipo = Msjtype.Success, string Detalle = "")
        {
            switch (Tipo)
            {
                case Msjtype.Success:
                    divMsj.Attributes["class"] = "rw BarMsj bgSuccess";
                    break;
                case Msjtype.Info:
                    divMsj.Attributes["class"] = "rw BarMsj bgInfo";
                    break;
                case Msjtype.Warning:
                    divMsj.Attributes["class"] = "rw BarMsj bgWarning";
                    break;
                case Msjtype.Error:
                    divMsj.Attributes["class"] = "rw BarMsj bgError";
                    break;
            }

            divMsj.Visible = true;
            Label lblMsj = (Label)divMsj.FindControl("lblMsj");
            lblMsj.Text = Mensaje;

            Label lblMsjDetalle = (Label)divMsj.FindControl("lblMsjDetalle");
            lblMsjDetalle.Text = Detalle;

            Button btnDetalleMsj = (Button)divMsj.FindControl("btnDetalleMsj");
            btnDetalleMsj.Visible = (Detalle.Trim().Length > 0);
        }
      


    }
}
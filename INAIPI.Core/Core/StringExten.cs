﻿using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Linq;
using System.IO;
using Newtonsoft.Json;
using System.Text;

namespace INAIPI.Core
{
    public static class StringExtenIsValidEmail
    {
        private static bool invalid = false;
        public static bool IsValidEmail(this string strIn)
        {
            invalid = false;
            if (String.IsNullOrEmpty(strIn))
                return false;

            // Use IdnMapping class to convert Unicode domain names.
            try
            {
                strIn = Regex.Replace(strIn, @"(@)(.+)$", DomainMapper,
                                      RegexOptions.None, TimeSpan.FromMilliseconds(200));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }

            if (invalid)
                return false;

            // Return true if strIn is in valid e-mail format.
            try
            {
                return Regex.IsMatch(strIn,
                      @"^(?("")("".+?(?<!\\)""@)|(([0-9a-z]((\.(?!\.))|[-!#\$%&'\*\+/=\?\^`\{\}\|~\w])*)(?<=[0-9a-z])@))" +
                      @"(?(\[)(\[(\d{1,3}\.){3}\d{1,3}\])|(([0-9a-z][-\w]*[0-9a-z]*\.)+[a-z0-9][\-a-z0-9]{0,22}[a-z0-9]))$",
                      RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
        }
        private static string DomainMapper(Match match)
        {
            // IdnMapping class with default property values.
            IdnMapping idn = new IdnMapping();

            string domainName = match.Groups[2].Value;
            try
            {
                domainName = idn.GetAscii(domainName);
            }
            catch (ArgumentException)
            {
                invalid = true;
            }
            return match.Groups[1].Value + domainName;
        }
        ////

    }
    public static class StringToCapitalLetters
    {
        private const string ARTICULOS = "el la un una los las unas";
        private const string PREPOSICIONES = " a ante bajo cabe con	contra de desde durante en entre hacia hasta mediante para por según sin so sobre tras versus vía";
        private const string CONTRACCIONES_PREPOSICIONES_ARTICULOS = "al del";
        private const string PRONOMBRE_PERSONALES = "yo me mi nos ustedes tú te ti usted vos os él lo le se sí ella la ello lo ellos ellas los las les se sí";
        private const string PRONOMBRE_PERSONALES_LARGOS = "nosotras conmigo nosotros contigo vosotros vosotras consigo";

        private const string ROMAMONOS_1_AL_10 = "i ii iii iv v vi vii viii ix x";







        public static string ToCapital(this string strIn, bool ExcluirPrepocicionesYArticulos = true)
        {
            if (string.IsNullOrWhiteSpace(strIn))
                return strIn;
            else
            {
                string[] Palabras = strIn.ToLower().Split(' ');
                string resul = string.Empty;

                for (int i = 0; i < Palabras.Length; i++)
                {
                    if (i == 0)
                    {
                        resul += PrimeraLetraMayucual(Palabras[i]);
                    }
                    else
                    {
                        if (ExcluirPrepocicionesYArticulos && Palabras[i].Length == 1)
                        {
                            if (IsNumeroRomano(Palabras[i]))
                            {
                                resul += " " + PrimeraLetraMayucual(Palabras[i]);
                            }
                            else
                            {
                                resul += " " + Palabras[i];
                            }

                        }
                        else if (ExcluirPrepocicionesYArticulos && !IsPrepocicionesOArticulos(Palabras[i]))
                        {
                            resul += " " + PrimeraLetraMayucual(Palabras[i]);
                        }
                        else if (!ExcluirPrepocicionesYArticulos)
                        {
                            resul += " " + PrimeraLetraMayucual(Palabras[i]);
                        }

                        else
                        {
                            resul += " " + Palabras[i];
                        }
                    }
                }

                return resul;

            }
        }


        private static string PrimeraLetraMayucual(string palabra)
        {
            if (string.IsNullOrWhiteSpace(palabra))
                return palabra;
            if (IsNumeroRomano(palabra) || IsAbreviacionOAcronimo(palabra))
            {
                return palabra.ToUpper();
            }

            string result = string.Empty;
            palabra = palabra.ToLower().Trim();
            string letra = palabra.First().ToString().ToUpper();

            result = letra + palabra.Substring(1, palabra.Length - 1);
            return result;
        }


        private static bool IsPrepocicionesOArticulos(string palabra)
        {
            string rti = ARTICULOS;
            rti += " " + PREPOSICIONES;
            rti += " " + CONTRACCIONES_PREPOSICIONES_ARTICULOS;
            rti += " " + PRONOMBRE_PERSONALES;
            rti += " " + PRONOMBRE_PERSONALES_LARGOS;

            return rti.Split(' ').Any(p => p == palabra.ToLower());

        }

        private static bool IsNumeroRomano(string palabra)
        {
            string rti = ROMAMONOS_1_AL_10;
            return rti.Split(' ').Any(p => p == palabra.ToLower());
        }
        private static bool IsAbreviacionOAcronimo(string palabra)
        {
            if (palabra == "caipi" || palabra == "cafi" || palabra == "pbfc" || palabra == "inaipi" || palabra == "fee" || palabra == "(fee)")
            {
                return true;
            }
            else return false;

        }
    }

    public static class StringToEllipsisInText
    {
        public static string ToEllipsis(this string strIn, int Length = 50)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(strIn) && strIn.Length > Length)
            {
                result = strIn.Substring(0, Length) + "...";
            }
            else result = strIn;

            return result;
        }
    }

    public static class StringToOtroTipo
    {

        public static bool? ToBoolNuleable(this string strIn)
        {
            if (strIn.ToLower().Trim() == "si"
                || strIn.ToLower().Trim() == "1")
            {
                return true;
            }
            else if (strIn.ToLower().Trim() == "no"
                || strIn.ToLower().Trim() == "0")
            {
                return false;
            }
            else
            {
                return null;
            }
        }

        public static int ToInt(this string strIn)
        {
            int.TryParse(strIn, out int result);
            return result;
        }
        public static int? ToIntNuleable(this string strIn)
        {
            if (int.TryParse(strIn, out int result))
            {
                return result;
            }
            else return null;
        }

        public static decimal? ToDecimalNuleable(this string strIn)
        {
            if (decimal.TryParse(strIn, out decimal result))
            {
                return result;
            }
            else return null;
        }
    }

    public static class StringIsValidPhone
    {
        public static bool IsValidPhoneNumber(this string strIn)
        {
            if (long.TryParse(strIn, out long numero))
            {
                return strIn.Length == 10;
            }
            else
            {
                return false;
            }
        }
    }

    public static class BoolExten
    {
        public static int? ToIntNuleable(this bool? strIn)
        {
            if (strIn == true)
            {
                return 1;
            }
            else if (strIn == false)
            {
                return 0;
            }
            else
            {
                return null;
            }
        }
    }

    public static class StrJsonExten
    {
        public static T ToObject<T>(this string strIn)
        {
            return JsonConvert.DeserializeObject<T>(strIn);
        }
    }

    public static class StringExtensions
    {
        public static string SinTildes(this string texto) =>
            new String(
                texto.Normalize(NormalizationForm.FormD)
                .Where(c => CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark)
                .ToArray()
            )
            .Normalize(NormalizationForm.FormC);
    }

    public static class LevenshteinDistance
    {
        /// <summary>
        /// Determina el porcentaje de similitud entre 2 cadenas de caractares
        /// </summary>
        /// <param name="s"></param>
        /// <param name="t"></param>
        /// <returns></returns>
        public static decimal Similitud(this string s, string t)
        {
            s =  (s ?? "").ToLower().Trim().SinTildes();
            t = (t ?? "").ToLower().Trim().SinTildes();
            int n = s.Length;
            int m = t.Length;
            
            int[,] d = new int[n + 1, m + 1];
           
            // Step 1
            if (n == 0)
            {
                return m;
            }

            if (m == 0)
            {
                return n;
            }

            // Step 2
            for (int i = 0; i <= n; d[i, 0] = i++)
            {
            }

            for (int j = 0; j <= m; d[0, j] = j++)
            {
            }

            // Step 3
            for (int i = 1; i <= n; i++)
            {
                //Step 4
                for (int j = 1; j <= m; j++)
                {
                    // Step 5
                    int cost = (t[j - 1] == s[i - 1]) ? 0 : 1;

                    // Step 6
                    d[i, j] = Math.Min(
                        Math.Min(d[i - 1, j] + 1, d[i, j - 1] + 1),
                        d[i - 1, j - 1] + cost);
                }
            }
            // Step 7

            decimal max = Math.Max(n, m).ToString().ToDecimalNuleable().Value;
            int l = d[n, m];
            decimal resul = Math.Round(((max-l) / max) * 100, 2);
            return resul;
        }
    }

    public static class NullableDateTimeStringFormatter
    {
        public static string NullableDateTimeToString(this DateTime? fecha, string formato)
        {
            if (fecha == null)
            {
                return "N/A";
            }
            else
            {
                return ((DateTime)fecha).ToString(formato);
            }
        }
    }
}
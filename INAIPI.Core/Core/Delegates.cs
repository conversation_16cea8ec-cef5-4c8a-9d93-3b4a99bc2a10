﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public delegate void ResultadoEventHandler(object sender, ResultadoEventArgs e);
    public delegate Resultado ResultadoEventHandlerBool(object sender, ResultadoEventArgs e);
    public delegate void ResultadoEventHandlerCierre(object sender, ResultadoEventArgs e);
    public delegate void ResultadoEventHandler<T>(object sender, ResultadoEventArgs<T> e) where T : class;

    public class ResultadoEventArgs : EventArgs
    {
        public Resultado Resultado { set; get; }
        public Resultado ResultadoApi { set; get; } = new Resultado();
    }
    public class ResultadoEventArgs<T> : EventArgs where T : class
    {
        public Resultado<T> Resultado { set; get; }
    }
}

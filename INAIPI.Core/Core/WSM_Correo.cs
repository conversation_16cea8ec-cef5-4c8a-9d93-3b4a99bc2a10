﻿using INAIPI.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace IntranetApp.Models.COMUN
{
    [Serializable]
    [Dapper.Contrib.Extensions.Table("WSM_Correo")]
    public class WSM_Correo : BaseModel
    {
        [Dapper.Contrib.Extensions.ExplicitKey]
        public int IdCorreo { get; set; }
        public decimal? IdUsuario { get; set; }
        public string Titulo { get; set; }
        public string Cuerpo { get; set; }
        public DateTime FechaSistema { get; set; }
        public byte IdEstado { get; set; }
        public string ErrorNoEnvio { get; set; }
        public DateTime? FechaEnvio { get; set; }
        public byte IdMetodoEnvio { get; set; }
        public byte IdCorreoOrigen { get; set; }
        public DateTime? FechaUltimoIntento { get; set; }
        public string PageId { get; set; }
        public int NoIntentos { get; set; }

        [Dapper.Contrib.Extensions.Write(false)]
        public List<WSM_CorreoDestinatarios> Destinatarios { get; set; }

        [Dapper.Contrib.Extensions.Write(false)]
        public WSM_CorreoOrigen Origen { get; set; }

    }
}
﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public static class BaseModelExten
    {
        public static DataTable ToDataTable<T>(this T Model, bool PropHeredadas = false) where T : BaseModel
        {
            List<T> list = new List<T>();
            list.Add(Model);

            DataTable dt = list.ToDataTablet(PropHeredadas);
            
            return dt;
        }
    }
}

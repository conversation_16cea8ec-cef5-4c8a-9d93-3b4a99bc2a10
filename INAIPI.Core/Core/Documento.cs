﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    [Serializable]
    public class Documento
    {
        public Documento()
        {
            TodoBien = true;
        }
        public Documento(string Ruta)
        {
            if (!System.IO.File.Exists(Ruta))
            {
                this.TodoBien = false;
                this.strError = "EL DOCUMENTO NO EXISTE EN ESTA RRUTA";
            }
            else
            {
                this.Nombre = System.IO.Path.GetFileName(Ruta);
                this.RutaFisica = Ruta;
                this.Archivo = System.IO.File.ReadAllBytes(this.Nombre);
                this.RutaFisica = Ruta;
                this.strError = string.Empty;
            }

        }
        public bool Existe { get; set; }
        public string Nombre { get; set; }
        public string Extension { get { return System.IO.Path.GetExtension(Nombre); } }
        public float Size { get; set; }
        public byte[] Archivo { get; set; }
        public string RutaFisica { get; set; }
        public string RutaVirtual { get; set; }
        public string strError { get; set; }
        public bool TodoBien { get; set; }
        public bool UrlEsValida()
        {
            if (RutaFisica != "")
            {
                return System.IO.File.Exists(RutaFisica);
            }
            else
            {
                return false;
            }

        }
        public bool EsValido()
        {
            if (Archivo != null && Nombre != "" && Extension != "")
            {
                return true;
            }
            else
            {
                return false;
            }
        }
       
    }
}

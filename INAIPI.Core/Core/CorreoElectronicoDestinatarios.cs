﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public class CorreoElectronicoDestinatarios
    {
        public CorreoElectronicoDestinatarios()
        {
            EnviarComo = ModoEnvio.PARA;
        }
        public enum ModoEnvio { PARA = 1, COPIA = 2, COPIA_OCULTA = 3 }
        public ModoEnvio EnviarComo { set; get; }
        public string CorreoElectronico { set; get; }
    }
}

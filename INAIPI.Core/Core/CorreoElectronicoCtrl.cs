﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public class CorreoElectronicoCtrl
    {
        [Obsolete("Este método no utiliza el web api para enviar los correos, por tanto los correos son propensos a perderse en caso de enviarse", true)]
        public Resultado EnviarCorreo(CorreoElectronico correo)
        {
            Resultado result = new Resultado();

            SmtpClient smtpClient = new SmtpClient();
            NetworkCredential basicCredential = new NetworkCredential(correo.UsuarioEnvia, correo.ContrasenaUsuarioEnvia, correo.host);
            MailMessage message = new MailMessage();
            MailAddress fromAddress = new MailAddress(correo.UsuarioEnvia);

            smtpClient.Host = correo.host;
            smtpClient.UseDefaultCredentials = correo.UseDefaultCredentials;
            smtpClient.Credentials = basicCredential;
            smtpClient.Port = 587;
            smtpClient.EnableSsl = true;

            message.From = fromAddress;
            message.Subject = correo.Asunto;
            message.IsBodyHtml = correo.CuerpoIsHtml;
            if (correo.HtmlView != null)
            {
                message.AlternateViews.Add(correo.HtmlView);
            }
            else
            {
                message.Body = correo.Cuerpo;
            }

            if (correo.Para != null)
            {
                foreach (string item in correo.Para)
                {
                    message.To.Add(item);
                }
            }

            if (correo.Copia != null)
            {
                foreach (string item in correo.Copia)
                {
                    message.CC.Add(item);
                }
            }

            if (correo.CopiaOculta != null)
            {
                foreach (string item in correo.CopiaOculta)
                {
                    message.Bcc.Add(item);
                }
            }


            if (correo.CopiaOculta != null && correo.Copia != null && correo.Para != null)
            {
                try
                {
                    smtpClient.Send(message);
                    result.TodoBien = true;

                }
                catch (Exception ex)
                {
                    result.TodoBien = false;
                    result.strError = ex.Message;
                }
            }
            else
            {
                result.TodoBien = false;
                result.strError = "NO SE SUMINISTRARON DESTINATARIOS PARA EL CORREO";
            }


            return result;
        }
        public Resultado EnviarCorreo(CorreoElectronico correo, bool Appi = false)
        {
            Resultado rst = new Resultado();

            // Prueba
            //string apiUrl = "http://sigepi.inaipi.gob.do:88/api/v2/Mail";
            // Producción
            string apiUrl = "http://sigepi.inaipi.gob.do:89/api/v2/Mail";
            //string apiUrl = "http://sigepi.inaipi.gob.do:89/api/Mail/Post";
            //string apiUrl = "http://localhost:58170/api/v2/Mail";
            //string apiUrl = "http://localhost:58170/api/Mail/Post";

            string inputJson = correo.ToJson();

            WebClient client = new WebClient();
            client.Headers["Content-type"] = "application/json";
            client.Encoding = Encoding.UTF8;

            string result = "";
            try
            {
                Uri uri = new Uri(apiUrl);
                //result = client.UploadStringAsync(uri, "POST", inputJson);
                Task.Run(async () => { result = await client.UploadStringTaskAsync(uri, "POST", inputJson); });
            }
            catch (Exception ex)
            {
                result = ex.Message;
            }

            if (result == "" || result == "\"ok\"")
            {
                if (result == "")
                {
                    rst = new Resultado(true, "Correo en proceso de envío", correo, true);
                }
                else
                {
                    rst = new Resultado(true, "Correo Enviado Exitosamente", correo, true);
                }
            }
            else
            {
                rst = new Resultado(false, result, correo);
            }

            return rst;
        }

        public Resultado EnviarCorreoVacaciones(CorreoElectronico correo, bool Appi = false)
        {
            Resultado rst = new Resultado();
            bool isException = false;

            // Prueba
            //string apiUrl = "http://sigepi.inaipi.gob.do:88/api/v2/Mail";
            // Producción
            string apiUrl = "http://sigepi.inaipi.gob.do:89/api/v2/Mail";
            //string apiUrl = "http://sigepi.inaipi.gob.do:89/api/Mail/Post";
            //string apiUrl = "http://localhost:58170/api/v2/Mail";
            //string apiUrl = "http://localhost:58170/api/Mail/Post";

            string inputJson = correo.ToJson();

            WebClient client = new WebClient();
            client.Headers["Content-type"] = "application/json";
            client.Encoding = Encoding.UTF8;

            string result = "";
            try
            {
                Uri uri = new Uri(apiUrl);
                //result = client.UploadStringAsync(uri, "POST", inputJson);

                Task.Run(async () =>
                {
                    result = await client.UploadStringTaskAsync(uri, "POST", inputJson);
                }).Wait();
            }
            catch (Exception ex)
            {
                isException = true;
                result = ex.Message;
            }

            if (!isException && (result == "" || result.Length > 0))
            {
                IntranetApp.Models.COMUN.WSM_Correo correoGuardado = result.ToObject<IntranetApp.Models.COMUN.WSM_Correo>();
                if (result == "")
                {
                    rst = new Resultado(true, "Correo en proceso de envío", correoGuardado, true);
                }
                else
                {
                    rst = new Resultado(true, "Correo Enviado Exitosamente", correoGuardado, true);
                }
            }
            else
            {
                rst = new Resultado(false, result, correo);
            }

            return rst;
        }

        public string EnmarcarCorreo(string Titulo, string Cuerpo)
        {
            string correo = $"<!DOCTYPE html PUBLIC '-//W3C//DTD XHTML 1.0 Transitional //EN' 'http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd'><html xmlns='http://www.w3.org/1999/xhtml' xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office'><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8' /> <style type='text/css'>  body, .mainTable {{ height:100% !important; width:100% !important; margin:0; padding:0; {{  img, a img {{ border:0; outline:none; text-decoration:none; {{  .imageFix {{ display:block; {{  table, td {{ border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;{{  p {{margin-top:0; margin-right:0; margin-left:0; padding:0;{{  .ReadMsgBody{{width:100%;{{ .ExternalClass{{width:100%;{{.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {{ line-height: 100% ; {{ img {{ -ms-interpolation-mode: bicubic; {{ body, table, td, p, a, li, blockquote {{ -ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; {{ </style><style>table{{ border-collapse: collapse; {{@media only screen and (max-width: 600px) {{ body[yahoo].rimg {{ max-width: 100%; height: auto; {{ body[yahoo].rtable {{width: 100%!important;table-layout: fixed; {{ body[yahoo].rtable tr {{ height: auto!important; {{ {{ </style><!--[if gte mso 9]><xml>  <o:OfficeDocumentSettings> <o: AllowPNG / ><o: PixelsPerInch > 96</o:PixelsPerInch>  </o: OfficeDocumentSettings ></xml><![endif]--></head ><body yahoo = fix scroll = 'auto' style='padding:0; margin:0; FONT-SIZE: 12px; FONT-FAMILY: Arial, Helvetica, sans-serif; cursor:auto; background:#F3F3F3'><TABLE class='rtable mainTable' cellSpacing=0 cellPadding=0 width='100%' bgColor=#f3f3f3> <TR ><TD style = 'FONT-SIZE: 0px; HEIGHT: 20px; LINE-HEIGHT: 0'>&#160;</TD></TR><TR><TD vAlign = top ><TABLE class = rtable style = 'WIDTH: 600px; MARGIN: 0px auto' cellSpacing=0 cellPadding=0 width=600 align=center border=0> <TR ><TD style = 'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: medium none; PADDING-RIGHT: 0px; BACKGROUND-COLOR: transparent'> <TABLE class = rtable style = 'WIDTH: 100%' cellSpacing=0 cellPadding=0 align=left> <TR style = 'HEIGHT: 10px'><TD style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 35px; TEXT-ALIGN: center; PADDING-TOP: 35px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent'><TABLE cellSpacing=0 cellPadding=0 align=center border=0><TR> <TD style = 'PADDING-BOTTOM: 2px; PADDING-TOP: 2px; PADDING-LEFT: 2px; PADDING-RIGHT: 2px' align=center><TABLE cellSpacing=0 cellPadding=0 border=0><TR><TD style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; BACKGROUND-COLOR: transparent'><IMG class=rimg style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; DISPLAY: block; BACKGROUND-COLOR: transparent' border=0 src='http://app.inaipi.gob.do:97/solicitudes/images/Image_1.png' width=263 height=64 hspace='0' vspace='0'></TD></TR></TABLE></TD></TR></TABLE></TD></TR></TABLE></TD></TR><TR> <TD style = 'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; PADDING-TOP: 1px; PADDING-LEFT: 0px; BORDER-LEFT: medium none; PADDING-RIGHT: 0px; BACKGROUND-COLOR: transparent'><TABLE class=rtable style='WIDTH: 100%' cellSpacing=0 cellPadding=0 align=left><TR style='HEIGHT: 10px'><TD style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; TEXT-ALIGN: center; PADDING-TOP: 1px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent'><P style='FONT-SIZE: 18px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #0070cd; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=center><STRONG>Instituto Nacional de Atenci&#243;n Integral a la Primera Infancia</STRONG><BR><FONT style='FONT-SIZE: 14px; COLOR: #ffa300'><STRONG>&#161;Ser ni&#241;o y&#160;ni&#241;a nunca fue mejor&#160;!</STRONG></FONT><BR></P></TD></TR></TABLE></TD></TR> <TR ><TD style = 'BORDER-TOP: #dbdbdb 1px solid; BORDER-RIGHT: #dbdbdb 1px solid; BORDER-BOTTOM: #dbdbdb 1px solid; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: #dbdbdb 1px solid; PADDING-RIGHT: 0px; BACKGROUND-COLOR: #feffff'><TABLE class=rtable style='WIDTH: 100%' cellSpacing=0 cellPadding=0 align=left><TR style='HEIGHT: 10px'><TD style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 35px; TEXT-ALIGN: center; PADDING-TOP: 35px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: #feffff'><P style='FONT-SIZE: 18px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #a8a7a7; LINE-HEIGHT: 155%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=center><STRONG><h1>{Titulo}</h1></STRONG></P> <P style = 'FONT-SIZE: 12px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #a7a7a7; LINE-HEIGHT: 155%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=center></h1> <p>{Cuerpo}</p></TD></TR></TABLE></TD></TR><TR> <TD style = 'BORDER-TOP: #dbdbdb 1px solid; BORDER-RIGHT: #dbdbdb 1px solid; BORDER-BOTTOM: #dbdbdb 1px solid; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: #dbdbdb 1px solid; PADDING-RIGHT: 0px; BACKGROUND-COLOR: #feffff'><TABLE class = rtable style = 'WIDTH: 100%' cellSpacing=0 cellPadding=0 align=left><TR style='HEIGHT: 10px'> <TD style = 'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; TEXT-ALIGN: center; PADDING-TOP: 1px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent'> <P style = 'FONT-SIZE: 4px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=left>&#160;</P><P style='FONT-SIZE: 12px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=right>Sistema de Informaci&#243;n &#169; {DateTime.Now.Year}<BR>Departamento de Desarrollo e Implementaci&#243;n de Sistemas, TIC </P></TD></TR></TABLE></TD></TR></TABLE></TD></TR><TR> <TD style = 'FONT-SIZE: 0px; HEIGHT: 8px; LINE-HEIGHT: 0'>&#160;</TD></TR></TABLE></body></html></TD></TR><TR> <TD style = 'FONT-SIZE: 0px; HEIGHT: 8px; LINE-HEIGHT: 0'>&#160;</TD></TR></TABLE></body></html>";

            return correo;
        }
    }


}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Runtime.Serialization.Formatters.Binary;
using System.IO;

namespace INAIPI.Core
{
    public static class ResultadoExten
    {
        public static void LogWindows(this Resultado Resultado,string Origen = "SI_INAIPI", string NombreRegistro = "RESULTADO")
        {
            if (!Resultado.EscritoEnElLog)
            {
                //if (!EventLog.SourceExists(Origen))
                //    EventLog.CreateEventSource(Origen, NombreRegistro);
                
                //EventLogEntryType logType = EventLogEntryType.Information;
                //if (Resultado.TodoBien)
                //{
                //    logType = EventLogEntryType.SuccessAudit;
                //}
                //else
                //{
                //    logType = EventLogEntryType.Error;
                //}

                // + Environment.NewLine + Resultado.ID?.ToJson()
                // , ObjectToByteArray(Resultado.ID.ToJson())
                //EventLog.WriteEntry(Origen, Resultado.strError, logType, 1, 1 );

                Resultado.EscritoEnElLog = true;
            }
        }

        private static byte[] ObjectToByteArray(object obj)
        {
            if (obj == null)
                return null;
            BinaryFormatter bf = new BinaryFormatter();
            using (MemoryStream ms = new MemoryStream())
            {
                bf.Serialize(ms, obj);
                return ms.ToArray();
            }
        }
    }
}

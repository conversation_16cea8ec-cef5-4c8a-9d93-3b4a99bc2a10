﻿using INAIPI.DataAcces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public abstract class BaseDA<T> where T : BaseModel
    {
        public BaseDA(IDA Contex)
        {
            get_NameTable();
            this.Contex = Contex;
            this.DA = Contex;
        }
        private string _NameTable;
        protected IDA Contex { set; get; }
        protected IDA DA { set; get; }
        private void get_NameTable()
        {
            var Atributo = typeof(T).CustomAttributes.FirstOrDefault(g => g.AttributeType == typeof(System.ComponentModel.DataAnnotations.Schema.TableAttribute));
            if (Atributo != null)
            {
                _NameTable = Atributo.ConstructorArguments.FirstOrDefault().Value.ToString();
            }
        }
        private List<string> PropiedadesMapeables()
        {
            List<string> result = new List<string>();
            DataTable Table = Contex.GetSchema(_NameTable);
            foreach (DataRow Columna in Table.Rows)
            {
                string ColumnName = Columna["COLUMN_NAME"].ToString();
                PropertyInfo propie = typeof(T).GetProperty(ColumnName);
                if (propie != null)
                {
                    result.Add(ColumnName);
                }

            }
            return result;
        }

        private System.Collections.Generic.List<System.Data.SqlClient.SqlParameter> getParameterfromModel(T Model)
        {
            List<SqlParameter> Parametros = new List<SqlParameter>();
            foreach (string PropertiName in PropiedadesMapeables())
            {
                PropertyInfo propertyInfo = Model.GetType().GetProperty(PropertiName);
                if (propertyInfo != null)
                {
                    object valor = propertyInfo.GetValue(Model);

                    if (valor is string && valor != null)
                    {
                        valor = (valor as string).ToUpper();
                    }

                    SqlParameter Sqlparametro = new SqlParameter("@" + PropertiName, valor);

                    var atributo = propertyInfo.CustomAttributes.FirstOrDefault(g => g.AttributeType == typeof(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute));
                    if (atributo != null)
                    {
                        Sqlparametro.Direction = ParameterDirection.InputOutput;
                    }
                    Parametros.Add(Sqlparametro);

                }
            }
            return Parametros;
        }
        protected virtual List<T> Get(string NameProcedure)
        {
            return Contex.EjecutarQuery<T>(NameProcedure).ToList();
        }
        protected virtual List<T> Get(string NameProcedure, System.Collections.Generic.List<System.Data.SqlClient.SqlParameter> Parametros)
        {
            return Contex.EjecutarQuery<T>(NameProcedure, CommandType.StoredProcedure, Parametros).ToList();
        }
        protected virtual string Guardar(T Model, string NameProcedure, SqlTransaction transaccion = null)
        {
            List<SqlParameter> Parametros = getParameterfromModel(Model);
            return Guardar(Model, NameProcedure, Parametros,IncludePropertyModelInParameters:false, transaccion:transaccion);
        }
        protected virtual string Guardar(T Model, string NameProcedure, System.Collections.Generic.List<System.Data.SqlClient.SqlParameter> Parametros ,bool IncludePropertyModelInParameters = true, SqlTransaction transaccion = null)
        {
            if (IncludePropertyModelInParameters)
            {
                 List<SqlParameter> Paramet = getParameterfromModel(Model);
                 if (Parametros != null )
                 {
                     Parametros.AddRange(Paramet);
                 }
            }
            bool isCloseConecction;
            SqlConnection coneccion;
            if (transaccion == null)
            {
                coneccion = Contex.Conectar();
                transaccion = coneccion.BeginTransaction();
                isCloseConecction = true;
            }
            else
            {
                coneccion = transaccion.Connection;
                isCloseConecction = false;
            }

            Contex.EjecutarQuery(NameProcedure, System.Data.CommandType.StoredProcedure, Parametros, coneccion, transaccion);

            if (Contex.strError.Length == 0)
            {
                foreach (SqlParameter item in Parametros.Where(t => t.Direction == ParameterDirection.Output || t.Direction == ParameterDirection.InputOutput))
                {
                    string name = item.ParameterName.Replace("@", "");

                    PropertyInfo pInfo = Model.GetType().GetProperty(name);
                    pInfo.SetValue(Model, item.Value);
                }
            }

            if (Contex.strError.Length > 0 && isCloseConecction)
            {
                transaccion.Rollback();

            }
            else if (Contex.strError.Length == 0 && isCloseConecction)
            {
                transaccion.Commit();
            }
            if (isCloseConecction)
            {
                transaccion.Dispose();
                coneccion.Close();
                coneccion.Dispose();
            }
            return Contex.strError;

        }
    }

}

﻿using INAIPI.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace IntranetApp.Models.COMUN
{
    [Serializable]
    [Dapper.Contrib.Extensions.Table("WSM_CorreoDestinatarios")]
    public class WSM_CorreoDestinatarios : BaseModel
    {
        [Dapper.Contrib.Extensions.ExplicitKey]
        public int IdCorreo { get; set; }
        [Dapper.Contrib.Extensions.ExplicitKey]
        public byte IdTipoDestinatario { get; set; }
        public string CorreoDestino { get; set; }
        public int IdDestinatario { get; set; }

        [Dapper.Contrib.Extensions.Write(false)]
        public WSM_CorreoTipoDestinatario Tipo { get; set; }


        [Serializable]
        [Dapper.Contrib.Extensions.Table("WSM_CorreoTipoDestinatario")]
        public class WSM_CorreoTipoDestinatario : BaseModel
        {
            [Dapper.Contrib.Extensions.ExplicitKey]
            public byte IdTipoDestinatario { get; set; }
            public string TipoDestinatario { get; set; }
        }
    }
}
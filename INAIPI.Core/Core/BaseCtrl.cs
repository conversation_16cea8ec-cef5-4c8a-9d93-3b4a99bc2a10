﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public class BaseCtrl
    {
        #region VALIDAR CORREO
        bool CorreoValido = false;
        public bool EsValidoElCorreo(string strIn)
        {
            CorreoValido = false;
            if (String.IsNullOrEmpty(strIn))
                return false;

            // Use IdnMapping class to convert Unicode domain names.
            try
            {
                strIn = System.Text.RegularExpressions.Regex.Replace(strIn, @"(@)(.+)$", this.DomainMapper,
                                      System.Text.RegularExpressions.RegexOptions.None, TimeSpan.FromMilliseconds(200));
            }
            catch (System.Text.RegularExpressions.RegexMatchTimeoutException)
            {
                return false;
            }

            if (CorreoValido)
                return false;

            // Return true if strIn is in valid e-mail format.
            try
            {
                return System.Text.RegularExpressions.Regex.IsMatch(strIn,
                      @"^(?("")("".+?(?<!\\)""@)|(([0-9a-z]((\.(?!\.))|[-!#\$%&'\*\+/=\?\^`\{\}\|~\w])*)(?<=[0-9a-z])@))" +
                      @"(?(\[)(\[(\d{1,3}\.){3}\d{1,3}\])|(([0-9a-z][-\w]*[0-9a-z]*\.)+[a-z0-9][\-a-z0-9]{0,22}[a-z0-9]))$",
                      System.Text.RegularExpressions.RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            }
            catch (System.Text.RegularExpressions.RegexMatchTimeoutException)
            {
                return false;
            }
        }
        private string DomainMapper(System.Text.RegularExpressions.Match match)
        {
            // IdnMapping class with default property values.
            System.Globalization.IdnMapping idn = new System.Globalization.IdnMapping();

            string domainName = match.Groups[2].Value;
            try
            {
                domainName = idn.GetAscii(domainName);
            }
            catch (ArgumentException)
            {
                CorreoValido = true;
            }
            return match.Groups[1].Value + domainName;
        }
        #endregion
    }
}

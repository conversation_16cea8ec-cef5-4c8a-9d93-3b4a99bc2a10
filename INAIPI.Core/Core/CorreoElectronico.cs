﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    [Serializable]
    public class CorreoElectronico : BaseModel
    {
        public CorreoElectronico()
        {
            UsuarioEnvia = "<EMAIL>";
            ContrasenaUsuarioEnvia = "SystemM@iler";
            Puerto = 587;
            host = "smtp.office365.com";
            EnableSsl = true;
            UseDefaultCredentials = false;
            CuerpoIsHtml = true;
            IdCorreoOrigen = 1;
            EnviaCorreo = true;

            Para = new List<string>();
            Copia = new List<string>();
            CopiaOculta = new List<string>();
            Adjuntos = new List<CorreoElectronicoAdjuntos>();
        }
        public List<string> Para { get; set; }
        public List<string> Copia { get; set; }
        public List<string> CopiaOculta { get; set; }
        public string UsuarioEnvia { set; get; }
        public string ContrasenaUsuarioEnvia { set; get; }
        public string Asunto { set; get; }
        public string Cuerpo { set; get; }
        public bool CuerpoIsHtml { set; get; }
        public string host { get; set; }
        public bool UseDefaultCredentials { get; set; }
        public int Puerto { get; set; }
        public bool EnableSsl { get; set; }
        public byte IdCorreoOrigen { get; set; }
        public decimal IdUsuario { get; set; }
        public decimal? IdColaborador { get; set; }
        public string PageId { get; set; }
        public string Mensaje { get; set; }
        public AlternateView HtmlView { get; set; }
        public List<CorreoElectronicoAdjuntos> Adjuntos { get; set; }
        public bool EnviaNotificacion { get; set; }
        public bool EnviaCorreo { get; set; }
        public List<CorreoElectronicoDestinatarios> Destinatarios { get; set; }
        [Dapper.Contrib.Extensions.Write(false)]
        public string Link { get; set; }
        public string PoliticaAN { get; set; }
        public decimal IdAN { get; set; }
        public string Ids { get; set; }
    }

    [Serializable]
    [Dapper.Contrib.Extensions.Table("QEC_WSM_Adjuntos")]
    public class CorreoElectronicoAdjuntos : BaseModel
    {
        [Dapper.Contrib.Extensions.ExplicitKey]
        public int IdAdjunto { get; set; }
        public int IdCorreo { get; set; }
        public int IdOrden { get; set; }
        public string Nombre { get; set; }        
        public byte[] Archivo { get; set; }
        [Dapper.Contrib.Extensions.Write(false)]
        public string ArchivoStr
        {
            get
            {
                if (Archivo != null)
                {
                    return Convert.ToBase64String(Archivo, 0, Archivo.Length);
                }
                else
                {
                    return "";
                }
            }
        }
        public double Tamano { get; set; }

        [Dapper.Contrib.Extensions.Write(false)]
        public string FileName { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core.Annotations
{
  public  class SPName:System.Attribute
    {
        public SPName( SPOperacion Operacion, string Name)
        {
            this.Operacion = Operacion;
            this.Name = Name;
        }

        string Name { set; get; }
        SPOperacion Operacion { set; get; }
    }
  public  enum SPOperacion
    {
        Guardar
    }
}

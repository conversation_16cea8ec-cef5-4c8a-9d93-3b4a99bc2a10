﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INAIPI.Core
{
    public static class NFCExten
    {
        public static bool IsValidNCF(this string strIn)
        {
            bool Bien = true;

            if (strIn == "")
            {
                return false;
            }
            else
            {
                if (strIn.Length < 11 || strIn.Length > 19)
                {
                    return false;
                }
            }
            

            if (strIn.Length > 11)
            {
                int Tipo = 0;

                if (strIn.Length == 19 || strIn.Length == 13)
                {
                    int.TryParse(strIn.Substring(1, 2), out int TipoE);
                    int.TryParse(strIn.Substring(9, 2), out int TipoA);
                    if (TipoE != 0 || TipoA != 0)
                    {
                        string tipo = strIn.Substring(0, 1);
                        if (tipo == "A")
                        {
                            if (TipoA != 15)
                            {
                                return false;
                            }
                        }
                        else if (tipo == "E")
                        {
                            if (TipoE != 45)
                            {
                                return false;
                            }
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }

            return Bien;
        }
    }
}

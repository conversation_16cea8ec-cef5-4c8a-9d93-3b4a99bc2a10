﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;

namespace INAIPI.Core
{
    public class FileServerCtrl
    {
        /// <summary>
        /// Metodo Constructor del FileServerCTRL
        /// </summary>
        /// <param name="FSN">Ruta del Directorio Virtual del FileServer Mapeada</param>
        /// <param name="PF">Ruta del Folder Donde Se Está Trabajando Dentro del FileServer</param>
        public FileServerCtrl(string FSN, string PF = "")
        {
            MapFileServerName = FSN;
            PathFolder = PF;
        }

        public string MapFileServerName { get; set; }
        public string PathFolder { get; set; }

        private string AddSlash(string URL)
        {
            if (!(URL.Substring(URL.Length - 1, 1) == "/"))
            {
                URL += "/";
            }

            return URL;
        }

        private Resultado VerifyPath(string Folder = "")
        {
            try
            {
                if (Folder.Length == 0)
                {
                    Folder = PathFolder;
                }

                if (!Directory.Exists(MapFileServerName))
                {
                    return new Resultado(false, "NO SE HA CREADO ESTE DIRECTORIO VIRTUAL.");
                }

                string[] Folders = Folder.Split('/');
                string Path = MapFileServerName;
                foreach (string Fold in Folders)
                {
                    if (Fold.Trim().Length > 0)
                    {
                        Path += "/" + Fold;

                        if (!Directory.Exists(Path))
                        {
                            Directory.CreateDirectory(Path);
                        }
                    }
                }

                //if (!Directory.Exists(MapFileServerName + Folder))
                //{
                //    Directory.CreateDirectory(MapFileServerName + Folder);
                //}

                return new Resultado(true);
            }
            catch (Exception ex)
            {
                return new Resultado(false, ex.Message);
            }

        }

        public Resultado GuardarArchivo(string FileName, byte[] Archivo, string Folder = "")
        {
            try
            {
                if (Folder.Length == 0)
                {
                    Folder = PathFolder;
                }

                Resultado r = VerifyPath(Folder);
                if (!r.TodoBien)
                {
                    return r;
                }

                r = null;

                MapFileServerName = AddSlash(MapFileServerName);
                Folder = AddSlash(Folder);

                File.WriteAllBytes(MapFileServerName + Folder + FileName, Archivo);

                return new Resultado(true);
            }
            catch (Exception ex)
            {
                return new Resultado(false, ex.Message);
            }
        }

        public Resultado EliminarArchivo(string FileName, string Folder = "")
        {
            try
            {
                if (Folder.Length == 0)
                {
                    Folder = PathFolder;
                }

                Resultado r = VerifyPath(Folder);
                if (!r.TodoBien)
                {
                    return r;
                }

                MapFileServerName = AddSlash(MapFileServerName);
                Folder = AddSlash(Folder);

                File.Delete(MapFileServerName + Folder + FileName);

                return new Resultado(true);
            }
            catch (Exception ex)
            {
                return new Resultado(false, ex.Message);
            }
        }

        public Resultado CopiarArchivo(string FileNameSource, string FolderTarget, string FolderSource = "", string FileNameTarget = "", bool OverWrite = false)
        {
            try
            {
                if (FolderSource.Length == 0)
                {
                    FolderSource = PathFolder;
                }

                if (FileNameTarget.Length == 0)
                {
                    FileNameTarget = FileNameSource;
                }

                Resultado r = VerifyPath(FolderSource);
                if (!r.TodoBien)
                {
                    return r;
                }

                r = VerifyPath(FolderTarget);
                if (!r.TodoBien)
                {
                    return r;
                }

                MapFileServerName = AddSlash(MapFileServerName);
                FolderSource = AddSlash(FolderSource);
                FolderTarget = AddSlash(FolderTarget);

                File.Copy(MapFileServerName + FolderSource + FileNameSource, MapFileServerName + FolderTarget + FileNameTarget, OverWrite);

                return new Resultado(true);
            }
            catch (Exception ex)
            {
                return new Resultado(false, ex.Message);
            }
        }

        public Resultado MoverArchivo(string FileNameSource, string FolderTarget, string FolderSource = "", string FileNameTarget = "")
        {
            try
            {
                if (FolderSource.Length == 0)
                {
                    FolderSource = PathFolder;
                }

                if (FileNameTarget.Length == 0)
                {
                    FileNameTarget = FileNameSource;
                }

                Resultado r = VerifyPath(FolderSource);
                if (!r.TodoBien)
                {
                    return r;
                }

                r = VerifyPath(FolderTarget);
                if (!r.TodoBien)
                {
                    return r;
                }

                MapFileServerName = AddSlash(MapFileServerName);
                FolderSource = AddSlash(FolderSource);
                FolderTarget = AddSlash(FolderTarget);

                File.Move(MapFileServerName + FolderSource + FileNameSource, MapFileServerName + FolderTarget + FileNameTarget);

                return new Resultado(true);
            }
            catch (Exception ex)
            {
                return new Resultado(false, ex.Message);
            }
        }

        public Resultado EliminarDirectorio(string Folder, bool Recursivo = false)
        {
            try
            {
                MapFileServerName = AddSlash(MapFileServerName);
                Folder = AddSlash(Folder);

                if (!Directory.Exists(MapFileServerName + Folder))
                {
                    return new Resultado(false, "EL DIRECTORIO QUE INTENTA ELIMINAR NO EXISTE.");
                }

                Directory.Delete(MapFileServerName + Folder, Recursivo);

                return new Resultado(true);
            }
            catch (Exception ex)
            {
                return new Resultado(false, ex.Message);
            }
        }

        public Resultado MoverDirectorio(string FolderSource, string FolderTarget)
        {
            try
            {
                if (FolderSource.Length == 0)
                {
                    FolderSource = PathFolder;
                }

                Resultado r = VerifyPath(FolderSource);
                if (!r.TodoBien)
                {
                    return r;
                }

                r = VerifyPath(FolderTarget);
                if (!r.TodoBien)
                {
                    return r;
                }

                MapFileServerName = AddSlash(MapFileServerName);
                FolderSource = AddSlash(FolderSource);
                //FolderTarget = AddSlash(FolderTarget);

                if (Directory.Exists(MapFileServerName + FolderTarget))
                {
                    Directory.Delete(MapFileServerName + FolderTarget);
                }

                Directory.Move(MapFileServerName + FolderSource, MapFileServerName + FolderTarget);

                return new Resultado(true);
            }
            catch (Exception ex)
            {
                return new Resultado(false, ex.Message);
            }
        }
    }
}

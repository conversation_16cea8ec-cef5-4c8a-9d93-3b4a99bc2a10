﻿using INAIPI.DataAcces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace INAIPI.Core
{
    public class BasePage : System.Web.UI.Page
    {

        /// <summary>
        /// DETERMINA SI LA PÁGINA REQUIERE O NO AUTENTICACIÓN
        /// </summary>
        public bool Autenticacion { get; set; }

        public BasePage(bool oAutenticacion = true)
        {
            this.Autenticacion = oAutenticacion;
        }

        #region MSBOX

        public void MsgBox(string Mensaje, string Titulo = "", Msjtype Tipo = Msjtype.Success, int time = 0)
        {
            string tipo = string.Empty;
            switch (Tipo)
            {
                case Msjtype.Success:
                    tipo = "success";
                    break;
                case Msjtype.Info:
                    tipo = "info";
                    break;
                case Msjtype.Warning:
                    tipo = "warning";
                    break;
                case Msjtype.Error:
                    tipo = "error";
                    break;
            }

            string Msj = string.Empty;
            if (time > 0)
            {
                Msj = "swal({title: '" + Titulo + "',text: '" + <PERSON>saje + "',timer: " + time * 1000 + ",type: '" + tipo + "'});";
            }
            else
            {
                Msj = "swal({title: '" + Titulo + "',text: '" + <PERSON><PERSON><PERSON> + "',type: '" + tipo + "'});";
            }
            ClientScript.RegisterStartupScript(GetType(), "js", Msj, true);

        }
        //string Mensaje, string Titulo = "", Msjtype Tipo = Msjtype.Success, int Timer = 0, string UrlOnClose = ""
        public void MsgBox(string Mensaje, Msjtype Tipo = Msjtype.Success, string Titulo = "", int time = 0)
        {

            string swal = MsgBoxString(Mensaje, Titulo: Titulo, Tipo: Tipo, Timer: time);

            ClientScript.RegisterStartupScript(GetType(), "js", swal, true);
        }

        private const string DEFAULT_TITLE_FOR_SUCCESS = "¡TODO SALIÓ BIEN!";
        private const string DEFAULT_TITLE_FOR_INFO = "¡ALGO QUE TIENE QUE SABER!";
        private const string DEFAULT_TITLE_FOR_WARNING = "¡UNA OBSERVACIÓN!";
        private const string DEFAULT_TITLE_FOR_ERROR = "¡UPS!,TUVIMOS UN PROBLEMA";

        //public void MsgBox(string Mensaje, string Titulo = "", Msjtype Tipo = Msjtype.Success, int Timer = 0, string UrlOnClose = "", string CommandOnClose = "")
        //{
        //    string swal = MsgBoxString(Mensaje, Titulo, Tipo, Timer, UrlOnClose, CommandOnClose);

        //    ClientScript.RegisterStartupScript(GetType(), "js", swal, true);
        //}

        public string MsgBoxString(string Mensaje, string Titulo = "", Msjtype Tipo = Msjtype.Success, int Timer = 0, string UrlOnClose = "", string CommandOnClose = "")
        {
            string tipo = string.Empty;
            switch (Tipo)
            {
                case Msjtype.Success:
                    tipo = "success";
                    if (string.IsNullOrEmpty(Titulo))
                    {
                        Titulo = DEFAULT_TITLE_FOR_SUCCESS;
                    }
                    break;
                case Msjtype.Info:
                    tipo = "info";
                    if (string.IsNullOrEmpty(Titulo))
                    {
                        Titulo = DEFAULT_TITLE_FOR_INFO;
                    }
                    break;
                case Msjtype.Warning:
                    tipo = "warning";
                    if (string.IsNullOrEmpty(Titulo))
                    {
                        Titulo = DEFAULT_TITLE_FOR_WARNING;
                    }
                    break;
                case Msjtype.Error:
                    tipo = "error";
                    if (string.IsNullOrEmpty(Titulo))
                    {
                        Titulo = DEFAULT_TITLE_FOR_ERROR;
                    }
                    break;
            }

            Mensaje = VerificarComillasSimples(Mensaje);
            string swal = string.Empty;
            string cabeseraSwal = "swal({ ";
            string contenidoSwal = "title: '" + Titulo + "',text: \"" + @Mensaje + "\" ,type: '" + tipo + "'";
            string timerSwal = ", timer: " + Timer * 1000;
            string reloadOnCloseSwal = ", onClose: function () { location.href = '" + UrlOnClose + "'; }";
            string comadoncloce = ", onClose: " + CommandOnClose;
            string FinalSwal = " });";

            swal += cabeseraSwal;
            swal += contenidoSwal;

            if (Timer > 0)
            {
                swal += timerSwal;
            }
            if (!string.IsNullOrEmpty(CommandOnClose))
            {
                swal += comadoncloce;
            }
            else if (string.IsNullOrEmpty(CommandOnClose) && !string.IsNullOrEmpty(UrlOnClose))
            {
                swal += reloadOnCloseSwal;
            }
            swal += FinalSwal;

            return swal;
        }

        private string VerificarComillasSimples(string texto)
        {
            return texto.Replace("\n", " ").Replace("\r", "");
        }
        public enum Msjtype : int
        {
            Success = 1,
            Info = 2,
            Warning = 3,
            Error = 4
        }

        #endregion


    }
}

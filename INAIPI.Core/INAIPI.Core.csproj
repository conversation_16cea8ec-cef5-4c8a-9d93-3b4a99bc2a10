﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1921EA28-5FEF-404F-96A5-1E590C659942}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>INAIPI</RootNamespace>
    <AssemblyName>INAIPI.Core</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>2</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Dapper">
      <HintPath>..\packages\Dapper.1.50.2\lib\net45\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="Dapper.Contrib, Version=1.50.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.Contrib.1.50.0\lib\net45\Dapper.Contrib.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=11.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.11.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Core\Annotations\SPName.cs" />
    <Compile Include="Core\BaseCtrl.cs" />
    <Compile Include="Core\BaseDA.cs" />
    <Compile Include="Core\BaseModel.cs" />
    <Compile Include="Core\BaseModelExten.cs" />
    <Compile Include="Core\BasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Core\CloneExten.cs" />
    <Compile Include="Core\Comun.cs" />
    <Compile Include="Core\CorreoElectronico.cs" />
    <Compile Include="Core\CorreoElectronicoCtrl.cs" />
    <Compile Include="Core\CorreoElectronicoDestinatarios.cs" />
    <Compile Include="Core\DataRowExten.cs" />
    <Compile Include="Core\DateTimeExten.cs" />
    <Compile Include="Core\Delegates.cs" />
    <Compile Include="Core\Documento.cs" />
    <Compile Include="Core\FileServerCtrl.cs" />
    <Compile Include="Core\IEnumerableExten.cs" />
    <Compile Include="Core\JsonExten.cs" />
    <Compile Include="Core\MemoExten.cs" />
    <Compile Include="Core\NFCExten.cs" />
    <Compile Include="Core\ResultadoExten.cs" />
    <Compile Include="Core\StringExten.cs" />
    <Compile Include="Core\Resultado.cs" />
    <Compile Include="Core\TipoDocumentoExten.cs" />
    <Compile Include="Core\ToPivotDataTable.cs" />
    <Compile Include="Core\WSM_Correo.cs" />
    <Compile Include="Core\WSM_CorreoDestinatarios.cs" />
    <Compile Include="Core\WSM_CorreoOrigen.cs" />
    <Compile Include="DataAcces\DA.cs" />
    <Compile Include="DataAcces\DAADJUNTOS.cs" />
    <Compile Include="DataAcces\DAGESHUM.cs" />
    <Compile Include="DataAcces\DAQEC.cs" />
    <Compile Include="DataAcces\IDA.cs" />
    <Compile Include="Helper\NumerosToLetras.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Content\bootstrap-grid.css.map" />
    <None Include="Content\bootstrap-grid.min.css.map" />
    <None Include="Content\bootstrap-reboot.css.map" />
    <None Include="Content\bootstrap-reboot.min.css.map" />
    <None Include="Content\bootstrap.css.map" />
    <None Include="Content\bootstrap.min.css.map" />
    <None Include="packages.config" />
    <None Include="Scripts\bootstrap.bundle.js.map" />
    <None Include="Scripts\bootstrap.bundle.min.js.map" />
    <None Include="Scripts\bootstrap.js.map" />
    <None Include="Scripts\bootstrap.min.js.map" />
    <None Include="Scripts\esm\popper-utils.js.map" />
    <None Include="Scripts\esm\popper-utils.min.js.map" />
    <None Include="Scripts\esm\popper.js.map" />
    <None Include="Scripts\esm\popper.min.js.map" />
    <None Include="Scripts\jquery-3.0.0.min.map" />
    <None Include="Scripts\jquery-3.0.0.slim.min.map" />
    <None Include="Scripts\popper-utils.js.map" />
    <None Include="Scripts\popper-utils.min.js.map" />
    <None Include="Scripts\popper.js.map" />
    <None Include="Scripts\popper.min.js.map" />
    <None Include="Scripts\README.md" />
    <None Include="Scripts\umd\popper-utils.js.map" />
    <None Include="Scripts\umd\popper-utils.min.js.map" />
    <None Include="Scripts\umd\popper.js.map" />
    <None Include="Scripts\umd\popper.min.js.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\esm\popper-utils.js" />
    <Content Include="Scripts\esm\popper-utils.min.js" />
    <Content Include="Scripts\esm\popper.js" />
    <Content Include="Scripts\esm\popper.min.js" />
    <Content Include="Scripts\jquery-3.0.0-vsdoc.js" />
    <Content Include="Scripts\jquery-3.0.0.js" />
    <Content Include="Scripts\jquery-3.0.0.min.js" />
    <Content Include="Scripts\jquery-3.0.0.slim.js" />
    <Content Include="Scripts\jquery-3.0.0.slim.min.js" />
    <Content Include="Scripts\popper-utils.js" />
    <Content Include="Scripts\popper-utils.min.js" />
    <Content Include="Scripts\popper.js" />
    <Content Include="Scripts\popper.min.js" />
    <Content Include="Scripts\umd\popper-utils.js" />
    <Content Include="Scripts\umd\popper-utils.min.js" />
    <Content Include="Scripts\umd\popper.js" />
    <Content Include="Scripts\umd\popper.min.js" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="Scripts\index.d.ts" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
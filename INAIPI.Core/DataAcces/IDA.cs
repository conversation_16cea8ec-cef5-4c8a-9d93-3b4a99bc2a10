﻿using System;
namespace INAIPI.DataAcces
{
    public interface IDA
    {
        string strError { get; set; }
        System.Data.SqlClient.SqlConnection Conectar();
        string ConnectionString { get; set; }
        System.Data.DataTable EjecutarQuery(string strQuery, System.Data.CommandType CommandType, System.Collections.Generic.List<System.Data.SqlClient.SqlParameter> Parametros, System.Data.SqlClient.SqlConnection oCon = null, System.Data.SqlClient.SqlTransaction oTran = null);
        System.Data.DataTable EjecutarQuery(string strQuery, System.Data.CommandType CommandType, System.Data.SqlClient.SqlParameterCollection Parametros, System.Data.SqlClient.SqlConnection oCon = null, System.Data.SqlClient.SqlTransaction oTran = null);
        System.Data.DataTable EjecutarQuery(string strQuery, System.Data.SqlClient.SqlConnection oCon = null, System.Data.SqlClient.SqlTransaction oTran = null);
        System.Collections.Generic.ICollection<T> EjecutarQuery<T>(string strQuery, System.Data.CommandType CommandType, System.Collections.Generic.List<System.Data.SqlClient.SqlParameter> Parametros, System.Data.SqlClient.SqlConnection oCon = null, System.Data.SqlClient.SqlTransaction oTran = null) where T : class;
        System.Collections.Generic.ICollection<T> EjecutarQuery<T>(string strQuery, System.Data.CommandType CommandType, System.Data.SqlClient.SqlParameterCollection Parametros, System.Data.SqlClient.SqlConnection oCon = null, System.Data.SqlClient.SqlTransaction oTran = null) where T : class;
        System.Collections.Generic.ICollection<T> EjecutarQuery<T>(string strQuery, System.Data.SqlClient.SqlConnection oCon = null, System.Data.SqlClient.SqlTransaction oTran = null) where T : class;
        System.Collections.Generic.ICollection<T> BindModel<T>(System.Data.DataTable dt, System.Data.SqlClient.SqlConnection oCon = null, System.Data.SqlClient.SqlTransaction oTran = null) where T : class;
        System.Data.DataTable GetSchema(string tableName, string schema = "dbo");        
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Reflection;
using Dapper;
using System.Data.Common;

namespace INAIPI.DataAcces
{
    public class DA : INAIPI.DataAcces.IDA
    {
        public string ConnectionString { get; set; }
        //public string strError { get; set; }
        private string _strError;
        public string strError
        {
            get
            {
                if (HttpContext.Current == null || HttpContext.Current.Session == null)
                {
                    return _strError;
                }
                else
                {
                    return (string)HttpContext.Current.Session["DA_Error"];
                }
            }
            set
            {
                if (HttpContext.Current == null || HttpContext.Current.Session == null)
                {
                    _strError = value;
                }
                else
                {
                    HttpContext.Current.Session["DA_Error"] = value;
                }
            }
        }
        /// <summary>
        /// CONSTRUCTOR
        /// </summary>
        /// <param name="ConectionName">NOMBRE DE LA CADENA DE CONEXIÓN DE DATOS DESEADA</param>
        public DA(string ConectionName)
        {
            ConnectionString = ConfigurationManager.ConnectionStrings[ConectionName].ConnectionString;

        }


        //public void InitCON(string cs)
        //{
        //    ConnectionString = cs;
        //}

        public bool ValidarDB(string DBServidor, string DBUsuario, string DBContrasena, string DBBaseDatos, ref string ErrorMsg)
        {
            try
            {
                SqlConnectionStringBuilder stroCon = new SqlConnectionStringBuilder();
                stroCon.DataSource = DBServidor;
                stroCon.UserID = DBUsuario;
                stroCon.Password = DBContrasena;
                stroCon.InitialCatalog = DBBaseDatos;

                SqlConnection oCon = new SqlConnection(stroCon.ConnectionString);
                oCon.Open();

                return true;
            }
            catch (Exception ex)
            {
                ErrorMsg = ex.Message;
                return false;
            }
        }

        public List<string> ListaDBs(string DBServidor, string DBUsuario, string DBContrasena, ref string ErrorMsg)
        {
            try
            {
                SqlConnectionStringBuilder stroCon = new SqlConnectionStringBuilder();
                stroCon.DataSource = DBServidor;
                stroCon.UserID = DBUsuario;
                stroCon.Password = DBContrasena;

                SqlConnection oCon = new SqlConnection(stroCon.ConnectionString);
                oCon.Open();

                SqlCommand cmd = new SqlCommand("SELECT Name FROM sys.databases", oCon);
                SqlDataAdapter sda = new SqlDataAdapter(cmd);

                DataTable dt = new DataTable();

                sda.Fill(dt);

                List<string> DBs = new List<string>();

                foreach (DataRow dr in dt.Rows)
                {
                    DBs.Add(dr["Name"].ToString());
                }

                return DBs;
            }
            catch (Exception ex)
            {
                ErrorMsg = ex.Message;
                return null;
            }
        }

        public SqlConnection Conectar()
        {
            try
            {
                SqlConnection oCon = new SqlConnection(ConnectionString);
                oCon.Open();

                return oCon;
            }
            catch (Exception ex)
            {
                strError = ex.Message;
                return null;
            }
        }

        //public DataTable EjecutarQuery(string strQuery, SqlConnection oCon = null, SqlTransaction oTran = null)
        //{
        //    strError = "";

        //    bool CloseConAtEnd = false;

        //    try
        //    {
        //        if (oCon == null)
        //        {
        //            CloseConAtEnd = true;
        //            oCon = Conectar();
        //            if (oCon == null)
        //            {
        //                return null;
        //            }
        //        }

        //        SqlCommand cmd = new SqlCommand(strQuery, oCon);

        //        cmd.CommandTimeout = 300;

        //        if (oTran != null)
        //        {
        //            cmd.Transaction = oTran;
        //        }

        //        SqlDataAdapter sda = new SqlDataAdapter(cmd);

        //        DataTable dt = new DataTable();

        //        sda.Fill(dt);

        //        strError = "";

        //        return dt;
        //    }
        //    catch (Exception ex)
        //    {
        //        strError = ex.Message;
        //        return null;
        //    }
        //    finally
        //    {
        //        if (CloseConAtEnd)
        //        {
        //            if (oCon != null)
        //            {
        //                if (oCon.State == ConnectionState.Open)
        //                {
        //                    oCon.Close();
        //                }
        //            }
        //        }
        //    }
        //}

        //public ICollection<T> EjecutarQuery<T>(string strQuery, SqlConnection oCon = null, SqlTransaction oTran = null) where T : class
        //{
        //    DataTable dt = EjecutarQuery(strQuery, oCon, oTran);

        //    List<T> Lista = new List<T>();

        //    if (dt != null)
        //    {
        //        foreach (DataRow fila in dt.Rows)
        //        {
        //            T Objeto = (T)typeof(T).GetConstructors()[0].Invoke(new object[0]);
        //            PPropertyInfo[] propiedades = Objeto.GetType().GetProperties().Where(t => t.CanWrite == true).ToArray();

        //            foreach (DataColumn Columna in fila.Table.Columns)
        //            {
        //               PropertyInfo propie = propiedades.FirstOrDefault(y=>y.Name == Columna.ColumnName);
        //                if (propie != null)
        //                {

        //                    object tt = fila[propie.Name];
        //                    if (tt is DBNull)
        //                    {
        //                        tt = null;
        //                    }
        //                    try
        //                    {

        //                        Objeto.GetType().GetProperty(propie.Name).SetValue(Objeto, tt);
        //                    }
        //                    catch (Exception)
        //                    {

        //                        throw;
        //                    }
        //                }

        //            }

        //            Lista.Add(Objeto);
        //        }
        //    }
        //    return Lista;

        //}

        public DateTime FechaHora()
        {
            try
            {
                DataTable dt = EjecutarQuery("SELECT GETDATE() FechaHora");

                if (dt != null && dt.Rows.Count > 0)
                {
                    return (DateTime)dt.Rows[0]["FechaHora"];
                }
                else
                {
                    return DateTime.Now;
                }
            }
            catch (Exception)
            {
                return DateTime.Now;
            }

        }

        public enum AUD_Acciones : int
        {
            AGREGO = 1,
            CONSULTO = 2,
            MODIFICO = 3,
            ELIMINO = 4
        }

        public bool Grabar_Auditoria(string AUD_Tabla, int IdUsuario, AUD_Acciones IdAccion, string FIS_IP, string FIS_PCName, string FIS_PCUserName, List<string> OtrosFields, List<string> OtrosValores, SqlConnection oCon = null, SqlTransaction oTran = null)
        {
            string sqlInsert = "INSERT INTO " + AUD_Tabla + "(IdUsuario,IdAccion,FechaSuceso,FIS_IP,FIS_PCName,FIS_PCUserName";
            string sqlValues = "VALUES(" + IdUsuario.ToString() + "," + ((int)IdAccion).ToString() + ",GETDATE(),'" + FIS_IP + "','" + FIS_PCName + "','" + FIS_PCUserName + "'";

            for (int oStr = 0; oStr < OtrosFields.Count; oStr++)
            {
                sqlInsert += "," + OtrosFields[oStr];
                sqlValues += ",'" + OtrosValores[oStr] + "'";
            }

            EjecutarQuery(sqlInsert + ") " + sqlValues + ")", oCon, oTran);
            if (strError.Length > 0)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        public DataTable GetSchema(string tableName, string schema = "dbo")
        {
            SqlConnection con = Conectar();
            string[] filtro = { con.Database, schema, tableName };
            DataTable dtSchema = con.GetSchema("Columns", filtro);
            return dtSchema;
        }

        public DataTable EjecutarQuery(string strQuery, SqlConnection oCon = null, SqlTransaction oTran = null)
        {
            strError = "";

            bool CloseConAtEnd = false;

            try
            {
                if (oCon == null)
                {
                    CloseConAtEnd = true;
                    oCon = Conectar();
                    if (oCon == null)
                    {
                        return null;
                    }
                }

                SqlCommand cmd = new SqlCommand(strQuery, oCon);

                cmd.CommandTimeout = 300;

                if (oTran != null)
                {
                    cmd.Transaction = oTran;
                }

                SqlDataAdapter sda = new SqlDataAdapter(cmd);

                DataTable dt = new DataTable();

                sda.Fill(dt);

                strError = "";

                return dt;
            }
            catch (Exception ex)
            {
                strError = ex.Message;
                return null;
            }
            finally
            {
                if (CloseConAtEnd)
                {
                    if (oCon != null)
                    {
                        if (oCon.State == ConnectionState.Open)
                        {
                            oCon.Close();
                        }
                    }
                }
            }
        }

        public DataTable EjecutarQuery(string strQuery, CommandType CommandType, List<SqlParameter> Parametros, SqlConnection oCon = null, SqlTransaction oTran = null)
        {
            strError = "";

            bool CloseConAtEnd = false;

            try
            {
                if (oCon == null)
                {
                    CloseConAtEnd = true;
                    oCon = Conectar();
                    if (oCon == null)
                    {
                        return null;
                    }
                }



                SqlCommand cmd = new SqlCommand(strQuery, oCon);
                cmd.CommandTimeout = 300;
                cmd.CommandType = CommandType;

                if (Parametros != null)
                {
                    foreach (SqlParameter item in Parametros)
                    {
                        cmd.Parameters.Add(item);
                    }
                }

                if (oTran != null)
                {
                    cmd.Transaction = oTran;
                }

                SqlDataAdapter sda = new SqlDataAdapter(cmd);

                DataTable dt = new DataTable();

                sda.Fill(dt);

                strError = "";

                return dt;
            }
            catch (Exception ex)
            {
                strError = ex.Message;
                return null;
            }
            finally
            {
                if (CloseConAtEnd)
                {
                    if (oCon != null)
                    {
                        if (oCon.State == ConnectionState.Open)
                        {
                            oCon.Close();
                        }
                    }
                }
            }
        }

        public DataTable EjecutarQuery(string strQuery, CommandType CommandType, SqlParameterCollection Parametros, SqlConnection oCon = null, SqlTransaction oTran = null)
        {
            List<SqlParameter> lst = new List<SqlParameter>();

            foreach (SqlParameter item in Parametros)
            {
                lst.Add(item);
            }

            return EjecutarQuery(strQuery, CommandType, lst, oCon, oTran);
        }

        public ICollection<T> EjecutarQuery<T>(string strQuery, SqlConnection oCon = null, SqlTransaction oTran = null) where T : class
        {
            DataTable dt = EjecutarQuery(strQuery, oCon, oTran);

            List<T> Lista = new List<T>();

            if (dt != null)
            {
                foreach (DataRow fila in dt.Rows)
                {
                    T Objeto = (T)typeof(T).GetConstructors()[0].Invoke(new object[0]);
                    PropertyInfo[] propiedades = Objeto.GetType().GetProperties().Where(t => t.CanWrite == true).ToArray();

                    foreach (DataColumn Columna in fila.Table.Columns)
                    {
                        PropertyInfo propie = Objeto.GetType().GetProperties().FirstOrDefault(t => t.Name == Columna.ColumnName);
                        if (propie != null && propie.CanWrite)
                        {

                            object tt = fila[propie.Name];
                            if (tt is DBNull)
                            {
                                tt = null;
                            }
                            try
                            {

                                propie.SetValue(Objeto, tt);
                            }
                            catch (Exception ex)
                            {

                                throw;
                            }
                        }

                    }

                    Lista.Add(Objeto);
                }
            }
            return Lista;
        }

        public ICollection<T> EjecutarQuery<T>(string strQuery, CommandType CommandType, List<SqlParameter> Parametros, SqlConnection oCon = null, SqlTransaction oTran = null) where T : class
        {
            DataTable dt = EjecutarQuery(strQuery, CommandType, Parametros, oCon, oTran);

            List<T> Lista = new List<T>();

            if (dt != null)
            {
                foreach (DataRow fila in dt.Rows)
                {
                    T Objeto = (T)typeof(T).GetConstructors()[0].Invoke(new object[0]);
                    // PropertyInfo[] propiedades = Objeto.GetType().GetProperties().Where(t => t.CanWrite == true).ToArray();

                    foreach (DataColumn Columna in fila.Table.Columns)
                    {
                        PropertyInfo propie = Objeto.GetType().GetProperties().FirstOrDefault(y => y.Name == Columna.ColumnName);

                        try
                        {
                            if (propie != null && propie.CanWrite)
                            {

                                object tt = fila[propie.Name];
                                if (tt is DBNull)
                                {
                                    tt = null;
                                }
                                propie.SetValue(Objeto, tt);

                            }
                        }
                        catch (Exception ex)
                        {

                            throw ex;
                        }


                    }

                    Lista.Add(Objeto);
                }
            }
            return Lista;

        }

        public ICollection<T> EjecutarQuery<T>(string strQuery, CommandType CommandType, SqlParameterCollection Parametros, SqlConnection oCon = null, SqlTransaction oTran = null) where T : class
        {
            List<SqlParameter> lst = new List<SqlParameter>();

            foreach (SqlParameter item in Parametros)
            {
                lst.Add(item);
            }

            List<T> Lista = EjecutarQuery<T>(strQuery, CommandType, lst, oCon, oTran).ToList<T>();

            return Lista;

        }

        public ICollection<T> BindModel<T>(DataTable dt, SqlConnection oCon = null, SqlTransaction oTran = null) where T : class
        {
            //DataTable dt = EjecutarQuery(strQuery, oCon, oTran);

            List<T> Lista = new List<T>();

            if (dt != null)
            {
                foreach (DataRow fila in dt.Rows)
                {
                    T Objeto = (T)typeof(T).GetConstructors()[0].Invoke(new object[0]);
                    PropertyInfo[] propiedades = Objeto.GetType().GetProperties().Where(t => t.CanWrite == true).ToArray();

                    foreach (DataColumn Columna in fila.Table.Columns)
                    {
                        PropertyInfo propie = Objeto.GetType().GetProperties().FirstOrDefault(t => t.Name == Columna.ColumnName);
                        if (propie != null && propie.CanWrite)
                        {

                            object tt = fila[propie.Name];
                            if (tt is DBNull)
                            {
                                tt = null;
                            }
                            try
                            {

                                propie.SetValue(Objeto, tt);
                            }
                            catch (Exception)
                            {

                                throw;
                            }
                        }

                    }

                    Lista.Add(Objeto);
                }
            }
            return Lista;

        }

        public T dpQuery<T>(string strQuery, CommandType CommandType, object Parametros, DbConnection oCon = null, DbTransaction oTran = null) where T : class
        {
            if (oCon == null)
            {
                oCon = (DbConnection)Conectar();
                if (oCon == null)
                {
                    return null;
                }
            }
            //else
            //{
            //    if (oTran != null)
            //    {

            //    }

            //}

            CommandDefinition cd = new CommandDefinition(strQuery, Parametros);

            T obj = (T)oCon.Query<T>(cd);

            return obj;
        }

    }
}
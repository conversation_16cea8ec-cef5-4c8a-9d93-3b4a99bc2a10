/*
 Copyright (C) <PERSON> 2018
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */function a(a,b){if(1!==a.nodeType)return[];const c=getComputedStyle(a,null);return b?c[b]:c}function b(a){return'HTML'===a.nodeName?a:a.parentNode||a.host}function c(d){if(!d)return document.body;switch(d.nodeName){case'HTML':case'BODY':return d.ownerDocument.body;case'#document':return d.body;}const{overflow:e,overflowX:f,overflowY:g}=a(d);return /(auto|scroll|overlay)/.test(e+g+f)?d:c(b(d))}const d={};var e=function(a='all'){return(a=a.toString(),d.hasOwnProperty(a))?d[a]:('11'===a?d[a]=-1!==navigator.userAgent.indexOf('Trident'):'10'===a?d[a]=-1!==navigator.appVersion.indexOf('MSIE 10'):'all'===a?d[a]=-1!==navigator.userAgent.indexOf('Trident')||-1!==navigator.userAgent.indexOf('MSIE'):void 0,d.all=d.all||Object.keys(d).some((a)=>d[a]),d[a])};function f(b){if(!b)return document.documentElement;const c=e(10)?document.body:null;let d=b.offsetParent;for(;d===c&&b.nextElementSibling;)d=(b=b.nextElementSibling).offsetParent;const g=d&&d.nodeName;return g&&'BODY'!==g&&'HTML'!==g?-1!==['TD','TABLE'].indexOf(d.nodeName)&&'static'===a(d,'position')?f(d):d:b?b.ownerDocument.documentElement:document.documentElement}function g(a){const{nodeName:b}=a;return'BODY'!==b&&('HTML'===b||f(a.firstElementChild)===a)}function h(a){return null===a.parentNode?a:h(a.parentNode)}function i(a,b){if(!a||!a.nodeType||!b||!b.nodeType)return document.documentElement;const c=a.compareDocumentPosition(b)&Node.DOCUMENT_POSITION_FOLLOWING,d=c?a:b,e=c?b:a,j=document.createRange();j.setStart(d,0),j.setEnd(e,0);const{commonAncestorContainer:k}=j;if(a!==k&&b!==k||d.contains(e))return g(k)?k:f(k);const l=h(a);return l.host?i(l.host,b):i(a,h(b).host)}function j(a,b='top'){const c='top'===b?'scrollTop':'scrollLeft',d=a.nodeName;if('BODY'===d||'HTML'===d){const b=a.ownerDocument.documentElement,d=a.ownerDocument.scrollingElement||b;return d[c]}return a[c]}function k(a,b,c=!1){const d=j(b,'top'),e=j(b,'left'),f=c?-1:1;return a.top+=d*f,a.bottom+=d*f,a.left+=e*f,a.right+=e*f,a}function l(a,b){const c='x'===b?'Left':'Top',d='Left'==c?'Right':'Bottom';return parseFloat(a[`border${c}Width`],10)+parseFloat(a[`border${d}Width`],10)}function m(a,b,c,d){return Math.max(b[`offset${a}`],b[`scroll${a}`],c[`client${a}`],c[`offset${a}`],c[`scroll${a}`],e(10)?c[`offset${a}`]+d[`margin${'Height'===a?'Top':'Left'}`]+d[`margin${'Height'===a?'Bottom':'Right'}`]:0)}function n(){const a=document.body,b=document.documentElement,c=e(10)&&getComputedStyle(b);return{height:m('Height',a,b,c),width:m('Width',a,b,c)}}var o=Object.assign||function(a){for(var b,c=1;c<arguments.length;c++)for(var d in b=arguments[c],b)Object.prototype.hasOwnProperty.call(b,d)&&(a[d]=b[d]);return a};function p(a){return o({},a,{right:a.left+a.width,bottom:a.top+a.height})}function q(b){let c={};try{if(e(10)){c=b.getBoundingClientRect();const a=j(b,'top'),d=j(b,'left');c.top+=a,c.left+=d,c.bottom+=a,c.right+=d}else c=b.getBoundingClientRect()}catch(a){}const d={left:c.left,top:c.top,width:c.right-c.left,height:c.bottom-c.top},f='HTML'===b.nodeName?n():{},g=f.width||b.clientWidth||d.right-d.left,h=f.height||b.clientHeight||d.bottom-d.top;let i=b.offsetWidth-g,k=b.offsetHeight-h;if(i||k){const c=a(b);i-=l(c,'x'),k-=l(c,'y'),d.width-=i,d.height-=k}return p(d)}function r(b,d,f=!1){var g=Math.max;const h=e(10),i='HTML'===d.nodeName,j=q(b),l=q(d),m=c(b),n=a(d),o=parseFloat(n.borderTopWidth,10),r=parseFloat(n.borderLeftWidth,10);f&&'HTML'===d.nodeName&&(l.top=g(l.top,0),l.left=g(l.left,0));let s=p({top:j.top-l.top-o,left:j.left-l.left-r,width:j.width,height:j.height});if(s.marginTop=0,s.marginLeft=0,!h&&i){const a=parseFloat(n.marginTop,10),b=parseFloat(n.marginLeft,10);s.top-=o-a,s.bottom-=o-a,s.left-=r-b,s.right-=r-b,s.marginTop=a,s.marginLeft=b}return(h&&!f?d.contains(m):d===m&&'BODY'!==m.nodeName)&&(s=k(s,d)),s}function s(a,b=!1){var c=Math.max;const d=a.ownerDocument.documentElement,e=r(a,d),f=c(d.clientWidth,window.innerWidth||0),g=c(d.clientHeight,window.innerHeight||0),h=b?0:j(d),i=b?0:j(d,'left'),k={top:h-e.top+e.marginTop,left:i-e.left+e.marginLeft,width:f,height:g};return p(k)}function t(c){const d=c.nodeName;return'BODY'===d||'HTML'===d?!1:!('fixed'!==a(c,'position'))||t(b(c))}function u(b){if(!b||!b.parentElement||e())return document.documentElement;let c=b.parentElement;for(;c&&'none'===a(c,'transform');)c=c.parentElement;return c||document.documentElement}function v(a,d,e,f,g=!1){let h={top:0,left:0};const j=g?u(a):i(a,d);if('viewport'===f)h=s(j,g);else{let e;'scrollParent'===f?(e=c(b(d)),'BODY'===e.nodeName&&(e=a.ownerDocument.documentElement)):'window'===f?e=a.ownerDocument.documentElement:e=f;const i=r(e,j,g);if('HTML'===e.nodeName&&!t(j)){const{height:a,width:b}=n();h.top+=i.top-i.marginTop,h.bottom=a+i.top,h.left+=i.left-i.marginLeft,h.right=b+i.left}else h=i}return h.left+=e,h.top+=e,h.right-=e,h.bottom-=e,h}function w({width:a,height:b}){return a*b}function x(a,b,c,d,e,f=0){if(-1===a.indexOf('auto'))return a;const g=v(c,d,f,e),h={top:{width:g.width,height:b.top-g.top},right:{width:g.right-b.right,height:g.height},bottom:{width:g.width,height:g.bottom-b.bottom},left:{width:b.left-g.left,height:g.height}},i=Object.keys(h).map((a)=>o({key:a},h[a],{area:w(h[a])})).sort((c,a)=>a.area-c.area),j=i.filter(({width:a,height:b})=>a>=c.clientWidth&&b>=c.clientHeight),k=0<j.length?j[0].key:i[0].key,l=a.split('-')[1];return k+(l?`-${l}`:'')}const y='undefined'!=typeof window&&'undefined'!=typeof document,z=['Edge','Trident','Firefox'];let A=0;for(let a=0;a<z.length;a+=1)if(y&&0<=navigator.userAgent.indexOf(z[a])){A=1;break}function B(a){let b=!1;return()=>{b||(b=!0,window.Promise.resolve().then(()=>{b=!1,a()}))}}function C(a){let b=!1;return()=>{b||(b=!0,setTimeout(()=>{b=!1,a()},A))}}const D=y&&window.Promise;var E=D?B:C;function F(a,b){return Array.prototype.find?a.find(b):a.filter(b)[0]}function G(a,b,c){if(Array.prototype.findIndex)return a.findIndex((a)=>a[b]===c);const d=F(a,(a)=>a[b]===c);return a.indexOf(d)}function H(a){let b;if('HTML'===a.nodeName){const{width:a,height:c}=n();b={width:a,height:c,left:0,top:0}}else b={width:a.offsetWidth,height:a.offsetHeight,left:a.offsetLeft,top:a.offsetTop};return p(b)}function I(a){const b=getComputedStyle(a),c=parseFloat(b.marginTop)+parseFloat(b.marginBottom),d=parseFloat(b.marginLeft)+parseFloat(b.marginRight),e={width:a.offsetWidth+d,height:a.offsetHeight+c};return e}function J(a){const b={left:'right',right:'left',bottom:'top',top:'bottom'};return a.replace(/left|right|bottom|top/g,(a)=>b[a])}function K(a,b,c){c=c.split('-')[0];const d=I(a),e={width:d.width,height:d.height},f=-1!==['right','left'].indexOf(c),g=f?'top':'left',h=f?'left':'top',i=f?'height':'width',j=f?'width':'height';return e[g]=b[g]+b[i]/2-d[i]/2,e[h]=c===h?b[h]-d[j]:b[J(h)],e}function L(a,b,c,d=null){const e=d?u(b):i(b,c);return r(c,e,d)}function M(a){const b=[!1,'ms','Webkit','Moz','O'],c=a.charAt(0).toUpperCase()+a.slice(1);for(let d=0;d<b.length;d++){const e=b[d],f=e?`${e}${c}`:a;if('undefined'!=typeof document.body.style[f])return f}return null}function N(a){return a&&'[object Function]'==={}.toString.call(a)}function O(a,b){return a.some(({name:a,enabled:c})=>c&&a===b)}function P(a,b,c){const d=F(a,({name:a})=>a===b),e=!!d&&a.some((a)=>a.name===c&&a.enabled&&a.order<d.order);if(!e){const a=`\`${b}\``,d=`\`${c}\``;console.warn(`${d} modifier is required by ${a} modifier in order to work, be sure to include it before ${a}!`)}return e}function Q(a){return''!==a&&!isNaN(parseFloat(a))&&isFinite(a)}function R(a){const b=a.ownerDocument;return b?b.defaultView:window}function S(a,b){return R(a).removeEventListener('resize',b.updateBound),b.scrollParents.forEach((a)=>{a.removeEventListener('scroll',b.updateBound)}),b.updateBound=null,b.scrollParents=[],b.scrollElement=null,b.eventsEnabled=!1,b}function T(a,b,c){const d=void 0===c?a:a.slice(0,G(a,'name',c));return d.forEach((a)=>{a['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');const c=a['function']||a.fn;a.enabled&&N(c)&&(b.offsets.popper=p(b.offsets.popper),b.offsets.reference=p(b.offsets.reference),b=c(b,a))}),b}function U(a,b){Object.keys(b).forEach(function(c){const d=b[c];!1===d?a.removeAttribute(c):a.setAttribute(c,b[c])})}function V(a,b){Object.keys(b).forEach((c)=>{let d='';-1!==['width','height','top','right','bottom','left'].indexOf(c)&&Q(b[c])&&(d='px'),a.style[c]=b[c]+d})}function W(a,b,d,e){const f='BODY'===a.nodeName,g=f?a.ownerDocument.defaultView:a;g.addEventListener(b,d,{passive:!0}),f||W(c(g.parentNode),b,d,e),e.push(g)}function X(a,b,d,e){d.updateBound=e,R(a).addEventListener('resize',d.updateBound,{passive:!0});const f=c(a);return W(f,'scroll',d.updateBound,d.scrollParents),d.scrollElement=f,d.eventsEnabled=!0,d}var Y={computeAutoPlacement:x,debounce:E,findIndex:G,getBordersSize:l,getBoundaries:v,getBoundingClientRect:q,getClientRect:p,getOffsetParent:f,getOffsetRect:H,getOffsetRectRelativeToArbitraryNode:r,getOuterSizes:I,getParentNode:b,getPopperOffsets:K,getReferenceOffsets:L,getScroll:j,getScrollParent:c,getStyleComputedProperty:a,getSupportedPropertyName:M,getWindowSizes:n,isFixed:t,isFunction:N,isModifierEnabled:O,isModifierRequired:P,isNumeric:Q,removeEventListeners:S,runModifiers:T,setAttributes:U,setStyles:V,setupEventListeners:X};export{x as computeAutoPlacement,E as debounce,G as findIndex,l as getBordersSize,v as getBoundaries,q as getBoundingClientRect,p as getClientRect,f as getOffsetParent,H as getOffsetRect,r as getOffsetRectRelativeToArbitraryNode,I as getOuterSizes,b as getParentNode,K as getPopperOffsets,L as getReferenceOffsets,j as getScroll,c as getScrollParent,a as getStyleComputedProperty,M as getSupportedPropertyName,n as getWindowSizes,t as isFixed,N as isFunction,O as isModifierEnabled,P as isModifierRequired,Q as isNumeric,S as removeEventListeners,T as runModifiers,U as setAttributes,V as setStyles,X as setupEventListeners};export default Y;
//# sourceMappingURL=popper-utils.min.js.map

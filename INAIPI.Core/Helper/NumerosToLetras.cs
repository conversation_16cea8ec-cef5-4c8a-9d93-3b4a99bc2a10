﻿//using System;
//using System.Collections.Generic;
//using System.Globalization;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace INAIPI.Helper
//{
//    public sealed class NumerosToLetras
//    {
//        private const int UNI = 0;

//        private const int DIECI = 1;

//        private const int DECENA = 2;

//        private const int CENTENA = 3;

//        private static string[,] _matriz;

//        private const char sub = '\u001a';

//        public const string SeparadorDecimalSalidaDefault = "con";

//        public const string MascaraSalidaDecimalDefault = "00'/100'";

//        public const int DecimalesDefault = 2;

//        public const bool LetraCapitalDefault = false;

//        public const bool ConvertirDecimalesDefault = false;

//        public const bool ApocoparUnoParteEnteraDefault = false;

//        public const bool ApocoparUnoParteDecimalDefault = false;

//        private int _decimales;

//        private CultureInfo _cultureInfo;

//        private string _separadorDecimalSalida;

//        private int _posiciones;

//        private string _mascaraSalidaDecimal;

//        private string _mascaraSalidaDecimalInterna;

//        private bool _esMascaraNumerica;

//        private bool _letraCapital;

//        private bool _convertirDecimales;

//        private bool _apocoparUnoParteEntera;

//        private bool _apocoparUnoParteDecimal;

//        public int Decimales
//        {
//            get
//            {
//                return this._decimales;
//            }
//            set
//            {
//                bool flag = value > 10;
//                if (flag)
//                {
//                    throw new ArgumentException(value.ToString() + " excede el número máximo de decimales admitidos, solo se admiten hasta 10.");
//                }
//                this._decimales = value;
//            }
//        }

//        public CultureInfo CultureInfo
//        {
//            get
//            {
//                return this._cultureInfo;
//            }
//            set
//            {
//                this._cultureInfo = value;
//            }
//        }

//        public string SeparadorDecimalSalida
//        {
//            get
//            {
//                return this._separadorDecimalSalida;
//            }
//            set
//            {
//                this._separadorDecimalSalida = value;
//                bool flag = value.Trim().IndexOf(" ") > 0;
//                if (flag)
//                {
//                    this._apocoparUnoParteEntera = true;
//                }
//                else
//                {
//                    this._apocoparUnoParteEntera = false;
//                }
//            }
//        }

//        public string MascaraSalidaDecimal
//        {
//            get
//            {
//                bool flag = !string.IsNullOrEmpty(this._mascaraSalidaDecimal);
//                string result;
//                if (flag)
//                {
//                    result = this._mascaraSalidaDecimal;
//                }
//                else
//                {
//                    result = "";
//                }
//                return result;
//            }
//            set
//            {
//                int num = 0;
//                checked
//                {
//                    while (num < value.Length && (value[num] == '0' || Operators.CompareString(Conversions.ToString(value[num]), "#", false) == 0))
//                    {
//                        num++;
//                    }
//                    this._posiciones = num;
//                    bool flag = num > 0;
//                    if (flag)
//                    {
//                        this._decimales = num;
//                        this._esMascaraNumerica = true;
//                    }
//                    else
//                    {
//                        this._esMascaraNumerica = false;
//                    }
//                    this._mascaraSalidaDecimal = value;
//                    flag = this._esMascaraNumerica;
//                    if (flag)
//                    {
//                        this._mascaraSalidaDecimalInterna = value.Substring(0, this._posiciones) + "'" + value.Substring(this._posiciones).Replace("''", '\u001a'.ToString()).Replace("'", string.Empty).Replace('\u001a'.ToString(), "'") + "'";
//                    }
//                    else
//                    {
//                        this._mascaraSalidaDecimalInterna = value.Replace("''", '\u001a'.ToString()).Replace("'", string.Empty).Replace('\u001a'.ToString(), "'");
//                    }
//                }
//            }
//        }

//        public bool LetraCapital
//        {
//            get
//            {
//                return this._letraCapital;
//            }
//            set
//            {
//                this._letraCapital = value;
//            }
//        }

//        public bool ConvertirDecimales
//        {
//            get
//            {
//                return this._convertirDecimales;
//            }
//            set
//            {
//                this._convertirDecimales = value;
//                this._apocoparUnoParteDecimal = value;
//                if (value)
//                {
//                    bool flag = Operators.CompareString(this._mascaraSalidaDecimal, "00'/100'", false) == 0;
//                    if (flag)
//                    {
//                        this.MascaraSalidaDecimal = "";
//                    }
//                }
//                else
//                {
//                    bool flag = string.IsNullOrEmpty(this._mascaraSalidaDecimal);
//                    if (flag)
//                    {
//                        this.MascaraSalidaDecimal = "00'/100'";
//                    }
//                }
//            }
//        }

//        public bool ApocoparUnoParteEntera
//        {
//            get
//            {
//                return this._apocoparUnoParteEntera;
//            }
//            set
//            {
//                this._apocoparUnoParteEntera = value;
//            }
//        }

//        public bool ApocoparUnoParteDecimal
//        {
//            get
//            {
//                return this._apocoparUnoParteDecimal;
//            }
//            set
//            {
//                this._apocoparUnoParteDecimal = value;
//            }
//        }

//        static NumerosToLetras()
//        {
//            // Note: this type is marked as 'beforefieldinit'.
//            string[,] array = new string[4, 10];
//            array[0, 0] = null;
//            array[0, 1] = " uno";
//            array[0, 2] = " dos";
//            array[0, 3] = " tres";
//            array[0, 4] = " cuatro";
//            array[0, 5] = " cinco";
//            array[0, 6] = " seis";
//            array[0, 7] = " siete";
//            array[0, 8] = " ocho";
//            array[0, 9] = " nueve";
//            array[1, 0] = " diez";
//            array[1, 1] = " once";
//            array[1, 2] = " doce";
//            array[1, 3] = " trece";
//            array[1, 4] = " catorce";
//            array[1, 5] = " quince";
//            array[1, 6] = " dieciséis";
//            array[1, 7] = " diecisiete";
//            array[1, 8] = " dieciocho";
//            array[1, 9] = " diecinueve";
//            array[2, 0] = null;
//            array[2, 1] = null;
//            array[2, 2] = null;
//            array[2, 3] = " treinta";
//            array[2, 4] = " cuarenta";
//            array[2, 5] = " cincuenta";
//            array[2, 6] = " sesenta";
//            array[2, 7] = " setenta";
//            array[2, 8] = " ochenta";
//            array[2, 9] = " noventa";
//            array[3, 0] = null;
//            array[3, 1] = null;
//            array[3, 2] = null;
//            array[3, 3] = null;
//            array[3, 4] = null;
//            array[3, 5] = " quinientos";
//            array[3, 6] = null;
//            array[3, 7] = " setecientos";
//            array[3, 8] = null;
//            array[3, 9] = " novecientos";
//            NumerosToLetras._matriz = array;
//        }

//        public NumerosToLetras()
//        {
//            this._decimales = 2;
//            this._cultureInfo = CultureInfo.CurrentCulture;
//            this._separadorDecimalSalida = "con";
//            this._posiciones = 2;
//            this._mascaraSalidaDecimalInterna = "00'/100'";
//            this._esMascaraNumerica = true;
//            this._letraCapital = false;
//            this._convertirDecimales = false;
//            this._apocoparUnoParteEntera = false;
//            this.MascaraSalidaDecimal = "00'/100'";
//            this.SeparadorDecimalSalida = "con";
//            this.LetraCapital = false;
//            this.ConvertirDecimales = this._convertirDecimales;
//        }

//        public NumerosToLetras(bool ConvertirDecimales, string MascaraSalidaDecimal, string SeparadorDecimalSalida, bool LetraCapital)
//        {
//            this._decimales = 2;
//            this._cultureInfo = CultureInfo.CurrentCulture;
//            this._separadorDecimalSalida = "con";
//            this._posiciones = 2;
//            this._mascaraSalidaDecimalInterna = "00'/100'";
//            this._esMascaraNumerica = true;
//            this._letraCapital = false;
//            this._convertirDecimales = false;
//            this._apocoparUnoParteEntera = false;
//            bool flag = !string.IsNullOrEmpty(MascaraSalidaDecimal);
//            if (flag)
//            {
//                this.MascaraSalidaDecimal = MascaraSalidaDecimal;
//            }
//            flag = !string.IsNullOrEmpty(SeparadorDecimalSalida);
//            if (flag)
//            {
//                this._separadorDecimalSalida = SeparadorDecimalSalida;
//            }
//            this._letraCapital = LetraCapital;
//            this._convertirDecimales = ConvertirDecimales;
//        }

//        public string ToCustomCardinal(double Numero)
//        {
//            return NumerosToLetras.Convertir(Convert.ToDecimal(Numero), this._decimales, this._separadorDecimalSalida, this._mascaraSalidaDecimalInterna, this._esMascaraNumerica, this._letraCapital, this._convertirDecimales, this._apocoparUnoParteEntera, this._apocoparUnoParteDecimal);
//        }

//        public string ToCustomCardinal(string Numero)
//        {
//            double numero;
//            bool flag = double.TryParse(Numero, NumberStyles.Float, this._cultureInfo, out numero);
//            if (flag)
//            {
//                return this.ToCustomCardinal(numero);
//            }
//            throw new ArgumentException("'" + Numero + "' no es un número válido.");
//        }

//        public string ToCustomCardinal(decimal Numero)
//        {
//            return NumerosToLetras.ToLetras(Numero);
//        }

//        public string ToCustomCardinal(int Numero)
//        {
//            return NumerosToLetras.Convertir(Convert.ToDecimal(Numero), 0, this._separadorDecimalSalida, this._mascaraSalidaDecimalInterna, this._esMascaraNumerica, this._letraCapital, this._convertirDecimales, this._apocoparUnoParteEntera, false);
//        }

//        public static string ToLetras(int Numero)
//        {
//            return NumerosToLetras.Convertir(Convert.ToDecimal(Numero), 0, null, null, true, false, false, false, false);
//        }

//        public static string ToLetras(double Numero)
//        {
//            return NumerosToLetras.Convertir(Convert.ToDecimal(Numero), 2, "con", "00'/100'", true, false, false, false, false);
//        }

//        public static string ToLetras(string Numero, CultureInfo ReferenciaCultural)
//        {
//            double numero;
//            bool flag = double.TryParse(Numero, NumberStyles.Float, ReferenciaCultural, out numero);
//            if (flag)
//            {
//                return NumerosToLetras.ToLetras(numero);
//            }
//            throw new ArgumentException("'" + Numero + "' no es un número válido.");
//        }

//        public static string ToLetras(string Numero)
//        {
//            return NumerosToLetras.ToLetras(Numero, CultureInfo.CurrentCulture);
//        }

//        public static string ToLetras(string Numero, bool TodoMayuscula)
//        {
//            string text = NumerosToLetras.ToLetras(Numero, CultureInfo.CurrentCulture);
//            if (TodoMayuscula)
//            {
//                text = String.Replace(text, "á", "a", 1, -1, CompareMethod.Binary);
//                text = String.Replace(text, "é", "e", 1, -1, CompareMethod.Binary);
//                text = String.Replace(text, "í", "i", 1, -1, CompareMethod.Binary);
//                text = String.Replace(text, "ó", "o", 1, -1, CompareMethod.Binary);
//                text = String.Replace(text, "ú", "u", 1, -1, CompareMethod.Binary);
//                text = String.UCase(text);
//            }
//            return text;
//        }

//        public static string ToLetras(decimal Numero)
//        {
//            return NumerosToLetras.ToLetras(Convert.ToDouble(Numero));
//        }

//        public static string Convertir(decimal Numero, int Decimales, string SeparadorDecimalSalida, string MascaraSalidaDecimal, bool EsMascaraNumerica, bool LetraCapital, bool ConvertirDecimales, bool ApocoparUnoParteEntera, bool ApocoparUnoParteDecimal)
//        {
//            StringBuilder stringBuilder = new StringBuilder();
//            long num = Convert.ToInt64(Math.Floor(Math.Abs(Numero)));
//            bool flag = num >= 1000000000001L || num < 0L;
//            if (flag)
//            {
//                throw new ArgumentException("El número '" + Numero.ToString() + "' excedió los límites del conversor: [0;1.000.000.000.001]");
//            }
//            flag = (num == 0L);
//            checked
//            {
//                bool flag2;
//                if (flag)
//                {
//                    stringBuilder.Append(" cero");
//                }
//                else
//                {
//                    int num2 = 0;
//                    while (num != 0L)
//                    {
//                        num2++;
//                        string text = string.Empty;
//                        int num3 = (int)(num % 1000L);
//                        int num4 = (int)Math.Round(Conversion.Int((double)num3 / 100.0));
//                        int num5 = num3 - num4 * 100;
//                        int num6 = (int)Math.Round(unchecked((double)num5 - Math.Floor((double)num5 / 10.0) * 10.0));
//                        int num7 = num5;
//                        flag = (num7 >= 1 && num7 <= 9);
//                        if (flag)
//                        {
//                            text = NumerosToLetras._matriz[0, num6] + text;
//                        }
//                        else
//                        {
//                            flag = (num7 >= 10 && num7 <= 19);
//                            if (flag)
//                            {
//                                text += NumerosToLetras._matriz[1, num6];
//                            }
//                            else
//                            {
//                                flag = (num7 == 20);
//                                if (flag)
//                                {
//                                    text += " veinte";
//                                }
//                                else
//                                {
//                                    flag = (num7 >= 21 && num7 <= 29);
//                                    if (flag)
//                                    {
//                                        text = " veinti" + NumerosToLetras._matriz[0, num6].Substring(1);
//                                    }
//                                    else
//                                    {
//                                        flag = (num7 >= 30 && num7 <= 99);
//                                        if (flag)
//                                        {
//                                            flag2 = (num6 != 0);
//                                            if (flag2)
//                                            {
//                                                text = NumerosToLetras._matriz[2, (int)Math.Round(Conversion.Int((double)num5 / 10.0))] + " y" + NumerosToLetras._matriz[0, num6] + text;
//                                            }
//                                            else
//                                            {
//                                                text += NumerosToLetras._matriz[2, (int)Math.Round(Conversion.Int((double)num5 / 10.0))];
//                                            }
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                        switch (num4)
//                        {
//                            case 1:
//                                flag2 = (num5 > 0);
//                                if (flag2)
//                                {
//                                    text = " ciento" + text;
//                                }
//                                else
//                                {
//                                    text = " cien" + text;
//                                }
//                                break;
//                            case 2:
//                            case 3:
//                            case 4:
//                            case 6:
//                            case 8:
//                                goto IL_2BD;
//                            case 5:
//                            case 7:
//                            case 9:
//                                text = NumerosToLetras._matriz[3, (int)Math.Round(Conversion.Int((double)num3 / 100.0))] + text;
//                                break;
//                            default:
//                                goto IL_2BD;
//                        }
//                    IL_314:
//                        flag2 = ((num2 > 1 || ApocoparUnoParteEntera) && num5 == 21);
//                        if (flag2)
//                        {
//                            text = text.Replace("veintiuno", "veintiún");
//                        }
//                        else
//                        {
//                            flag2 = ((num2 > 1 || ApocoparUnoParteEntera) && num6 == 1 && num5 != 11);
//                            if (flag2)
//                            {
//                                text = text.Substring(0, text.Length - 1);
//                            }
//                            else
//                            {
//                                flag2 = (num5 == 22);
//                                if (flag2)
//                                {
//                                    text = text.Replace("veintidos", "veintidós");
//                                }
//                                else
//                                {
//                                    flag2 = (num5 == 23);
//                                    if (flag2)
//                                    {
//                                        text = text.Replace("veintitres", "veintitrés");
//                                    }
//                                    else
//                                    {
//                                        flag2 = (num5 == 26);
//                                        if (flag2)
//                                        {
//                                            text = text.Replace("veintiseis", "veintiséis");
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                        switch (num2)
//                        {
//                            case 2:
//                            case 4:
//                                flag2 = (num3 > 0);
//                                if (flag2)
//                                {
//                                    text += " mil";
//                                }
//                                break;
//                            case 3:
//                                flag2 = (decimal.Compare(Numero, new decimal(2000000L)) < 0);
//                                if (flag2)
//                                {
//                                    text += " millón";
//                                }
//                                else
//                                {
//                                    text += " millones";
//                                }
//                                break;
//                        }
//                        stringBuilder.Insert(0, text);
//                        num = (long)Math.Round(Conversion.Int((double)num / 1000.0));
//                        continue;
//                    IL_2BD:
//                        flag2 = (Conversion.Int((double)num3 / 100.0) > 1.0);
//                        if (flag2)
//                        {
//                            text = NumerosToLetras._matriz[0, (int)Math.Round(Conversion.Int((double)num3 / 100.0))] + "cientos" + text;
//                        }
//                        goto IL_314;
//                    }
//                }
//                flag2 = (Decimales > 0);
//                if (flag2)
//                {
//                    stringBuilder.Append(" " + SeparadorDecimalSalida + " ");
//                    int value = (int)Math.Round(Conversion.Int(Math.Round(unchecked(Convert.ToDouble(decimal.Subtract(Numero, Conversion.Int(Numero))) * Math.Pow(10.0, (double)Decimales)))));
//                    if (ConvertirDecimales)
//                    {
//                        bool flag3 = Operators.CompareString(MascaraSalidaDecimal, "00'/100'", false) == 0;
//                        stringBuilder.Append(Operators.AddObject(NumerosToLetras.Convertir(Convert.ToDecimal(value), 0, null, null, EsMascaraNumerica, false, false, ApocoparUnoParteDecimal && !EsMascaraNumerica, false) + " ", Interaction.IIf(EsMascaraNumerica, "", MascaraSalidaDecimal)));
//                    }
//                    else if (EsMascaraNumerica)
//                    {
//                        stringBuilder.Append(value.ToString(MascaraSalidaDecimal));
//                    }
//                    else
//                    {
//                        stringBuilder.Append(value.ToString() + " " + MascaraSalidaDecimal);
//                    }
//                }
//                string result;
//                if (LetraCapital)
//                {
//                    result = stringBuilder[1].ToString().ToUpper() + stringBuilder.ToString(2, stringBuilder.Length - 2);
//                }
//                else
//                {
//                    result = stringBuilder.ToString().Substring(1);
//                }
//                return result;
//            }
//        }
//    }
//}

﻿function cerrarConexiones(nombreBaseDeDatos) {
    let db;
    const request = indexedDB.open(nombreBaseDeDatos);

    request.onsuccess = function (event) {
        db = event.target.result;
        console.log(`Conexión a la base de datos "${nombreBaseDeDatos}" abierta.`);

        // Cierra la conexión activamente
        db.close();
        console.log(`Conexión a la base de datos "${nombreBaseDeDatos}" cerrada.`);
    };

    request.onerror = function (event) {
        console.error(`Error al abrir la base de datos "${nombreBaseDeDatos}":`, event.target.error);
    };
}

function eliminarBaseDeDatos(nombreBaseDeDatos) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.deleteDatabase(nombreBaseDeDatos);

        request.onsuccess = function () {
            console.log(`La base de datos "${nombreBaseDeDatos}" fue eliminada con éxito.`);
            resolve(`La base de datos "${nombreBaseDeDatos}" fue eliminada con éxito.`);
        };

        request.onerror = function (event) {
            console.error(`Error al intentar eliminar la base de datos "${nombreBaseDeDatos}":`, event.target.error);
            reject(`Error al intentar eliminar la base de datos "${nombreBaseDeDatos}": ${event.target.error}`);
        };

        request.onblocked = function () {
            console.warn(`La eliminación de la base de datos "${nombreBaseDeDatos}" está bloqueada. Cierra todas las conexiones abiertas.`);
            cerrarConexiones(nombreBaseDeDatos); // Intenta cerrar conexiones activas
            reject(`La eliminación de la base de datos "${nombreBaseDeDatos}" está bloqueada.`);
        };
    });
}

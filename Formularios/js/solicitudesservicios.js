﻿const createDatabase = () => {
    const request = indexedDB.open('SolicitudesServicios', 1);

    request.onsuccess = (e) => {
        //console.log('database creada');     
        db = request.result;
    }
    return request;
}

const crearTablas = () => {
    const database = createDatabase();

    //request = database;  

    database.onupgradeneeded = (e) => {

        const db = database.result;
        //const db = request.result;

        const objectStoreNNs = db.createObjectStore('Ninos', { keyPath: 'IdNN' });

        objectStoreNNs.createIndex('IdNN', 'IdNN', { unique: true });
        objectStoreNNs.createIndex("Nombre1", "Nombre1", { unique: false });
        objectStoreNNs.createIndex("Nombre2", "Nombre2", { unique: false });
        objectStoreNNs.createIndex("Apellido1", "Apellido1", { unique: false });
        objectStoreNNs.createIndex("Apellido2", "Apellido2", { unique: false });
        objectStoreNNs.createIndex("Sexo", "Sexo", { unique: false });
        objectStoreNNs.createIndex("Edad", "Edad", { unique: false });
        objectStoreNNs.createIndex("FechaNacimiento", "FechaNacimiento", { unique: false });

        objectStoreNNs.transaction.oncomplete = (e) => {
            console.log('Tabla Ninos ha sido creada');
        }

        const objectStoreSolicitudesServicios = db.createObjectStore('Solicitudes', { keyPath: 'IdSolicitud' });

        objectStoreSolicitudesServicios.createIndex('IdSolicitud', 'IdSolicitud', { unique: true });
        objectStoreSolicitudesServicios.createIndex("TipoDocumentoIdentidad", "TipoDocumentoIdentidad", { unique: false });
        objectStoreSolicitudesServicios.createIndex("DocumentoIdentidad", "DocumentoIdentidad", { unique: false });
        objectStoreSolicitudesServicios.createIndex("TipoCedulaOPasaporte", "TipoCedulaOPasaporte", { unique: false });
        objectStoreSolicitudesServicios.createIndex("CedulaOPasaporte", "CedulaOPasaporte", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Nombre1", "Nombre1", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Nombre2", "Nombre2", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Apellido1", "Apellido1", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Apellido2", "Apellido2", { unique: false });
        objectStoreSolicitudesServicios.createIndex("CorreoElectronico", "CorreoElectronico", { unique: false });
        objectStoreSolicitudesServicios.createIndex("TelefonoResidencial", "TelefonoResidencial", { unique: false });
        objectStoreSolicitudesServicios.createIndex("TelefonoMobil", "TelefonoMobil", { unique: false });
        objectStoreSolicitudesServicios.createIndex("TelefonoOtro", "TelefonoOtro", { unique: false });
        objectStoreSolicitudesServicios.createIndex("ProvinciaId", "ProvinciaId", { unique: false });
        objectStoreSolicitudesServicios.createIndex("MunicipioId", "MunicipioId", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Sector", "Sector", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Barrio", "Barrio", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Calle", "Calle", { unique: false });
        objectStoreSolicitudesServicios.createIndex("NumeroVivienda", "NumeroVivienda", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Manzana", "Manzana", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Apartamento", "Apartamento", { unique: false });
        objectStoreSolicitudesServicios.createIndex("Edificio", "Edificio", { unique: false });
        objectStoreSolicitudesServicios.createIndex("EntreLaCalle", "EntreLaCalle", { unique: false });
        objectStoreSolicitudesServicios.createIndex("YLaCalle", "YLaCalle", { unique: false });

        objectStoreSolicitudesServicios.transaction.oncomplete = (e) => {
            console.log('Tabla Solicitudes ha sido creada');
        }

        const objectStoreGestantes = db.createObjectStore('Gestantes', { keyPath: 'IdNN' });

        objectStoreGestantes.createIndex('IdNN', 'IdNN', { unique: true });
        objectStoreGestantes.createIndex("Nombre1", "Nombre1", { unique: false });
        objectStoreGestantes.createIndex("Nombre2", "Nombre2", { unique: false });
        objectStoreGestantes.createIndex("Apellido1", "Apellido1", { unique: false });
        objectStoreGestantes.createIndex("Apellido2", "Apellido2", { unique: false });
        objectStoreGestantes.createIndex("Sexo", "Sexo", { unique: false });
        objectStoreGestantes.createIndex("Edad", "Edad", { unique: false });
        objectStoreGestantes.createIndex("FechaNacimiento", "FechaNacimiento", { unique: false });

        objectStoreGestantes.transaction.oncomplete = (e) => {
            console.log('Tabla Gestantes ha sido creada');
        }

    }
}

function abrirBaseDeDatos(nombreDB, storeName) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(nombreDB, 1);

        //request.onupgradeneeded = (event) => {
        //    const db = event.target.result;
        //    if (!db.objectStoreNames.contains(storeName)) {
        //        db.createObjectStore(storeName, { keyPath: "IdNN" });
        //        console.log(`Object store '${storeName}' creado.`);
        //    }
        //};

        request.onsuccess = (event) => {
            resolve(event.target.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

const crearRegistrosVacios = () => {
    for (let i = 1; i <= cantidadNNs; i++) {
        const nino = {
            IdNN: i,
            Nombre1: "",
            Nombre2: "",
            Apellido1: "",
            Apellido2: "",
            Sexo: "",
            Edad: "",
            FechaNacimiento: ""
        }
        ninos.push(nino);
    }
    agregarNNs(ninos);
}

const agregarNNs = (ninos) => {
    const request = indexedDB.open('SolicitudesServicios', 1);
    request.onsuccess = (e) => {
        console.log('abrir database');
        db = request.result;

        const transaction = db.transaction('Ninos', 'readwrite');

        transaction.oncomplete = function (event) {
            console.log('Todos los nns han sido agregados');
        };

        transaction.onerror = function (event) {
            console.log('No se pudieron agregar los nns');
        };

        const objectStoreNNs = transaction.objectStore('Ninos');

        for (nino of ninos) {
            agregar = objectStoreNNs.add(nino);

            agregar.onsuccess = () => {
                // request.result contains key of the added object
                console.log(`Nuevo nn agregado, IdNN: ${request.result}`);
            }

            agregar.onerror = (err) => {
                console.error(`Error al agregar nn: ${err}`)
            }
        }
    }
}

const agregarNN = (nino) => {
    const request = indexedDB.open('SolicitudesServicios', 1);
    request.onsuccess = (e) => {
        console.log('abrir database para agregar nn');
        db = request.result;

        const transaction = db.transaction('Ninos', 'readwrite');
        const objectStoreNNs = transaction.objectStore('Ninos');
        agregar = objectStoreNNs.add(nino);

        agregar.onsuccess = () => {
            // request.result contains key of the added object
            console.log(`Nuevo nn agregado, IdNN: ${request.result}`);
        }

        agregar.onerror = (err) => {
            console.error(`Error al agregar nn: ${err}`)
        }
    }
}




//var db = openDatabase('SolicitudesServicios', '1.0', '', 2 * 1024 * 1024);

//db.transaction(function (tx) {
//    tx.executeSql('DROP TABLE Ninos');
//    tx.executeSql('DROP TABLE Solicitudes');

//    tx.executeSql('CREATE TABLE Ninos (IdNN unique, Nombre1, Nombre2, Apellido1, Apellido2, Sexo, Edad, FechaNacimiento)');
//    tx.executeSql('CREATE TABLE Solicitudes(IdSolicitud unique,TipoDocumentoIdentidad,DocumentoIdentidad,TipoCedulaOPasaporte,CedulaOPasaporte,Nombre1,Nombre2,Apellido1,Apellido2,CorreoElectronico,TelefonoResidencial,TelefonoMobil,TelefonoOtro,ProvinciaId,MunicipioId,Sector,Barrio,Calle,NumeroVivienda,Manzana,Apartamento,Edificio,EntreLaCalle,YLaCalle)');
//});

//function Agregar(IdNN, Nombre1, Nombre2, Apellido1, Apellido2, Sexo, Edad, FechaNacimiento) {
//    db.transaction(function (tx) {
//        tx.executeSql('INSERT INTO Ninos (IdNN, Nombre1) VALUES (2, "Marcos")');
//    });
//}
//let db;
//let request = indexedDB.open("SolicitudesServicios", 1);

//request.onsuccess = function (event) {
//    db = event.target.result; // Aquí se asigna db correctamente
//    console.log("Base de datos abierta:", db);
//};

//request.onerror = function (event) {
//    console.error("Error al abrir la base de datos:", event.target.error);
//};

//request.onupgradeneeded = function (event) {
//    let db = event.target.result;
//    if (!db.objectStoreNames.contains("Ninos")) {
//        db.createObjectStore("Ninos", { keyPath: "id" });
//    }
//};

//// Definir las tablas (object stores) cuando la base de datos se crea o actualiza
//request.onupgradeneeded = function (event) {
//    db = event.target.result;

//    // Crear object store para "Ninos"
//    if (!db.objectStoreNames.contains("Ninos")) {
//        let objectStore = db.createObjectStore("Ninos", { keyPath: "IdNN" }); // 'IdNN' es la clave primaria
//        objectStore.createIndex("Nombre1", "Nombre1", { unique: false });
//        objectStore.createIndex("Nombre2", "Nombre2", { unique: false });
//        objectStore.createIndex("Apellido1", "Apellido1", { unique: false });
//        objectStore.createIndex("Apellido2", "Apellido2", { unique: false });
//        objectStore.createIndex("Sexo", "Sexo", { unique: false });
//        objectStore.createIndex("Edad", "Edad", { unique: false });
//        objectStore.createIndex("FechaNacimiento", "FechaNacimiento", { unique: false });
//    }

//    // Crear object store para "Solicitudes"
//    if (!db.objectStoreNames.contains("Solicitudes")) {
//        let objectStore = db.createObjectStore("Solicitudes", { keyPath: "IdSolicitud" });
//        objectStore.createIndex("TipoDocumentoIdentidad", "TipoDocumentoIdentidad", { unique: false });
//        objectStore.createIndex("DocumentoIdentidad", "DocumentoIdentidad", { unique: false });
//        // Agrega otros índices según sea necesario
//    }
//};

//// Manejar errores al abrir la base de datos
//request.onerror = function (event) {
//    console.error("Error al abrir la base de datos IndexedDB:", event.target.error);
//};

// Función para agregar un registro a la tabla "Ninos"
function AgregarNino(IdNN, Nombre1, Nombre2, Apellido1, Apellido2, Sexo, Edad, FechaNacimiento) {
    let transaction = db.transaction(["Ninos"], "readwrite");
    let objectStore = transaction.objectStore("Ninos");

    let nino = {
        IdNN: IdNN,
        Nombre1: Nombre1,
        Nombre2: Nombre2,
        Apellido1: Apellido1,
        Apellido2: Apellido2,
        Sexo: Sexo,
        Edad: Edad,
        FechaNacimiento: FechaNacimiento
    };

    let request = objectStore.add(nino); // Agregar el nuevo registro
    request.onsuccess = function () {
        console.log("Registro agregado exitosamente a Ninos");
    };

    request.onerror = function (event) {
        console.error("Error al agregar el registro:", event.target.error);
    };
}

function CrearRegistros(IdNN) {
    //db.transaction(function (tx) {
    //    tx.executeSql('INSERT INTO Ninos (IdNN) VALUES (' + IdNN + ')');
    //});
    const nino = { IdNN: i, Nombre1: "", Nombre2: "", Apellido1: "", Apellido2: "", Sexo: "", Edad: "", FechaNacimiento: "" }
    agregarNN(nino);
}

function CrearRegistrosGestantes(IdGestante) {
    db.transaction(function (tx) {
        tx.executeSql('INSERT INTO Gestantes (IdGestante) VALUES (' + IdGestante + ')');
    });
}

//function Actualizar(IdNN) {
//    var Nombre1 = document.getElementById("Nombre1N" + IdNN).value;
//    var Nombre2 = document.getElementById("Nombre2N" + IdNN).value;
//    var Apellido1 = document.getElementById("Apellido1N" + IdNN).value;
//    var Apellido2 = document.getElementById("Apellido2N" + IdNN).value;
//    var Sexo = document.getElementById("SexoN" + IdNN).value;
//    var Edad = document.getElementById("EdadN" + IdNN).value;
//    var FechaNacimiento = document.getElementById("FechaNacimientoN" + IdNN).value;

//    db.transaction(function (tx) {
//        var data = '';
//        data += 'Nombre1 = "' + Nombre1 + '", ';
//        data += 'Nombre2 = "' + Nombre2 + '", ';
//        data += 'Apellido1 = "' + Apellido1 + '", ';
//        data += 'Apellido2 = "' + Apellido2 + '", ';
//        data += 'Sexo = "' + Sexo + '", ';
//        data += 'Edad = "' + Edad + '", ';
//        data += 'FechaNacimiento = "' + FechaNacimiento + '"';
//        var query = 'UPDATE Ninos SET ' + data + ' WHERE IdNN = ' + IdNN;

//        tx.executeSql(query);
//        GenerarJson();
//    });
//}

async function Actualizar(IdNN) {
    // Obtener los valores de los campos
    const Nombre1 = document.getElementById("Nombre1N" + IdNN).value;
    const Nombre2 = document.getElementById("Nombre2N" + IdNN).value;
    const Apellido1 = document.getElementById("Apellido1N" + IdNN).value;
    const Apellido2 = document.getElementById("Apellido2N" + IdNN).value;
    const Sexo = document.getElementById("SexoN" + IdNN).value;
    const Edad = document.getElementById("EdadN" + IdNN).value;
    const FechaNacimiento = document.getElementById("FechaNacimientoN" + IdNN).value;

    // Base de datos y object store
    const nombreDB = "SolicitudesServicios";
    const storeName = "Ninos";

    try {
        // Abrir la base de datos
        let db = await abrirBaseDeDatos(nombreDB, storeName);

        // Crear una transacción para leer y escribir
        const transaction = db.transaction(storeName, "readwrite");
        const store = transaction.objectStore(storeName);

        // Obtener el registro actual por ID
        const registroExistente = await new Promise((resolve, reject) => {
            const request = store.get(IdNN);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });

        if (registroExistente) {
            // Actualizar los valores del registro
            registroExistente.Nombre1 = Nombre1;
            registroExistente.Nombre2 = Nombre2;
            registroExistente.Apellido1 = Apellido1;
            registroExistente.Apellido2 = Apellido2;
            registroExistente.Sexo = Sexo;
            registroExistente.Edad = Edad;
            registroExistente.FechaNacimiento = FechaNacimiento;

            // Guardar el registro actualizado
            await new Promise((resolve, reject) => {
                const updateRequest = store.put(registroExistente);
                updateRequest.onsuccess = () => resolve();
                updateRequest.onerror = () => reject(updateRequest.error);
            });

            console.log(`Registro con IdNN = ${IdNN} actualizado exitosamente.`);
        } else {
            // Crear un nuevo registro si no existe
            const nuevoRegistro = {
                IdNN,
                Nombre1,
                Nombre2,
                Apellido1,
                Apellido2,
                Sexo,
                Edad,
                FechaNacimiento,
            };

            await new Promise((resolve, reject) => {
                const addRequest = store.add(nuevoRegistro);
                addRequest.onsuccess = () => resolve();
                addRequest.onerror = () => reject(addRequest.error);
            });

            console.log(`Nuevo registro con IdNN = ${IdNN} creado exitosamente.`);
        }

        // Cerrar la conexión
        db.close();

        // Llamar a la función GenerarJson() (si es necesaria)
        GenerarJson();
    } catch (error) {
        console.error("Error al actualizar o crear el registro:", error);
    }
}


function ActualizarGestante(IdGestante) {
    var Nombre1 = document.getElementById("Nombre1G" + IdGestante).value;
    var Nombre2 = document.getElementById("Nombre2G" + IdGestante).value;
    var Apellido1 = document.getElementById("Apellido1G" + IdGestante).value;
    var Apellido2 = document.getElementById("Apellido2G" + IdGestante).value;
    var Sexo = document.getElementById("SexoG" + IdGestante).value;
    var FechaNacimiento = document.getElementById("FechaNacimientoG" + IdGestante).value;
    var DocumentoIdentidad = document.getElementById("DocumentoIdentidadG" + IdGestante).value;

    db.transaction(function (tx) {
        var data = '';
        data += 'Nombre1 = "' + Nombre1 + '", ';
        data += 'Nombre2 = "' + Nombre2 + '", ';
        data += 'Apellido1 = "' + Apellido1 + '", ';
        data += 'Apellido2 = "' + Apellido2 + '", ';
        data += 'Sexo = "' + Sexo + '", ';
        data += 'DocumentoIdentidad = "' + DocumentoIdentidad + '", ';
        data += 'FechaNacimiento = "' + FechaNacimiento + '"';
        var query = 'UPDATE Gestantes SET ' + data + ' WHERE IdGestante = ' + IdGestante;

        tx.executeSql(query);
        GenerarJson();
    });
}

function Eliminar(IdNN) {
    if (IdNN == $("#CantidadNinos").val()) {
        $('#form_nino' + IdNN).remove();
        $("#CantidadNinos").val($("#CantidadNinos").val() - 1);

        if ($("#CantidadNinos").val() > 1) {
            $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niños/as.");
        }
        else {
            $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niño/a.");
        }

        if ($("#CantidadNinos").val() === "0") {
            $("#CantidadNinos").removeAttr('readOnly');

            $("#divCantidadNNs").show();
            $("#lblCantidadNNs").html("");
            $("#lblCantidadNNs").attr("visible", false);
            $("#btnPaso4").attr("disabled", true);
            
        }

        GenerarJson();
    }
    else {
        ////alert('ES NECESARIO ELIMINAR EL ÚLTIMO NIÑO/A');
    }
}

function EliminarGestante(IdGestante) {
    if (IdGestante == $("#CantidadGestantes").val()) {
        $('#form_gestante' + IdGestante).remove();
        $("#CantidadGestantes").val($("#CantidadGestantes").val() - 1);

        if ($("#CantidadGestantes").val() > 1) {
            $("#lblCantidadGestantes").html("Está solicitando servicio para <b><u>" + $("#CantidadGestantes").val() + "</u></b>&nbsp;gestantes.");
        } else {
            $("#lblCantidadGestantes").html("Está solicitando servicio para <b><u>" + $("#CantidadGestantes").val() + "</u></b>&nbsp;gestante.");
        }

        if ($("#CantidadGestantes").val() === "0") {
            $("#CantidadGestantes").removeAttr('readOnly');

            $("#divCantidadGestantes").show();
            $("#lblCantidadGestantes").html("");
            $("#lblCantidadGestantes").attr("visible", false);
        }

        GenerarJson();
    }
    else {
        //alert('ES NECESARIO ELIMINAR A LA ÚLTIMA GESTANTE');
    }
}

//function Registros() {
//    $("#contenedor_form_ninos").html('');
//    db.transaction(function (tx) {
//        tx.executeSql('SELECT * FROM Ninos', [], function (txt, results) {
//            var len = results.rows.length, i;
//            if (len > 0) {
//                for (i = 0; i < len; i++) {
//                    formulario = AgregarFormularioNinosEdit(i + 1, results.rows.item(i).IdNN, results.rows.item(i).Nombre1, results.rows.item(i).Nombre2, results.rows.item(i).Apellido1, results.rows.item(i).Apellido2, results.rows.item(i).Sexo, results.rows.item(i).Edad, results.rows.item(i).FechaNacimiento);
//                    $("#contenedor_form_ninos").append(formulario);

//                    $("#CantidadNinos").attr('readOnly', true);
//                    $("#btnAgregarNino").show();
//                }
//                $("#CantidadNinos").val(i);
//            }
//        });

//    });

//}

const Registros = async () => {
    
    const nombreDB = "SolicitudesServicios";
    const storeName = "Ninos";

    let db = await abrirBaseDeDatos(nombreDB, storeName);

    let transaction = db.transaction(["Ninos"], "readonly");
    let objectStore = transaction.objectStore("Ninos");

    let request = objectStore.getAll();

    request.onsuccess = function (event) {
        let results = event.target.result; // Lista de registros
        if (results.length > 0) {
            for (let i = 0; i < results.length; i++) {
                let nino = results[i];
                // Usar la función AgregarFormularioNinosEdit para generar el formulario
                let formulario = AgregarFormularioNinosEdit(
                    i + 1,
                    nino.IdNN,
                    nino.Nombre1,
                    nino.Nombre2,
                    nino.Apellido1,
                    nino.Apellido2,
                    nino.Sexo,
                    nino.Edad,
                    nino.FechaNacimiento
                );
                // Agregar el formulario al contenedor
                $("#contenedor_form_ninos").append(formulario);

                // Ajustar el estado del formulario
                $("#CantidadNinos").attr('readOnly', true);
                $("#btnAgregarNino").show();
            }
            // Actualizar el valor de "CantidadNinos"
            $("#CantidadNinos").val(results.length);
        } else {
            console.log("No se encontraron registros en la tabla Ninos.");
        }
    };

    request.onerror = function (event) {
        console.error("Error al cargar los registros:", event.target.error);
    };
}


const RegistrosGestantes = async () => {

    const nombreDB = "SolicitudesServicios";
    const storeName = "Gestantes";

    let db = await abrirBaseDeDatos(nombreDB, storeName);

    let transaction = db.transaction(["Gestantes"], "readonly");
    let objectStore = transaction.objectStore("Gestantes");

    let request = objectStore.getAll();

    request.onsuccess = function (event) {
        let results = event.target.result; // Lista de registros
        if (results.length > 0) {
            for (let i = 0; i < results.length; i++) {
                let nino = results[i];
                // Usar la función AgregarFormularioNinosEdit para generar el formulario
                let formulario = AgregarFormularioNinosEdit(
                    i + 1,
                    nino.IdNN,
                    nino.Nombre1,
                    nino.Nombre2,
                    nino.Apellido1,
                    nino.Apellido2,
                    nino.Sexo,
                    nino.Edad,
                    nino.FechaNacimiento
                );
                // Agregar el formulario al contenedor
                $("#contenedor_form_info_gestante").append(formulario);

                // Ajustar el estado del formulario
                $("#CantidadGestantes").attr('readOnly', true);
                $("#btnAgregarGestante").show();
            }
            // Actualizar el valor de "CantidadNinos"
            $("#CantidadGestantes").val(results.length);
        } else {
            console.log("No se encontraron registros en la tabla gestantes.");
        }
    };

    request.onerror = function (event) {
        console.error("Error al cargar los registros:", event.target.error);
    };
}



//function RegistrosGestantes() {
//    $("#contenedor_form_info_gestante").html('');
//    db.transaction(function (tx) {
//        tx.executeSql('SELECT * FROM Gestantes', [], function (txt, results) {
//            var len = results.rows.length, i;
//            if (len > 0) {
//                for (i = 0; i < len; i++) {
//                    formulario = AgregarFormularioGestanteEdit(i + 1, results.rows.item(i).IdNN, results.rows.item(i).Nombre1, results.rows.item(i).Nombre2, results.rows.item(i).Apellido1, results.rows.item(i).Apellido2, results.rows.item(i).Sexo, results.rows.item(i).DocumentoIdentidad, results.rows.item(i).FechaNacimiento);
//                    $("#contenedor_form_info_gestante").append(formulario);

//                    // $("#CantidadGestantes").attr('readOnly', true);
//                    $("#btnAgregarGestante").show();
//                }
//                $("#CantidadGestantes").val(i);
//            }
//            else {
//                $("#contenedor_form_info_gestante").html("");
//            }
//        });

//    });

//}

//const RegistrosGestantes = async () => {
//    $("#contenedor_form_info_gestante").html(""); // Limpiar el contenedor.

//    // Limpiar el contenedor antes de mostrar los registros
//    //$("#contenedor_form_ninos").html('');

//    //if (!db) {
//    //    console.error("La base de datos no está disponible todavía.");
//    //    return;
//    //}
//    // Base de datos y object store
//    const nombreDB = "SolicitudesServicios";
//    const storeName = "Gestantes";

//    // Abrir la base de datos
//    let db = await abrirBaseDeDatos(nombreDB, storeName);

//    // Abrir una transacción de solo lectura en la tabla "Ninos"
//    let transaction = db.transaction(["Gestantes"], "readonly");
//    let objectStore = transaction.objectStore("Gestantes");

//    // Usar getAll() para obtener todos los registros
//    let request = objectStore.getAll();

//    request.onsuccess = function (event) {
//        //let db = event.target.result;
//    //let db = await abrirBaseDeDatos(nombreDB, storeName);

//        // Iniciar una transacción de solo lectura en el almacén "Gestantes".
//        let transaction = db.transaction(["Gestantes"], "readonly");
//        let objectStore = transaction.objectStore("Gestantes");

//        // Obtener todos los registros del almacén.
//        let getAllRequest = objectStore.getAll();

//        getAllRequest.onsuccess = function (event) {
//            let results = event.target.result;

//            if (results.length > 0) {
//                results.forEach((gestante, index) => {
//                    // Crear el formulario utilizando los datos obtenidos.
//                    let formulario = AgregarFormularioGestanteEdit(
//                        index + 1,
//                        gestante.IdNN,
//                        gestante.Nombre1,
//                        gestante.Nombre2,
//                        gestante.Apellido1,
//                        gestante.Apellido2,
//                        gestante.Sexo,
//                        gestante.DocumentoIdentidad,
//                        gestante.FechaNacimiento
//                    );

//                    // Agregar el formulario al contenedor.
//                    $("#contenedor_form_info_gestante").append(formulario);

//                    // Mostrar el botón de agregar gestante.
//                    $("#btnAgregarGestante").show();
//                });

//                // Actualizar la cantidad de gestantes.
//                $("#CantidadGestantes").val(results.length);
//            } else {
//                $("#contenedor_form_info_gestante").html(""); // No hay datos.
//            }
//        };

//        getAllRequest.onerror = function (event) {
//            console.error("Error al obtener los registros de Gestantes:", event.target.error);
//        };
//    };

//    request.onerror = function (event) {
//        console.error("Error al abrir la base de datos:", event.target.error);
//    };
//}



/*------------------ NO SE USA ------------------*/
function Salvar() {
    var TipoDocumentoIdentidad = document.getElementById("TipoDocumentoIdentidad").value;
    var DocumentoIdentidad = document.getElementById("DocumentoIdentidad").value;
    var TipoCedulaOPasaporte = document.getElementById("TipoCedulaOPasaporte").value;
    var CedulaOPasaporte = document.getElementById("CedulaOPasaporte").value;
    var Nombre1 = document.getElementById("Nombre1").value;
    var Nombre2 = document.getElementById("Nombre2").value;
    var Apellido1 = document.getElementById("Apellido1").value;
    var Apellido2 = document.getElementById("Apellido2").value;
    var CorreoElectronico = document.getElementById("CorreoElectronico").value;
    var TelefonoResidencial = document.getElementById("TelefonoResidencial").value;
    var TelefonoMobil = document.getElementById("TelefonoMobil").value;
    var TelefonoOtro = document.getElementById("TelefonoOtro").value;
    //var ProvinciaId = document.getElementById("ProvinciaId").value;
    //var MunicipioId = document.getElementById("MunicipioId").value;
    var Sector = document.getElementById("Sector").value;
    var Barrio = document.getElementById("Barrio").value;
    var Calle = document.getElementById("Calle").value;
    var NumeroVivienda = document.getElementById("NumeroVivienda").value;
    var Manzana = document.getElementById("Manzana").value;
    var Apartamento = document.getElementById("Apartamento").value;
    var Edificio = document.getElementById("Edificio").value;
    var EntreLaCalle = document.getElementById("EntreLaCalle").value;
    var YLaCalle = document.getElementById("YLaCalle").value;
    var query = 'INSERT INTO Solicitudes (TipoDocumentoIdentidad,DocumentoIdentidad,TipoCedulaOPasaporte,CedulaOPasaporte,Nombre1,Nombre2,Apellido1,Apellido2,CorreoElectronico,TelefonoResidencial,TelefonoMobil,TelefonoOtro,ProvinciaId,MunicipioId,Sector,Barrio,Calle,NumeroVivienda,Manzana,Apartamento,Edificio,EntreLaCalle,YLaCalle) VALUES ("' + TipoDocumentoIdentidad + '","' + DocumentoIdentidad + '","' + TipoCedulaOPasaporte + '","' + CedulaOPasaporte + '","' + Nombre1 + '","' + Nombre2 + '","' + Apellido1 + '","' + Apellido2 + '","' + CorreoElectronico + '","' + TelefonoResidencial + '","' + TelefonoMobil + '","' + TelefonoOtro + '","' + ProvinciaId + '","' + MunicipioId + '","' + Sector + '","' + Barrio + '","' + Calle + '",",' + NumeroVivienda + '","' + Manzana + '","' + Apartamento + '","' + Edificio + '","' + EntreLaCalle + '","' + YLaCalle + '")';

    db.transaction(function (tx) {
        tx.executeSql(query);
    });
}

function JsonBasico() {
    //alert('generar json');
    var IdSolicitud = 0;
    var TipoDocumentoIdentidad = document.getElementById("TipoDocumentoIdentidad").value;
    var DocumentoIdentidad = document.getElementById("DocumentoIdentidad").value;
    var TipoCedulaOPasaporte = document.getElementById("TipoCedulaOPasaporte").value;
    var CedulaOPasaporte = document.getElementById("CedulaOPasaporte").value;
    var Contrasena = document.getElementById("Contrasena").value;
    var ConfirmarContrasena = document.getElementById("ConfirmarContrasena").value;
    var Nombre1 = document.getElementById("Nombre1").value;
    var Nombre2 = document.getElementById("Nombre2").value;
    var Apellido1 = document.getElementById("Apellido1").value;
    var Apellido2 = document.getElementById("Apellido2").value;
    var CorreoElectronico = document.getElementById("CorreoElectronico").value;
    var TelefonoResidencial = document.getElementById("TelefonoResidencial").value;
    var TelefonoMobil = document.getElementById("TelefonoMobil").value;
    var TelefonoOtro = document.getElementById("TelefonoOtro").value;
    //var ProvinciaId = document.getElementById("ProvinciaId").value;
    //var MunicipioId = document.getElementById("MunicipioId").value;
    var Sector = document.getElementById("Sector").value;
    var Barrio = document.getElementById("Barrio").value;
    var Calle = document.getElementById("Calle").value;
    var NumeroVivienda = document.getElementById("NumeroVivienda").value;
    var Manzana = document.getElementById("Manzana").value;
    var Apartamento = document.getElementById("Apartamento").value;
    var Edificio = document.getElementById("Edificio").value;
    var EntreLaCalle = document.getElementById("EntreLaCalle").value;
    var YLaCalle = document.getElementById("YLaCalle").value;

    var json = '';
    json += '[{';
    json += '"IdSolicitud": "' + IdSolicitud + '",';
    json += '"TipoDocumentoIdentidad": "' + TipoDocumentoIdentidad + '",';
    json += '"DocumentoIdentidad": "' + DocumentoIdentidad + '",';
    json += '"TipoCedulaOPasaporte": "' + TipoCedulaOPasaporte + '",';
    json += '"CedulaOPasaporte": "' + CedulaOPasaporte + '",';
    json += '"Contrasena": "' + Contrasena + '",';
    json += '"ConfirmarContrasena": "' + ConfirmarContrasena + '",';
    json += '"Nombre1": "' + Nombre1 + '",';
    json += '"Nombre2": "' + Nombre2 + '",';
    json += '"Apellido1": "' + Apellido1 + '",';
    json += '"Apellido2": "' + Apellido2 + '",';
    json += '"CorreoElectronico": "' + CorreoElectronico + '",';
    json += '"TelefonoResidencial": "' + TelefonoResidencial + '",';
    json += '"TelefonoMobil": "' + TelefonoMobil + '",';
    json += '"TelefonoOtro": "' + TelefonoOtro + '",';
    //json += '"ProvinciaId": "' + ProvinciaId + '",';
    //json += '"MunicipioId": "' + MunicipioId + '",';
    json += '"Sector": "' + Sector + '",';
    json += '"Barrio": "' + Barrio + '",';
    json += '"Calle": "' + Calle + '",';
    json += '"NumeroVivienda": "' + NumeroVivienda + '",';
    json += '"Manzana": "' + Manzana + '",';
    json += '"Apartamento": "' + Apartamento + '",';
    json += '"Edificio": "' + Edificio + '",';
    json += '"EntreLaCalle": "' + EntreLaCalle + '",';
    json += '"YLaCalle": "' + YLaCalle + '",';
    json += '"Ninos": []';
    json += '"Gestantes": []';
    json += '}]';

    $("#DataJson").val(json);
}

var Georeferencia = "";
function GenerarJson() {
    var IdSolicitud = document.getElementById("IdSolicitud").value;

    if (IdSolicitud.length == 0) {
        IdSolicitud = 0;
    }

    var TipoDocumentoIdentidad = document.getElementById("TipoDocumentoIdentidad").value;
    var DocumentoIdentidad = document.getElementById("DocumentoIdentidad").value;
    var TipoCedulaOPasaporte = document.getElementById("TipoCedulaOPasaporte").value;
    var CedulaOPasaporte = document.getElementById("CedulaOPasaporte").value;
    var Contrasena = document.getElementById("Contrasena").value;
    var ConfirmarContrasena = document.getElementById("ConfirmarContrasena").value;
    var Nombre1 = document.getElementById("Nombre1").value;
    var Nombre2 = document.getElementById("Nombre2").value;
    var Apellido1 = document.getElementById("Apellido1").value;
    var Apellido2 = document.getElementById("Apellido2").value;
    var CorreoElectronico = document.getElementById("CorreoElectronico").value;
    var TelefonoResidencial = document.getElementById("TelefonoResidencial").value;
    var TelefonoMobil = document.getElementById("TelefonoMobil").value;
    var TelefonoOtro = document.getElementById("TelefonoOtro").value;
    //var ProvinciaId = document.getElementById("ProvinciaId").value;
    //var MunicipioId = document.getElementById("MunicipioId").value;
    var IdMunicipio = cboMunicipios.GetValue();
    var Sector = document.getElementById("Sector").value;
    var Barrio = document.getElementById("Barrio").value;
    var Calle = document.getElementById("Calle").value;
    var NumeroVivienda = document.getElementById("NumeroVivienda").value;
    var Manzana = document.getElementById("Manzana").value;
    var Apartamento = document.getElementById("Apartamento").value;
    var Edificio = document.getElementById("Edificio").value;
    var EntreLaCalle = document.getElementById("EntreLaCalle").value;
    var YLaCalle = document.getElementById("YLaCalle").value;
    navigator.geolocation.getCurrentPosition(function (position) { Georeferencia = (position.coords.latitude + ', ' + position.coords.longitude); });

    var Ninos = '';
    var len = document.getElementById("CantidadNinos").value
    if (len > 0) {
        len = (Number(len) + 1);
        for (i = 1; i < len; i++) {
            Ninos += '{"Nombre1": "' + document.getElementById("Nombre1N" + i).value + '",'
            Ninos += '"Nombre2": "' + document.getElementById("Nombre2N" + i).value + '",';
            Ninos += '"Apellido1": "' + document.getElementById("Apellido1N" + i).value + '",';
            Ninos += '"Apellido2": "' + document.getElementById("Apellido2N" + i).value + '",';
            Ninos += '"Sexo": "' + document.getElementById("SexoN" + i).value + '",';
            Ninos += '"Edad": "' + document.getElementById("EdadN" + i).value + '",';
            Ninos += '"FechaNacimiento": "' + document.getElementById("FechaNacimientoN" + i).value + '"';
            Ninos += '},';
        }
        Ninos = Ninos.slice(0, -1);
    }

    var Gestantes = '';
    var lenG = document.getElementById("CantidadGestantes").value;
    if (lenG > 0) {
        lenG = (Number(lenG) + 1);
        for (var o = 1; o < lenG; o++) {
            Gestantes += '{"Nombre1": "' + document.getElementById("Nombre1G" + o).value + '",'
            Gestantes += '"Nombre2": "' + document.getElementById("Nombre2G" + o).value + '",';
            Gestantes += '"Apellido1": "' + document.getElementById("Apellido1G" + o).value + '",';
            Gestantes += '"Apellido2": "' + document.getElementById("Apellido2G" + o).value + '",';
            Gestantes += '"Sexo": "' + document.getElementById("SexoG" + o).value + '",';
            Gestantes += '"DocumentoIdentidad": "' + document.getElementById("DocumentoIdentidadG" + o).value + '",';
            Gestantes += '"FechaNacimiento": "' + document.getElementById("FechaNacimientoG" + o).value + '"';
            Gestantes += '},';
        }

        Gestantes = Gestantes.slice(0, -1);
    }

    var json = '';
    json += '{';
    json += '"IdSolicitud": "' + IdSolicitud + '",';
    json += '"TipoDocumentoIdentidad": "' + TipoDocumentoIdentidad + '",';
    json += '"DocumentoIdentidad": "' + DocumentoIdentidad + '",';
    json += '"TipoCedulaOPasaporte": "' + TipoCedulaOPasaporte + '",';
    json += '"CedulaOPasaporte": "' + CedulaOPasaporte + '",';
    json += '"Contrasena": "' + Contrasena + '",';
    json += '"Nombre1": "' + Nombre1 + '",';
    json += '"Nombre2": "' + Nombre2 + '",';
    json += '"Apellido1": "' + Apellido1 + '",';
    json += '"Apellido2": "' + Apellido2 + '",';
    json += '"CorreoElectronico": "' + CorreoElectronico + '",';
    json += '"TelefonoResidencial": "' + TelefonoResidencial + '",';
    json += '"TelefonoMobil": "' + TelefonoMobil + '",';
    json += '"TelefonoOtro": "' + TelefonoOtro + '",';
    json += '"IdMunicipio": ' + IdMunicipio + ',';
    json += '"Sector": "' + Sector + '",';
    json += '"Barrio": "' + Barrio + '",';
    json += '"Calle": "' + Calle + '",';
    json += '"NumeroVivienda": "' + NumeroVivienda + '",';
    json += '"Manzana": "' + Manzana + '",';
    json += '"Apartamento": "' + Apartamento + '",';
    json += '"Edificio": "' + Edificio + '",';
    json += '"EntreLaCalle": "' + EntreLaCalle + '",';
    json += '"YLaCalle": "' + YLaCalle + '",';
    json += '"Georeferencia": "' + Georeferencia + '",';
    json += '"NNs": [' + Ninos + '],';
    json += '"Gestantes": [' + Gestantes + ']';
    json += '}';

    $("#DataJson").val(json);
}

function EliminarFormularioNino(indice) {
    $('#form_nino' + indice).remove();
    $("#CantidadNinos").val($("#CantidadNinos").val() - 1);

    var restantes = $("#CantidadNinos").val();

    var elementos = $('.header-form-nino');
    $.each(elementos, function (i, val) {
        $("#titulo" + i).html(i);
    });
}

function EliminarFormularioGestante(indice) {
    $('#form_gestante' + indice).remove();
    $("#CantidadGestantes").val($("#CantidadGestantes").val() - 1);

    var restantes = $("#CantidadGestantes").val();

    var elementos = $('.header-form-gestante');
    $.each(elementos, function (i, val) {
        $("#tituloGestante" + i).html(i);
    });
}

//Funcion para presentar el formulario para los niños
function AgregarFormularioNinos(indice) {
    var formulario = '<div class="form_ninos" id="form_nino' + indice + '">';
    formulario += '<div class="container_data">';
    formulario += '<h1 class="header-form-nino">Datos del niño/a #<span id="titulo' + indice + '">' + indice + '</span><button class="btn-close-form-ninos" type="button" onclick="Eliminar(' + indice + ');">Eliminar</button></h1>';
    formulario += '<div class="form-row"> ';
    formulario += '<div class="form-group col-md-3 required"> ';
    formulario += '<label for="NombreN1">Primer Nombre:</label> ';
    formulario += '<input type="text" onkeyup="Actualizar(' + indice + ')" class="form-control " id="Nombre1N' + indice + '" required="required" name="Nombre1N' + indice + '" /> ';
    formulario += '</div> ';
    formulario += ' <div class="form-group col-md-3"> ';
    formulario += '   <label for="NombreN2">Segundo Nombre:</label> ';
    formulario += '    <input onkeyup="Actualizar(' + indice + ')" type="text" class="form-control" id="Nombre2N' + indice + '"  /> ';
    formulario += '   </div> ';
    formulario += '    <div class="form-group col-md-3 required"> ';
    formulario += '      <label for="ApellidoN1">Primer Apellido:</label> ';
    formulario += '      <input onkeyup="Actualizar(' + indice + ')" type="text" class="form-control" id="Apellido1N' + indice + '" required="required" /> ';
    formulario += '   </div> ';
    formulario += '   <div class="form-group col-md-3"> ';
    formulario += '      <label for="ApellidoN2">Segundo Apellido:</label> ';
    formulario += '      <input onkeyup="Actualizar(' + indice + ')" type="text" class="form-control" id="Apellido2N' + indice + '" /> ';
    formulario += '   </div> ';
    formulario += ' </div> ';
    formulario += ' <div class="form-group row required"> ';
    formulario += '  <div class="form-group col-md-4"> ';
    formulario += '   <label for="Sexo">Sexo:</label> ';
    formulario += '    <select class="form-control" id="SexoN' + indice + '" name="SexoN' + indice + '" onChange="Actualizar(' + indice + ')" required="required"> ';
    formulario += '      <option value="" disabled="" selected="">Seleccione</option> ';
    formulario += '       <option value="1">Masculino</option> ';
    formulario += '        <option value="2">Femenino</option> ';
    formulario += '    </select> ';
    formulario += '  </div> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '     <label for="FechaNacimiento">Fecha de Nacimiento:</label> ';
    formulario += '     <input onkeyup="Actualizar(' + indice + ')" type="date" class="form-control" value="" id="FechaNacimientoN' + indice + '" name="FechaNacimientoN' + indice + '" required="required" /> ';
    formulario += '  </div> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '      <label for="Edad">Edad:</label> ';
    formulario += '    <select class="form-control" id="EdadN' + indice + '" name="EdadN' + indice + '" onchange="Actualizar(' + indice + '); VerificarEdad(' + indice + ')" required="required"> ';
    formulario += '      <option value="" disabled="" selected="">Seleccione</option> ';
    formulario += '      <option value="0">0 años</option> ';
    formulario += '        <option value="1">1 año</option> ';
    formulario += '        <option value="2">2 años</option> ';
    formulario += '        <option value="3">3 años</option> ';
    formulario += '        <option value="4">4 años</option> ';
    formulario += '    </select> ';
    formulario += '   </div> ';
    formulario += ' </div> ';
    formulario += '</div > ';
    formulario += ' </div> ';

    return formulario;
}

function AgregarFormularioGestantes(indice) {
    var formulario = '<div class="form_gestantes" id="form_gestante' + indice + '" novalidate>';
    formulario += '<div class="container_data">';
    formulario += '<h1 class="header-form-gestante">Datos de la gestante #<span id="tituloGestante' + indice + '">' + indice + '</span><button class="btn-close-form-gestantes" type="button" onclick="EliminarGestante(' + indice + ');">Eliminar</button></h1>';
    formulario += '<div class="form-row"> ';
    formulario += '<div class="form-group col-md-3 required"> ';
    formulario += '<label for="Nombre1G">Primer Nombre:</label> ';
    formulario += '<input type="text" onchange="ActualizarGestante(' + indice + ')" class="form-control " id="Nombre1G' + indice + '" required="required" name="Nombre1G' + indice + '" /> ';
    formulario += '</div> ';
    formulario += ' <div class="form-group col-md-3"> ';
    formulario += '   <label for="Nombre2G">Segundo Nombre:</label> ';
    formulario += '    <input onchange="ActualizarGestante(' + indice + ')" type="text" class="form-control" id="Nombre2G' + indice + '"  /> ';
    formulario += '   </div> ';
    formulario += '    <div class="form-group col-md-3 required"> ';
    formulario += '      <label for="Apellido1G">Primer Apellido:</label> ';
    formulario += '      <input onchange="ActualizarGestante(' + indice + ')" type="text" class="form-control" id="Apellido1G' + indice + '" required="required" /> ';
    formulario += '   </div> ';
    formulario += '   <div class="form-group col-md-3"> ';
    formulario += '      <label for="Apellido2G">Segundo Apellido:</label> ';
    formulario += '      <input onchange="ActualizarGestante(' + indice + ')" type="text" class="form-control" id="Apellido2G' + indice + '" /> ';
    formulario += '   </div> ';
    formulario += ' </div> ';
    formulario += ' <div class="form-group row required"> ';
    formulario += '  <div class="form-group col-md-4"> ';
    formulario += '   <label for="SexoG">Sexo:</label> ';
    formulario += '    <select class="form-control" id="SexoG' + indice + '" name="SexoG' + indice + '" onChange="ActualizarGestante(' + indice + ')" disabled="disabled" required="required"> ';
    formulario += '      <option value="" disabled="" selected="">Seleccione</option> ';
    formulario += '       <option value="1">Masculino</option> ';
    formulario += '        <option value="2" selected="selected">Femenino</option> ';
    formulario += '    </select> ';
    formulario += '  </div> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '     <label for="FechaNacimientoG">Fecha de Nacimiento:</label> ';
    formulario += '     <input onchange="ActualizarGestante(' + indice + ')" type="date" class="form-control" id="FechaNacimientoG' + indice + '" name="FechaNacimientoG' + indice + '" required="required" /> ';
    formulario += '  </div> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '      <label for="DocumentoIdentidadG">Documento Identidad:</label> ';
    formulario += '      <input type="text" onchange="ActualizarGestante(' + indice + ')" class="form-control" id="DocumentoIdentidadG' + indice + '" name="DocumentoIdentidadG' + indice + '" />';
    formulario += '   </div> ';
    //formulario += '  <div class="form-group col-md-4 required"> ';
    //formulario += '      <label for="EdadG">Edad:</label> ';
    //formulario += '    <select class="form-control" id="EdadG' + indice + '" name="EdadG' + indice + '" onchange="ActualizarGestante(' + indice + '); VerificarEdadGestante(' + indice + ')" required="required"> ';
    //formulario += '      <option value="" disabled="" selected="">Seleccione</option> ';
    //formulario += '      <option value="0">0 años</option> ';
    //formulario += '        <option value="1">1 año</option> ';
    //formulario += '        <option value="2">2 años</option> ';
    //formulario += '        <option value="3">3 años</option> ';
    //formulario += '        <option value="4">4 años</option> ';
    //formulario += '    </select> ';
    //formulario += '   </div> ';
    formulario += ' </div> ';
    formulario += '</div > ';
    formulario += ' </div> ';

    return formulario;
}

function addZero(i) {
    if (i < 10) {
        i = '0' + i;
    }
    return i;
}

function addZeroGestante(i) {
    if (i < 10) {
        i = '0' + i;
    }
    return i;
}

function hoyFecha() {
    var hoy = new Date();
    var dd = hoy.getDate();
    var mm = hoy.getMonth() + 1;
    var yyyy = hoy.getFullYear();

    dd = addZero(dd);
    mm = addZero(mm);

    return yyyy + '-' + mm + '-' + dd;
}

function hoyFechaGestante() {
    var hoy = new Date();
    var dd = hoy.getDate();
    var mm = hoy.getMonth() + 1;
    var yyyy = hoy.getFullYear();

    dd = addZeroGestante(dd);
    mm = addZeroGestante(mm);

    return yyyy + '-' + mm + '-' + dd;
}

function VerificarEdad(IdNN) {

    var fechaInicio = new Date($("#FechaNacimientoN" + IdNN).val()).getTime();
    var fechaFin = new Date(hoyFecha()).getTime();
    var edad = $("#EdadN" + IdNN).val();

    var diff = fechaFin - fechaInicio;
    var dias = diff / (1000 * 60 * 60 * 24);
    var anos = Math.floor(dias / 365);

    if (anos < edad) {
        $("#msjmodal").text("La edad no puede ser mayor que la fecha nacimiento");
        $("#ModalMensaje").modal("show");
    }
    if (anos > edad) {
        $("#msjmodal").text("La edad no puede ser menor que la fecha nacimiento");
        $("#ModalMensaje").modal("show");
    }
}

function VerificarEdadGestante(IdGestante) {

    var fechaInicio = new Date($("#FechaNacimientoG" + IdGestante).val()).getTime();
    var fechaFin = new Date(hoyFecha()).getTime();
    var edad = $("#EdadG" + IdGestante).val();

    var diff = fechaFin - fechaInicio;
    var dias = diff / (1000 * 60 * 60 * 24);
    var anos = Math.floor(dias / 365);

    if (anos < edad) {
        $("#msjmodal").text("La edad no puede ser mayor que la fecha nacimiento");
        $("#ModalMensaje").modal("show");
    }
    if (anos > edad) {
        $("#msjmodal").text("La edad no puede ser menor que la fecha nacimiento");
        $("#ModalMensaje").modal("show");
    }
}

function AgregarFormularioNinosEdit(indice, IdNN, Nombre1, Nombre2, Apellido1, Apellido2, Sexo, Edad, FechaNacimiento) {
    var script = '<script>$("#SexoN' + IdNN + ' option[value = ' + Sexo + ']").attr("selected", "selected");</script>';
    if (Sexo === '') script = '';
    if (FechaNacimiento === '') FechaNacimiento = '';

    var formulario = '<div class="form_ninos" id="form_nino' + indice + '">';
    formulario += '<div class="container_data">';
    formulario += '<h1 class="header-form-nino">Datos del niño/a #<span id="titulo' + indice + '">' + indice + '</span><button class="btn-close-form-ninos" type="button" onclick="Eliminar(' + IdNN + ');">Eliminar</button></h1>';
    formulario += '<div class="form-row"> ';
    formulario += '<div class="form-group col-md-3 required">';
    formulario += '<label for="NombreN1">Primer Nombre:</label> ';
    formulario += '<input type="text" onkeyup="Actualizar(' + IdNN + ')" class="form-control" id="Nombre1N' + IdNN + '" name="Nombre1N' + IdNN + '" value="' + Nombre1 + '" required="required" /> ';
    formulario += '</div> ';
    formulario += ' <div class="form-group col-md-3"> ';
    formulario += '   <label for="NombreN2">Segundo Nombre:</label>';
    formulario += '    <input type="text" onkeyup="Actualizar(' + IdNN + ')" class="form-control" id="Nombre2N' + IdNN + '" value="' + Nombre2 + '" /> ';
    formulario += '   </div> ';
    formulario += '    <div class="form-group col-md-3 required"> ';
    formulario += '      <label for="ApellidoN1">Primer Apellido:</label> ';
    formulario += '      <input type="text" onkeyup="Actualizar(' + IdNN + ')" class="form-control" id="Apellido1N' + IdNN + '" value="' + Apellido1 + '" required="required" /> ';
    formulario += '   </div> ';
    formulario += '   <div class="form-group col-md-3"> ';
    formulario += '      <label for="ApellidoN2">Segundo Apellido:</label> ';
    formulario += '      <input type="text" onkeyup="Actualizar(' + IdNN + ')" class="form-control" id="Apellido2N' + IdNN + '" value="' + Apellido2 + '" /> ';
    formulario += '   </div> ';
    formulario += ' </div> ';
    formulario += ' <div class="form-group row"> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '   <label for="Sexo">Sexo:</label> ';
    formulario += '    <select class="form-control" id="SexoN' + IdNN + '" name="SexoN' + IdNN + '" onchange="Actualizar(' + IdNN + ')" required="required"> ';
    formulario += '      <option value="" disabled="" selected="">Seleccione</option> ';
    formulario += '       <option value="1">Masculino</option> ';
    formulario += '        <option value="2">Femenino</option> ';
    formulario += '    </select> ';
    formulario += '  </div> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '     <label for="FechaNacimiento">Fecha de Nacimiento:</label> ';
    formulario += '     <input type="date" onkeyup="Actualizar(' + IdNN + ')" class="form-control" value="' + FechaNacimiento + '" id="FechaNacimientoN' + IdNN + '" name="FechaNacimientoN' + IdNN + '" required="required" /> ';
    formulario += '  </div> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '      <label for="Edad">Edad:</label> ';
    formulario += '    <select class="form-control" id="EdadN' + IdNN + '" name="EdadN' + IdNN + '" onchange="Actualizar(' + IdNN + '); VerificarEdad(' + IdNN + ')" required="required"> ';
    formulario += '      <option value="" disabled="" selected="">Seleccione</option> ';
    formulario += '      <option value="0">0 años</option> ';
    formulario += '        <option value="1">1 año</option> ';
    formulario += '        <option value="2">2 años</option> ';
    formulario += '        <option value="3">3 años</option> ';
    formulario += '        <option value="4">4 años</option> ';
    formulario += '    </select> ';
    formulario += '   </div> ';
    formulario += ' </div> ';
    formulario += '</div > ';
    formulario += ' </div>' + script;

    return formulario;
}

function AgregarFormularioGestanteEdit(indice, IdGestante, Nombre1, Nombre2, Apellido1, Apellido2, Sexo, DocumentoIdentidad, FechaNacimiento) {
    var script = '<script>$("#SexoG' + IdGestante + ' option[value = ' + Sexo + ']").attr("selected", "selected");</script>';
    if (Sexo === '') script = '';
    if (FechaNacimiento === '') FechaNacimiento = '';

    var formulario = '<div class="form_gestantes" id="form_gestante' + indice + '" novalidate>';
    formulario += '<div class="container_data">';
    formulario += '<h1 class="header-form-nino">Datos de la gestante #<span id="tituloGestante' + indice + '">' + indice + '</span><button class="btn-close-form-gestante" type="button" onclick="EliminarGestante(' + IdGestante + ');">Eliminar</button></h1>';
    formulario += '<div class="form-row"> ';
    formulario += '<div class="form-group col-md-3 required">';
    formulario += '<label for="Nombre1G">Primer Nombre:</label> ';
    formulario += '<input type="text" onchange="ActualizarGestante(' + IdGestante + ')" class="form-control" id="Nombre1G' + IdGestante + '" name="Nombre1G' + IdGestante + '" value="' + Nombre1 + '" required="required" /> ';
    formulario += '</div> ';
    formulario += ' <div class="form-group col-md-3"> ';
    formulario += '   <label for="Nombre2G">Segundo Nombre:</label>';
    formulario += '    <input type="text" onchange="ActualizarGestante(' + IdGestante + ')" class="form-control" id="Nombre2G' + IdGestante + '" value="' + Nombre2 + '" /> ';
    formulario += '   </div> ';
    formulario += '    <div class="form-group col-md-3 required"> ';
    formulario += '      <label for="Apellido1G">Primer Apellido:</label> ';
    formulario += '      <input type="text" onchange="ActualizarGestante(' + IdGestante + ')" class="form-control" id="Apellido1G' + IdGestante + '" value="' + Apellido1 + '" required="required" /> ';
    formulario += '   </div> ';
    formulario += '   <div class="form-group col-md-3"> ';
    formulario += '      <label for="Apellido2G">Segundo Apellido:</label> ';
    formulario += '      <input type="text" onchange="ActualizarGestante(' + IdGestante + ')" class="form-control" id="Apellido2G' + IdGestante + '" value="' + Apellido2 + '" /> ';
    formulario += '   </div> ';
    formulario += ' </div> ';
    formulario += ' <div class="form-group row"> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '   <label for="SexoG">Sexo:</label> ';
    formulario += '    <select class="form-control" id="SexoG' + IdGestante + '" name="SexoG' + IdGestante + '" onchange="ActualizarGestante(' + IdGestante + ')" disabled="disabled" required="required"> ';
    formulario += '      <option value="" disabled="" selected="">Seleccione</option> ';
    formulario += '       <option value="1">Masculino</option> ';
    formulario += '        <option value="2" selected="selected">Femenino</option> ';
    formulario += '    </select> ';
    formulario += '  </div> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '     <label for="FechaNacimientoG">Fecha de Nacimiento:</label> ';
    formulario += '     <input type="date" onchange="ActualizarGestante(' + IdGestante + ')" class="form-control" value="' + FechaNacimiento + '" id="FechaNacimientoG' + IdGestante + '" name="FechaNacimientoG' + IdGestante + '" required="required" /> ';
    formulario += '  </div> ';
    formulario += '  <div class="form-group col-md-4 required"> ';
    formulario += '      <label for="DocumentoIdentidadG">Documento Identidad:</label> ';
    formulario += '      <input type="text" onchange="ActualizarGestante(' + IdGestante + ')" class="form-control" value="' + DocumentoIdentidad + '" id="DocumentoIdentidadG' + IdGestante + '" name="DocumentoIdentidadG' + IdGestante + '" required="required" /> ';
    formulario += '   </div> ';
    //formulario += '  <div class="form-group col-md-4 required"> ';
    //formulario += '      <label for="EdadG">Edad:</label> ';
    //formulario += '    <select class="form-control" id="EdadG' + IdGestante + '" name="EdadG' + IdGestante + '" onchange="ActualizarGestante(' + IdGestante + '); VerificarEdadGestante(' + IdGestante + ')" required="required"> ';
    //formulario += '      <option value="" disabled="" selected="">Seleccione</option> ';
    //formulario += '      <option value="0">0 años</option> ';
    //formulario += '        <option value="1">1 año</option> ';
    //formulario += '        <option value="2">2 años</option> ';
    //formulario += '        <option value="3">3 años</option> ';
    //formulario += '        <option value="4">4 años</option> ';
    //formulario += '    </select> ';
    //formulario += '   </div> ';
    formulario += ' </div> ';
    formulario += '</div > ';
    formulario += ' </div>' + script;

    return formulario;
}

function ValidarDocumentIdentidad() {
    /*var TipoDocumentoIdentidad = document.getElementById('TipoDocumentoIdentidad').value;
    var DocumentoIdentidad = document.getElementById('DocumentoIdentidad').value;

    if (TipoDocumentoIdentidad == '1' && DocumentoIdentidad.length != 11) {
        //alert("Error");
        return false;
    }*/
}

function ValidarPaso1() {
    //var TipoDocumentoIdentidad = document.getElementById("TipoDocumentoIdentidad").value;
    var DocumentoIdentidad = document.getElementById("DocumentoIdentidad").value;
    var Contrasena = document.getElementById("Contrasena").value;
    var ConfirmarContrasena = document.getElementById("ConfirmarContrasena").value;
    if (DocumentoIdentidad === "") {
        $("#msjmodal").text("Debe digitar el documento de identidad");
        $("#ModalMensaje").modal("show");
        //alert("documento identidad vacio")
        return false;
    }
    if (Contrasena === "" || ConfirmarContrasena === "") {
        //alert("confirmar contrasena")
        $("#msjmodal").text("El campo de contraseña es obligatorio");
        $("#ModalMensaje").modal("show");
        return false;
    } else if (Contrasena.length > 20 || Contrasena.length < 6) {
        //alert('contrasena mayor a 20 menor a 6')
        $("#msjmodal").text("Digite su Contraseña entre 6 y 20 caracteres");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else {
        if (Contrasena === ConfirmarContrasena) {
            //alert("contrasena confirmada")
            $("#Paso1").css("visibility", "hidden"); $("#Paso1").css("height", "0px");
            $("#msjmodal").text("Su Contraseña no coincide");
            $("#Paso2").css("visibility", "visible");
            $("#Paso2").css("height", "auto");
            $("#CantidadPasos").html("2");
            $("#Nombre1").focus();
            JsonBasico();
        } else {
            //alert('no coincie contrasena')
            $("#msjmodal").text("La contraseña no coincide");
            $("#ModalMensaje").modal("show");
            return false;
        }
    }
    topFunction();
}

function ValidarPaso2() {
    var TipoDocumentoIdentidad = document.getElementById("TipoDocumentoIdentidad").value;
    TipoCedulaOPasaporte = document.getElementById("TipoCedulaOPasaporte").value;
    CedulaOPasaporte = document.getElementById("CedulaOPasaporte").value;
    PrimerNombre = document.getElementById("Nombre1").value;
    SegundoNombre = document.getElementById("Nombre2").value;
    PrimerApellido = document.getElementById("Apellido1").value;
    SegundoApellido = document.getElementById("Apellido2").value;
    CorreoElectronico = document.getElementById("CorreoElectronico").value;
    TelefonoResidencial = document.getElementById("TelefonoResidencial").value;
    TelefonoMobil = document.getElementById("TelefonoMobil").value;
    TelefonoOtro = document.getElementById("TelefonoOtro").value;

    if (TipoCedulaOPasaporte !== null && CedulaOPasaporte === null) {
        $("#msjmodal").text("Favor de indicar la identificación");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (CedulaOPasaporte !== null && TipoCedulaOPasaporte === null) {
        $("#msjmodal").text("Favor de indicar el tipo de identificación");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (TipoCedulaOPasaporte === "1" && CedulaOPasaporte.length != 11) {
        $("#msjmodal").text("Favor de indicar una cédula válida");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (PrimerNombre === null || PrimerNombre.length === 0 || /^\s+$/.test(PrimerNombre)) {
        $("#msjmodal").text("El campo Primer Nombre está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (PrimerNombre.length > 50) {
        $("#msjmodal").text("El campo Primer Nombre es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (PrimerNombre.length < 2) {
        $("#msjmodal").text("El campo Primer Nombre es muy corto");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (SegundoNombre.length > 50) {
        $("#msjmodal").text("El campo Segundo Nombre es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (PrimerApellido === null || PrimerApellido.length === 0 || /^\s+$/.test(PrimerApellido)) {
        $("#msjmodal").text("El campo Primer Apellido está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (PrimerApellido.length > 50) {
        $("#msjmodal").text("El campo Primer Apellido es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (SegundoApellido.length > 50) {
        $("#msjmodal").text("El campo Segundo Apellido es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    //else if
    //(CorreoElectronico === null || CorreoElectronico.length === 0 || /^\s+$/.test(CorreoElectronico)) {
    //    $("#msjmodal").text("El campo Correo Electrónico está vacío o incorrecto");
    //    $("#ModalMensaje").modal("show");
    //    return false;
    //}
    else if ((CorreoElectronico !== null && CorreoElectronico.length > 0) && !(/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.([a-zA-Z]{2,4})+$/.test(CorreoElectronico))) {
        $("#msjmodal").text("El campo Correo Electronico no es válido");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (TelefonoResidencial === "" && TelefonoMobil === "" && TelefonoOtro === "") {
        $("#msjmodal").text("Debe digitar por lo menos un número de teléfono");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (isNaN(TelefonoResidencial) || isNaN(TelefonoMobil) || isNaN(TelefonoOtro)) {
        $("#msjmodal").text("El campo teléfono tiene que ser numérico");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (!(/^\d{10}$/.test(TelefonoResidencial || TelefonoMobil || TelefonoOtro))) {
        $("#msjmodal").text("El teléfono debe tener 10 caracteres");
        $("#ModalMensaje").modal("show");
        return false;
    } else {

        JsonBasico();
        $("#Paso2").css("visibility", "hidden"); $("#Paso2").css("height", "0px");
        $("#Paso3").css("visibility", "visible");
        $("#Paso3").css("height", "auto");
        $("#CantidadPasos").html("3");
    }

    topFunction();
}

function ValidarPaso3() {
    Sector = document.getElementById("Sector").value;
    Calle = document.getElementById("Calle").value;
    Barrio = document.getElementById("Barrio").value;
    NumeroVivienda = document.getElementById("NumeroVivienda").value
    Manzana = document.getElementById("Manzana").value;
    Edificio = document.getElementById("Edificio").value;
    Apartamento = document.getElementById("Apartamento").value;
    EntreLaCalle = document.getElementById("EntreLaCalle").value;
    YLaCalle = document.getElementById("YLaCalle").value;

    if (Sector === null || Sector.length === 0 || /^\s+$/.test(Sector)) {
        $("#msjmodal").text("El campo Sector está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Sector.length > 50) {
        $("#msjmodal").text("El campo Sector es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Barrio.length > 50) {
        $("#msjmodal").text("El campo Barrio es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Calle === null || Calle.length === 0 || /^\s+$/.test(Calle)) {
        $("#msjmodal").text("El campo Dirección está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Calle.length > 50) {
        $("#msjmodal").text("El campo Dirección es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (NumeroVivienda === null || NumeroVivienda.length === 0 || /^\s+$/.test(NumeroVivienda)) {
        $("#msjmodal").text("El campo Número Casa está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (NumeroVivienda.length > 50) {
        $("#msjmodal").text("El campo Número Casa es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Manzana.length > 50) {
        $("#msjmodal").text("El campo Manzana es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Edificio.length > 50) {
        $("#msjmodal").text("El campo Edificio es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Apartamento.length > 50) {
        $("#msjmodal").text("El campo Apartamento es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (EntreLaCalle.length > 50) {
        $("#msjmodal").text("El campo Entre la Calle es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (YLaCalle.length > 50) {
        $("#msjmodal").text("El campo Y la Calle es muy largo");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else {
        JsonBasico();
        $("#Paso3").css("visibility", "hidden"); $("#Paso3").css("height", "0px");
        $("#Paso4").css("visibility", "visible");
        $("#Paso4").css("height", "auto");
        $("#CantidadPasos").html("4");
        $("#CantidadNinos").focus();
        topFunction();
    }
}

function topFunction() {
    //alert('top function')
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
}

function Ocultar() {
    $("#Paso1").css("visibility", "hidden"); $("#Paso1").css("height", "0px");
    $("#Paso3").css("visibility", "visible"); $("#Paso3").css("height", "auto");
}

function ValidarPaso4() {
    var total_ninos = Number($("#CantidadNinos").val());
    var total_gestantes = Number($("#CantidadGestantes").val());
    var contenido_modal = "";

    if (total_ninos > 0 || total_gestantes > 0) {
        for (i = 0; i <= total_ninos; i++) {
            if ($("#Nombre1N" + i).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            } else if ($("#Apellido1N" + i).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            } else if ($("#SexoN" + i).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            } else if ($("#FechaNacimientoN" + i).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            } else if ($("#Edad1N" + i).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            }
        }

        for (var o = 0; o < total_gestantes; o++) {
            if ($("#Nombre1G" + o).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            } else if ($("#Apellido1G" + o).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            } else if ($("#SexoG" + o).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            } else if ($("#FechaNacimientoG" + o).val() == "") {
                contenido_modal = "Debe llenar todos los campos obligatorios";
                $("#msjmodal").text(contenido_modal);
                $("#ModalMensaje").modal("show");
                $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                $("#CantidadPasos").html("4");
                ///return false;
            } else if ($("#DocumentoIdentidadG" + o).val() != "" && $("#DocumentoIdentidadG" + o).val() != undefined) {
                if ($("#DocumentoIdentidadG" + o).val().length != 11) {
                    contenido_modal = "Debe llenar todos los campos obligatorios";
                    $("#msjmodal").text(contenido_modal);
                    $("#ModalMensaje").modal("show");
                    $("#Paso4").css("visibility", "visible"); $("#Paso4").css("height", "auto");
                    $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
                    $("#CantidadPasos").html("4");
                }
            }
        }

        if (contenido_modal.length <= 0) {
            GenerarJson();
            $("#Paso4").css("visibility", "hidden"); $("#Paso4").css("height", "0px");
            $("#Paso5").css("visibility", "visible"); $("#Paso5").css("height", "auto");
            $("#CantidadPasos").html("5");
        }
    }

    topFunction();
}
function Paso1() {
    var Documento = $("#TipoDocumentoIdentidad").val();
    switch (Documento) {
        case "2":
            document.getElementById("lblCorreoElectronico").innerHTML = "Correo Electrónico (Opcional):";
            break;
    }
    ValidarPaso1();
}
function Paso2() {
    ValidarPaso2();
}
function Paso3() {
    ValidarPaso3();
}
function Paso4() {
    ValidarPaso4();
}
function VolverPaso1() {
    $("#Paso2").css("visibility", "hidden"); $("#Paso2").css("height", "0px");
    $("#Paso1").css("visibility", "visible");
    $("#Paso1").css("height", "auto");
    $("#CantidadPasos").html("1");
    topFunction();
}
function VolverPaso2() {
    $("#Paso3").css("visibility", "hidden"); $("#Paso3").css("height", "0px");
    $("#Paso2").css("visibility", "visible");
    $("#Paso2").css("height", "auto");
    $("#CantidadPasos").html("2");
    topFunction();
}
function VolverPaso3() {
    $("#Paso4").css("visibility", "hidden"); $("#Paso4").css("height", "0px");
    $("#Paso3").css("visibility", "visible");
    $("#Paso3").css("height", "auto");
    $("#CantidadPasos").html("3");
    topFunction();
}
function VolverPaso4() {
    $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
    $("#Paso4").css("visibility", "visible");
    $("#Paso4").css("height", "auto");
    $("#CantidadPasos").html("4");
    topFunction();
}

function btnAgregarCantidadNNs_ChangeCantidadNN() {
    var formulario;
    var cantidadNNs = Number(document.getElementById("CantidadNinos").value);

    $("#contenedor_form_ninos").html('');

    for (i = 1; i <= cantidadNNs; i++) {
        CrearRegistros(i);
        formulario = AgregarFormularioNinos(i);
        $("#contenedor_form_ninos").append(formulario);
        $("#btnAgregarNino").show();
    }

    $("#CantidadNinos").attr('readOnly', true);
    $("#divCantidadNNs").hide();
    $("#lblCantidadNNs").attr("visible", true);

    if (cantidadNNs > 0) {
        $("#divAlertCantidadNNs").hide();
        $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niños/as.");
        $("#btnPaso4").attr("disabled", false);
    } else {
        //$("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niño/a.");
        $("#divAlertCantidadNNs").show();
        $("#divCantidadNNs").show();
        $("#CantidadNinos").attr('readOnly', false);
    }

    //if ($("#CantidadNinos").val() > 1) {
    //    $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niños/as.");
    //} else {
    //    $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niño/a.");
    //}
}

//alert('dddd');
$(document).ready(function () {
    //alert('dddddd');
    $("#btnContinuarContrasena").click(function () {
        ValidarDocumentIdentidad()
    });

    //$("#btnPaso1").click(function () {
    //    //alert('validando paso 1');
    //    var Documento = $("#TipoDocumentoIdentidad").val();
    //    switch (Documento) {
    //        case "2":
    //            document.getElementById("lblCorreoElectronico").innerHTML = "Correo Electrónico (Opcional):";
    //            break;
    //    }
    //    ValidarPaso1();
    //});

    $("#btnPaso2").click(function () {
        ValidarPaso2();
    });

    $("#btnPaso3").click(function () {
        ValidarPaso3();
    });

    $("#btnPaso4").click(function () {
        ValidarPaso4();
    });

    $("#btnVolverPaso4").click(function () {
        $("#Paso5").css("visibility", "hidden"); $("#Paso5").css("height", "0px");
        $("#Paso4").css("visibility", "visible");
        $("#Paso4").css("height", "auto");
        $("#CantidadPasos").html("4");
        topFunction();
    });

    $("#btnVolverPaso3").click(function () {
        $("#Paso4").css("visibility", "hidden"); $("#Paso4").css("height", "0px");
        $("#Paso3").css("visibility", "visible");
        $("#Paso3").css("height", "auto");
        $("#CantidadPasos").html("3");
        topFunction();
    });

    $("#btnVolverPaso2").click(function () {
        $("#Paso3").css("visibility", "hidden"); $("#Paso3").css("height", "0px");
        $("#Paso2").css("visibility", "visible");
        $("#Paso2").css("height", "auto");
        $("#CantidadPasos").html("2");
        topFunction();
    });

    $("#btnVolverPaso1").click(function () {
        $("#Paso2").css("visibility", "hidden"); $("#Paso2").css("height", "0px");
        $("#Paso1").css("visibility", "visible");
        $("#Paso1").css("height", "auto");
        $("#CantidadPasos").html("1");
        topFunction();
    });

    var fecha = new Date();
    $("#FechaRegistro").val(fecha.getDate() + "/" + (fecha.getMonth() + 1) + "/" + fecha.getFullYear());

    $("#CantidadNinos").keydown(function (e) {
        var formulario;

        if (e.which === 13) {
            Registros();

            $("#contenedor_form_ninos").html('');
            for (i = 1; i <= $(this).val(); i++) {
                CrearRegistros(i);
                formulario = AgregarFormularioNinos(i);
                $("#contenedor_form_ninos").append(formulario);
            }

            $("#CantidadNinos").attr('readOnly', true);
            $("#divCantidadNNs").hide();
            $("#lblCantidadNNs").attr("visible", true);

            if ($("#CantidadNinos").val() > 1) {
                $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niños/as.");
            } else {
                $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niño/a.");
            }

            return false;
        }
    });

    $("#CantidadGestantes").keydown(function (e) {
        var formulario;

        if (e.which === 13) {
            RegistrosGestantes();

            $("#contenedor_form_info_gestante").html('');
            for (i = 1; i <= $(this).val(); i++) {
                CrearRegistrosGestantes(i);
                formulario = AgregarFormularioGestantes(i);
                $("#contenedor_form_info_gestante").append(formulario);
            }

            $("#CantidadGestantes").attr('readOnly', true);
            $("#divCantidadGestantes").hide();
            $("#lblCantidadGestantes").attr("visible", true);

            if ($("#CantidadGestantes").val() > 1) {
                $("#lblCantidadGestantes").html("Está solicitando servicio para <b><u>" + $("#CantidadGestantes").val() + "</u></b>&nbsp;gestantes.");
            } else {
                $("#lblCantidadGestantes").html("Está solicitando servicio para <b><u>" + $("#CantidadGestantes").val() + "</u></b>&nbsp;gestante.");
            }

            return false;
        }
    });



    $("#CantidadNinos").change(function (e) {
        var formulario;
        $("#contenedor_form_ninos").html('');
        for (i = 1; i <= $(this).val(); i++) {
            CrearRegistros(i);
            formulario = AgregarFormularioNinos(i);
            $("#contenedor_form_ninos").append(formulario);
            $("#btnAgregarNino").show();
        }

        $("#CantidadNinos").attr('readOnly', true);
        $("#divCantidadNNs").hide();
        $("#lblCantidadNNs").attr("visible", true);

        if ($("#CantidadNinos").val() > 1) {
            $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niños/as.");
        } else {
            $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niño/a.");
        }
    });

    $("#CantidadGestantes").change(function (e) {
        var formulario;
        $("#contenedor_form_info_gestante").html('');
        for (i = 1; i <= $(this).val(); i++) {
            CrearRegistrosGestantes(i);
            formulario = AgregarFormularioGestantes(i);
            $("#contenedor_form_info_gestante").append(formulario);
            $("#btnAgregarGestante").show();
        }

        $("#CantidadGestantes").attr('readOnly', true);
        $("#divCantidadGestantes").hide();
        $("#lblCantidadGestantes").attr("visible", true);

        if ($("#CantidadGestantes").val() > 1) {
            $("#lblCantidadGestantes").html("Está solicitando servicio para <b><u>" + $("#CantidadGestantes").val() + "</u></b>&nbsp;gestantes.");
        } else {
            $("#lblCantidadGestantes").html("Está solicitando servicio para <b><u>" + $("#CantidadGestantes").val() + "</u></b>&nbsp;gestante.");
        }
    });

    $("#btnAgregarNino").click(function () {
        $("#CantidadNinos").val(Number($("#CantidadNinos").val()) + 1);
        var registros = Number($("#CantidadNinos").val());
        formulario = AgregarFormularioNinos(registros);
        $("#contenedor_form_ninos").append(formulario);

        $("#divCantidadNNs").hide();
        $("#lblCantidadNNs").attr("visible", true);

        if ($("#CantidadNinos").val() > 1) {
            $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niños/as.");
        } else {
            $("#lblCantidadNNs").html("Está solicitando servicio para <b><u>" + $("#CantidadNinos").val() + "</u></b>&nbsp;niño/a.");
        }
    });

    $("#btnAgregarGestante").click(function () {
        $("#CantidadGestantes").val(Number($("#CantidadGestantes").val()) + 1);
        var registros = Number($("#CantidadGestantes").val());
        formulario = AgregarFormularioGestantes(registros);
        $("#contenedor_form_info_gestante").append(formulario);

        $("#divCantidadGestantes").hide();
        $("#lblCantidadGestantes").attr("visible", true);

        if ($("#CantidadGestantes").val() > 1) {
            $("#lblCantidadGestantes").html("Está solicitando servicio para <b><u>" + $("#CantidadGestantes").val() + "</u></b>&nbsp;gestantes.");
        } else {
            $("#lblCantidadGestantes").html("Está solicitando servicio para <b><u>" + $("#CantidadGestantes").val() + "</u></b>&nbsp;gestante.");
        }
    });

    //Ir arriba
    $('.ir-arriba').click(function () {
        $('body, html').animate({
            scrollTop: '0px'
        }, 300);
    });

    $(window).scroll(function () {
        if ($(this).scrollTop() > 0) {
            $('.ir-arriba').slideDown(300);
        } else {
            $('.ir-arriba').slideUp(300);
        }
    });
});

//Modal 
function ModalVerificarCedula() {
    // Show the Modal on load
    $("#ModalMensaje").modal("show");
    // Hide the Modal
    $("#ModalMensaje").modal("hide");
};

// Validar formulario solicitudes
function validacion() {

    PrimerNombre = document.getElementById("Nombre1").value;
    PrimerApellido = document.getElementById("Apellido1").value;
    CorreoElectronico = document.getElementById("CorreoElectronico").value;
    TelefonoResidencial = document.getElementById("TelefonoResidencial").value;
    TelefonoMobil = document.getElementById("TelefonoMobil").value;
    TelefonoOtro = document.getElementById("TelefonoOtro").value;
    Calle = document.getElementById("Calle").value;
    Sector = document.getElementById("Sector").value;
    NumeroVivienda = document.getElementById("NumeroVivienda").value;

    if (PrimerNombre === null || PrimerNombre.length === 0 || /^\s+$/.test(PrimerNombre)) {
        $("#msjmodal").text("El campo Primer Nombre está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (PrimerApellido === null || PrimerApellido.length === 0 || /^\s+$/.test(PrimerApellido)) {
        $("#msjmodal").text("El campo Primer Apellido está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (CorreoElectronico === null || CorreoElectronico.length === 0 || /^\s+$/.test(CorreoElectronico)) {
        $("#msjmodal").text("El campo Correo Electrónico está vacío o incorrecto");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (!(/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.([a-zA-Z]{2,4})+$/.test(CorreoElectronico))) {
        $("#msjmodal").text("El campo Correo Electronico no es válido");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (TelefonoResidencial === "" && TelefonoMobil === "" && TelefonoOtro === "") {
        $("#msjmodal").text("Debe digitar por lo menos un número de teléfono");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (isNaN(TelefonoResidencial) || isNaN(TelefonoMobil) || isNaN(TelefonoOtro)) {
        $("#msjmodal").text("El campo teléfono tiene que ser numérico");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (!(/^\d{10}$/.test(TelefonoResidencial || TelefonoMobil || TelefonoOtro))) {
        $("#msjmodal").text("El teléfono debe tener 10 caracteres");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Sector === null || Sector.length === 0 || /^\s+$/.test(Sector)) {
        $("#msjmodal").text("El campo Sector está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (Calle === null || Calle.length === 0 || /^\s+$/.test(Calle)) {
        $("#msjmodal").text("El campo Dirección está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (NumeroVivienda === null || NumeroVivienda.length === 0 || /^\s+$/.test(NumeroVivienda)) {
        $("#msjmodal").text("El campo Número Casa está vacío");
        $("#ModalMensaje").modal("show");
        return false;
    }
    else if (isNaN(NumeroVivienda)) {
        $("#msjmodal").text("El campo teléfono tiene que ser numérico");
        $("#ModalMensaje").modal("show");
        return false;
    }
    //final del la validacion del formulario   
}


//Función que valida los caracteres permitidos
function permite(onkeypress, permitidos) {
    // Variables que definen los caracteres permitidos
    var numeros = "0123456789";
    var caracteres = " abcdefghijklmnñopqrstuvwxyzABCDEFGHIJKLMNÑOPQRSTUVWXYZ";
    var numeros_caracteres = numeros + caracteres;
    var teclas_especiales = [8, 37, 39, 46];

    // Seleccionar los caracteres a partir del parámetro de la función
    switch (permitidos) {
        case 'num':
            permitidos = numeros;
            break;
        case 'car':
            permitidos = caracteres;
            break;
        case 'num_car':
            permitidos = numeros_caracteres;
            break;
    }

    // Obtener la tecla pulsada 
    var evento = onkeypress || window.event;
    var codigoCaracter = evento.charCode || evento.keyCode;
    var caracter = String.fromCharCode(codigoCaracter);

    // Comprobar si la tecla pulsada es alguna de las teclas especiales
    // (teclas de borrado y flechas horizontales)
    var tecla_especial = false;
    for (var i in teclas_especiales) {
        if (codigoCaracter === teclas_especiales[i]) {
            tecla_especial = true;
            break;
        }
    }
    // Comprobar si la tecla pulsada se encuentra en los caracteres permitidos
    // o si es una tecla especial
    return permitidos.indexOf(caracter) !== -1 || tecla_especial;
}

//Actualizar la página, si continúa, cerrará la sesión y perderá los datos
document.onkeydown = fkey;
document.onkeypress = fkey
document.onkeyup = fkey;

var wasPressed = false;

function fkey(e) {
    e = e || window.event;
    if (wasPressed) return;

    if (e.keyCode === 116) {
        var txt;
        //var r = confirm("Seguro que desea actualizar la página?, si continúa, cerrará la sesión y perderá los datos");
        //if (r === true) {
        //    return true;
        //} else {
        //    return false;
        //}
    }
}

function HabilitarPanelGestante() {
    var valorCbo = document.getElementById("cboRespuestaGestante").value;
    if (valorCbo != null) {
        switch (valorCbo) {
            case "0":
                document.getElementById("contenedor_form_gestante").style.display = "none";
                //document.getElementById("contenedor_form_gestante").innerHTML = "";
                document.getElementById("CantidadGestantes").value = undefined;
                RegistrosGestantes();

                $("#CantidadGestantes").attr('readOnly', false);
                $("#divCantidadGestantes").show();
                $("#lblCantidadGestantes").attr("visible", false);
                $("#lblCantidadGestantes").html("");
                break;
            case "1":
                document.getElementById("contenedor_form_gestante").style.display = "inline";
                break;
        }
    }
}

function VolverAtras() {
    window.location.href = '/Views/SolicitudesServicios/solicitudes_login.aspx';
}


window.onload = function () {
    //Crear base de datos
    crearTablas();
    Registros();
    RegistrosGestantes();

}
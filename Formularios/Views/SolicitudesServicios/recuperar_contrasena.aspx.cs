﻿using INAIPI.Core;
using SI_INAIPI.Controls.INS;
using SI_INAIPI.Models;
using SI_INAIPI.Models.INS;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IdentityModel.Tokens;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Security.Cryptography;
using System.Text;

namespace Formularios.Views.SolicitudesServicios
{
    public partial class recuperar_contrasena : System.Web.UI.Page
    {
        INS_SolicitudesServiciosCtrl ctrl = new INS_SolicitudesServiciosCtrl();

        private INS_SolicitudesServicios Solicitud
        {
            get { return (INS_SolicitudesServicios)Session["solicitud_Solicitud"]; }
            set { Session["solicitud_Solicitud"] = value; }
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }
        protected string GenerarToken()
        {
            string token = Convert.ToBase64String(Guid.NewGuid().ToByteArray());
            token = token.Replace("=", "").Replace("+", "").Replace("/", "");
            return token;
        }

        String getDominio()
        {
            string cadena = HttpContext.Current.Request.Url.AbsoluteUri;
            string[] Separado = cadena.Split('V');
            string Final = Separado[0];

            return Final;
        }

        protected void btnResetearContrasena_Click(object sender, EventArgs e)
        {
            if (OlvidoDocumentoIdentidad.Value == string.Empty)
            {
                Modal.ShowOnPageLoad = true;
            }
            else
            {
                if (OlvidoDocumentoIdentidad.Value == Solicitud.DocumentoIdentidad)
                {
                    string token = GenerarToken();
                    //char.TryParse((token), out char ctoken);
                    Solicitud.token = token;
                    String server = getDominio();

                    ctrl.RegistrarToken(Solicitud);
                    //TODO: LA URL EN EL CORREO ESTÁ LOCALHOST, ES NECESARIO PARAMETRIZARLA
                    string Cuerpo = "<!DOCTYPE html PUBLIC '-//W3C//DTD XHTML 1.0 Transitional //EN' 'http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd'><html xmlns='http://www.w3.org/1999/xhtml' xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office'><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8' /> <style type='text/css'>  body, .mainTable { height:100% !important; width:100% !important; margin:0; padding:0; }  img, a img { border:0; outline:none; text-decoration:none; }  .imageFix { display:block; }  table, td { border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;}  p {margin-top:0; margin-right:0; margin-left:0; padding:0;}  .ReadMsgBody{width:100%;} .ExternalClass{width:100%;}.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, 	.ExternalClass td, 	.ExternalClass div { line-height: 100% ; } 	img { -ms-interpolation-mode: bicubic; 	} body, table, td, 	p, 	a, li, 	blockquote { -ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; } </style><style>table{ border-collapse: collapse; }@media only screen and (max-width: 600px) {    body[yahoo] .rimg {   max-width: 100%; height: auto; } body[yahoo].rtable { 	width: 100%!important;table-layout: fixed; } body[yahoo].rtable tr { height: auto!important; } } </style><!--[if gte mso 9]><xml>  <o:OfficeDocumentSettings> <o: AllowPNG / ><o: PixelsPerInch > 96</o:PixelsPerInch>  </o: OfficeDocumentSettings ></xml><![endif]--></head ><body yahoo = fix scroll = 'auto' style='padding:0; margin:0; FONT-SIZE: 12px; FONT-FAMILY: Arial, Helvetica, sans-serif; cursor:auto; background:#F3F3F3'><TABLE class='rtable mainTable' cellSpacing=0 cellPadding=0 width='100%' bgColor=#f3f3f3> <TR ><TD style = 'FONT-SIZE: 0px; HEIGHT: 20px; LINE-HEIGHT: 0'>&#160;</TD></TR><TR><TD vAlign = top ><TABLE class = rtable style = 'WIDTH: 600px; MARGIN: 0px auto' cellSpacing=0 cellPadding=0 width=600 align=center border=0> <TR ><TD style = 'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: medium none; PADDING-RIGHT: 0px; BACKGROUND-COLOR: transparent'> <TABLE class = rtable style = 'WIDTH: 100%' cellSpacing=0 cellPadding=0 align=left> <TR style = 'HEIGHT: 10px'><TD style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 35px; TEXT-ALIGN: center; PADDING-TOP: 35px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent'><TABLE cellSpacing=0 cellPadding=0 align=center border=0><TR> <TD style = 'PADDING-BOTTOM: 2px; PADDING-TOP: 2px; PADDING-LEFT: 2px; PADDING-RIGHT: 2px' align=center><TABLE cellSpacing=0 cellPadding=0 border=0><TR><TD style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; BACKGROUND-COLOR: transparent'><IMG class=rimg style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; DISPLAY: block; BACKGROUND-COLOR: transparent' border=0 src='http://app.inaipi.gob.do:97/solicitudes/images/Image_1.png' width=263 height=64 hspace='0' vspace='0'></TD></TR></TABLE></TD></TR></TABLE></TD></TR></TABLE></TD></TR><TR> <TD style = 'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; PADDING-TOP: 1px; PADDING-LEFT: 0px; BORDER-LEFT: medium none; PADDING-RIGHT: 0px; BACKGROUND-COLOR: transparent'><TABLE class=rtable style='WIDTH: 100%' cellSpacing=0 cellPadding=0 align=left><TR style='HEIGHT: 10px'><TD style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; TEXT-ALIGN: center; PADDING-TOP: 1px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent'><P style='FONT-SIZE: 18px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #0070cd; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=center><STRONG>Instituto Nacional de Atenci&#243;n Integral a la Primera Infancia</STRONG><BR><FONT style='FONT-SIZE: 14px; COLOR: #ffa300'><STRONG>&#161;Ser ni&#241;o y&#160;ni&#241;a nunca fue mejor&#160;!</STRONG></FONT><BR></P></TD></TR></TABLE></TD></TR> <TR ><TD style = 'BORDER-TOP: #dbdbdb 1px solid; BORDER-RIGHT: #dbdbdb 1px solid; BORDER-BOTTOM: #dbdbdb 1px solid; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: #dbdbdb 1px solid; PADDING-RIGHT: 0px; BACKGROUND-COLOR: #feffff'><TABLE class=rtable style='WIDTH: 100%' cellSpacing=0 cellPadding=0 align=left><TR style='HEIGHT: 10px'><TD style='BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 35px; TEXT-ALIGN: center; PADDING-TOP: 35px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: #feffff'><P style='FONT-SIZE: 18px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #a8a7a7; LINE-HEIGHT: 155%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=center><STRONG><h1>Hola, " + Solicitud.Nombre1 + " " + Solicitud.Apellido1 + "</h1></STRONG></P> <P style = 'FONT-SIZE: 12px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #a7a7a7; LINE-HEIGHT: 155%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=center></h1> <p > Usted ha solicitado cambiar su contraseña. Puede hacerlo a través del siguiente enlace.</p> <p ><DIV style = 'TEXT-ALIGN: center; MARGIN: 20px 0px 0px'><a href='"+server+"Views/SolicitudesServicios/actualizar_contrasena.aspx?token=" + token + "&cedula=" + Solicitud.DocumentoIdentidad + "'>Cambiar contraseña</A></DIV></p><p > En caso de no acceder su contraseña no registrará cambios.</p> <p> Por favor no responder a este correo, esta cuenta no está siendo monitoreada.</p></TD></TR></TABLE></TD></TR><TR> <TD style = 'BORDER-TOP: #dbdbdb 1px solid; BORDER-RIGHT: #dbdbdb 1px solid; BORDER-BOTTOM: #dbdbdb 1px solid; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: #dbdbdb 1px solid; PADDING-RIGHT: 0px; BACKGROUND-COLOR: #feffff'><TABLE class = rtable style = 'WIDTH: 100%' cellSpacing=0 cellPadding=0 align=left><TR style='HEIGHT: 10px'> <TD style = 'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; TEXT-ALIGN: center; PADDING-TOP: 1px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent'> <P style = 'FONT-SIZE: 10px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=left>&#160;</P><P style='FONT-SIZE: 10px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=left>&#160;</P><P style='FONT-SIZE: 16px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly' align=right>Sistema de Informaci&#243;n &#169;2018 <BR>Departamento de Desarrollo e Implementaci&#243;n de Sistemas, TIC </P></TD></TR></TABLE></TD></TR></TABLE></TD></TR><TR> <TD style = 'FONT-SIZE: 0px; HEIGHT: 8px; LINE-HEIGHT: 0'>&#160;</TD></TR></TABLE></body></html></TD></TR><TR> <TD style = 'FONT-SIZE: 0px; HEIGHT: 8px; LINE-HEIGHT: 0'>&#160;</TD></TR></TABLE></body></html>";
                    ctrl.EnviarCorreoASolicitante(Solicitud, "RECUPERAR CONTRASEÑA | SOLICITUD DE SERVICIOS INAIPI", Cuerpo);
                    Response.Redirect("~/Views/SolicitudesServicios/Mensaje.aspx");
                }
                else
                {
                    Modal.ShowOnPageLoad = true;
                    modalContent.InnerText = "Su número de identidad está incorrecto";
                }

            }
        }
    }
}
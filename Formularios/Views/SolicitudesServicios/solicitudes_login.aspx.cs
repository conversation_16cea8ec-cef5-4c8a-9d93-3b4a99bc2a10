﻿using INAIPI.Core;
using SI_INAIPI.Controls.INS;
using SI_INAIPI.Models.INS;
using SI_INAIPI.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Formularios.Views.SolicitudesServicios
{
    public partial class solicitudes_login : System.Web.UI.Page
    {
        INS_SolicitudesServiciosCtrl ctrl = new INS_SolicitudesServiciosCtrl();

        #region PROPIEDADES
        private INS_SolicitudesServicios Solicitud
        {
            get { return (INS_SolicitudesServicios)Session["solicitud_Solicitud"]; }
            set { Session["solicitud_Solicitud"] = value; }
        }
        private string IdDocumentoIdentidad
        {
            get { return (string)(Session["solicitud_IdDocumentoIdentidad"] ?? ""); }
            set { Session["solicitud_IdDocumentoIdentidad"] = value; }
        }
        private byte IdTipoDocumentoIdentidad
        {
            get { return (byte)(Session["solicitud_IdTipoDocumentoIdentidad"] ?? "0"); }
            set { Session["solicitud_IdTipoDocumentoIdentidad"] = value; }
        }
        private bool EntradaPermitida
        {
            get { return (bool)(Session["solicitud_EntradaPermitida"] ?? false); }
            set { Session["solicitud_EntradaPermitida"] = value; }
        }
        private string AceptarAvertencia
        {
            get { return (string)Session["AceptarCondicion"]; }
            set { Session["AceptarCondicion"] = value; }
        }
        #endregion PROPIEDADES

        protected void Page_Load(object sender, EventArgs e)
        {
            if (AceptarAvertencia == null)
            {
                Response.Redirect("~/Views/SolicitudesServicios/Condiciones.aspx");
            }
        }

        protected void btnContinuarContrasena_Click(object sender, EventArgs e)
        {
            byte.TryParse(TipoDocumentoIdentidad.Value, out byte IdTipoDocumentoIdentidad);
            string IdDocumentoIdentidad = DocumentoIdentidad.Value;

            if (Validar())
            {
                this.IdTipoDocumentoIdentidad = IdTipoDocumentoIdentidad;
                this.IdDocumentoIdentidad = IdDocumentoIdentidad;

                Resultado<INS_SolicitudesServicios> Consulta = ctrl.ConsultarSolicitadaPorDocumentoIdentidad(IdDocumentoIdentidad, true);

                Solicitud = Consulta.Objeto;

                if (Solicitud != null)
                {
                    Response.Redirect("~/Views/SolicitudesServicios/solicitudes_login_contrasena.aspx");
                }
                else
                {
                    Solicitud = null;
                    EntradaPermitida = true;
                    Response.Redirect("~/Views/SolicitudesServicios/Solicitudes.aspx");
                }
            }

            bool Validar()
            {
                bool bien = true;

                if (string.IsNullOrEmpty(TipoDocumentoIdentidad.Value) || string.IsNullOrEmpty(DocumentoIdentidad.Value))
                {
                    Modal.ShowOnPageLoad = true;
                    bien = false;
                }

                if (IdTipoDocumentoIdentidad == 1 && IdDocumentoIdentidad.Length != 11)
                {
                    Modal.ShowOnPageLoad = true;
                    modalContent.InnerText = "La cédula no es correcta";
                    bien = false;
                }
                else if(IdTipoDocumentoIdentidad == 1 && !IdDocumentoIdentidad.All(char.IsDigit))
                {
                    Modal.ShowOnPageLoad = true;
                    modalContent.InnerText = "La cédula no es correcta";
                    bien = false;
                }
                if (IdTipoDocumentoIdentidad == 0 && IdDocumentoIdentidad.Length > 20)
                {
                    Modal.ShowOnPageLoad = true;
                    modalContent.InnerText = "Pasaporte No Válido";
                    bien = false;
                }

                if (IdTipoDocumentoIdentidad == 2 && IdDocumentoIdentidad.Length != 10)
                {
                    Modal.ShowOnPageLoad = true;
                    modalContent.InnerText = "Coloque un número de telefono válido";
                    bien = false;
                }

                return bien;
            }
        }
    }
}
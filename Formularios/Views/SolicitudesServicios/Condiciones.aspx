﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Condiciones.aspx.cs" Inherits="Formularios.Views.SolicitudesServicios.Condiciones" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Condiciones</title>
    <link rel="icon" href="../../images/favicon.ico" />
    <meta charset="UTF-8" />
    <meta name="description" content="Solicitudes de Servicios" />
    <meta name="keywords" content="Solicitudes,Servicios,niños,niñas" />
    <%--<meta name="viewport" content="width=device-width, initial-scale=1.0" />--%>
    <link rel="stylesheet" href="/Content/bootstrap.min.css" />
    <%--<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />--%>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="/css/solicitudesservicios.css" />
    <!--Insertando el Jquery-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script src="/Scripts/bootstrap.min.js"></script>
 <%--   <script src="/js/solicitudesservicios.js"></script>--%>

    <script type="text/javascript">
    var _userway_config = {
    /* uncomment the following line to override default position*/
    /* position: '6', */
    /* uncomment the following line to override default size (values: small, large)*/
    /* size: 'small', */
    /* uncomment the following line to override default language (e.g., fr, de, es, he, nl, etc.)*/
    language: 'es', 
    /* uncomment the following line to override color set via widget (e.g., #053f67)*/
    /* color: '#053f67', */
    /* uncomment the following line to override type set via widget(1=person, 2=chair, 3=eye)*/
    /* type: '1', */
    /* uncomment the following line to override support on mobile devices*/
    /* mobile: true, */
    account: 'QNhyx3cvTc'
    };
    </script>
    <script type="text/javascript" src="https://cdn.userway.org/widget.js"></script>
</head>
<body class="bg-body">
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../images/logo.png" alt="INAIPI" />
            </div>
        </div>
    </header>
    <span class="ir-arriba icon-keyboard_arrow_up"></span>
    <section class="container">
        <div class="main" style="position: relative">
            <h1>Solicitud de Servicios</h1>
            <form action="Condiciones.aspx" method="post" id="Condiciones" name="Condiciones" runat="server">
                <h3>¡Aviso Importante!</h3>
                <p>Su solicitud se someterá a un proceso de diagnóstico y validación y se le notificará por los medios de contacto que usted nos suministre el estatus de la misma.</p>
                <p><b>¡OJO!</b> Esta solicitud será tomada en cuenta para servicios CAIPI o CAFI según aplique.</p>
                <p>El proceso de selección está basado en diversos criterios, prestando especial atención a: </p>
                <ul>
                    <li>Índice de Vulnerabilidad</li>
                    <li>Niños y Niñas con Discapacidad</li>
                    <li>Ubicación Geográfica</li>
                    <li>Modalidad de Servicio</li>
                    <li>Entre Otros</li>
                </ul>
                <br />
                <div class="row my-2">
                    <div class="col-sm-12">
                        <a target="_blank" href="https://inaipi.gob.do/media/attachments/solicitud%20de%20servicios%20en%20linea.pdf" class="btn btn-info  btn-block" runat="server"><i class="fa fa-download"></i>&nbsp;Descargar instructivo de uso</a>
                    </div>
                </div>
                <div class="row my-2">
                    <div class="col-sm-6">
                        <a href="https://www.inaipi.gob.do" class="btn btn-secondary  btn-block" runat="server"><i class="fa fa-home"></i>&nbsp;Volver al Portal</a>
                    </div>
                    <div class="col-sm-6">
                        <a id="btnAceptar" href="#" class="btn btn-primary  btn-block" onserverclick="btnAceptar_Click" runat="server">Entendido</a>
                        <%--<asp:Button ID="btnAceptar" CssClass="btn btn-primary  btn-block" Text="Entendido" OnClick="btnAceptar_Click" runat="server" />--%>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <script type="text/javascript" src="../../js/helpers.js"></script>
    <script>
        eliminarBaseDeDatos("SolicitudesServicios-1")
            .then(mensaje => console.log(mensaje))
            .catch(error => console.error(error));
    </script>
</body>
</html>

﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="recuperar_contrasena.aspx.cs" Inherits="Formularios.Views.SolicitudesServicios.recuperar_contrasena" %>

<%@ Register Assembly="DevExpress.Web.Bootstrap.v20.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.v20.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web" TagPrefix="dx" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <link rel="icon" href="../../images/favicon.ico" />
    <meta charset="UTF-8" />
    <meta name="description" content="Solicitudes de Servicios" />
    <meta name="keywords" content="Solicitudes,Servicios,niños,niñas" />
    <%--<meta name="viewport" content="width=device-width, initial-scale=1.0" />--%>
    <link rel="stylesheet" href="/Content/bootstrap.min.css" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="/css/solicitudesservicios.css" />
    <!--Insertando el Jquery-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script src="/Scripts/bootstrap.min.js"></script>
    <script src="/js/solicitudesservicios.js"></script>

    <script type="text/javascript">
        var _userway_config = {
            /* uncomment the following line to override default position*/
            /* position: '6', */
            /* uncomment the following line to override default size (values: small, large)*/
            /* size: 'small', */
            /* uncomment the following line to override default language (e.g., fr, de, es, he, nl, etc.)*/
            language: 'es',
            /* uncomment the following line to override color set via widget (e.g., #053f67)*/
            /* color: '#053f67', */
            /* uncomment the following line to override type set via widget(1=person, 2=chair, 3=eye)*/
            /* type: '1', */
            /* uncomment the following line to override support on mobile devices*/
            /* mobile: true, */
            account: 'QNhyx3cvTc'
        };
    </script>
    <script type="text/javascript" src="https://cdn.userway.org/widget.js"></script>


</head>
<body>
    <form id="form1" runat="server">

        <dx:BootstrapPopupControl runat="server" PopupElementCssSelector="#default-popup-control-5"
            PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" ShowCloseButton="false" CloseAction="CloseButton" ID="Modal" Modal="true" HeaderTex="Mensaje de Alerta">
            <ContentCollection>
                <dx:ContentControl>
                    <p runat="server" id="modalContent">Debe digitar su número de identidad</p>
                    <dx:ASPxButton ID="btnCerrar" ClientInstanceName="btnCerrar" CssClass="btn btn-secundary" runat="server" AutoPostBack="false" Text="Cerrar">
                        <ClientSideEvents Click="function(s, e) { Modal.Hide(); }" />
                    </dx:ASPxButton>
                </dx:ContentControl>
            </ContentCollection>
        </dx:BootstrapPopupControl>

        <header>
            <div class="container">
                <div class="logo">
                    <img src="../../images/logo.png" alt="INAIPI" />
                </div>
            </div>
        </header>
        <section class="container">
            <div class="tabla-login">

                <ul class="list-group">
                    <li class="list-group-item active">Olvidó su contraseña</li>
                    <li class="list-group-item">

                        <div class="form-row">


                            <div class="form-group col-md-12">
                                <label for="DocumentoIdentidad">Número de Identidad:</label>
                                <input type="text" class="form-control" id="OlvidoDocumentoIdentidad" placeholder="Digitar sin guiones" runat="server" />
                            </div>


                            <div class="form-group col-md-12">
                                <asp:Button CssClass="btn btn-primary btn-block" ID="btnResetearContrasena" OnClick="btnResetearContrasena_Click" runat="server" Text="Resetear Contraseña"></asp:Button>
                            </div>
                        </div>

                    </li>
                </ul>
            </div>
        </section>
    </form>
</body>
</html>

﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="olvidocontrasena.aspx.cs" Inherits="Formularios.Views.SolicitudesServicios.olvidocontrasena" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Solicitudes de Servicios</title>
    <link rel="icon" href="../../images/favicon.ico" />
    <meta charset="UTF-8" />
    <meta name="description" content="Solicitudes de Servicios" />
    <meta name="keywords" content="Solicitudes,Servicios,niños,niñas" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../Content/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="../../css/solicitudesservicios.css" />
    <!--Insertando el Jquery-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>

    <script src="../../js/solicitudesservicios.js"></script>
    <script type="text/javascript">
    var _userway_config = {
    /* uncomment the following line to override default position*/
    /* position: '6', */
    /* uncomment the following line to override default size (values: small, large)*/
    /* size: 'small', */
    /* uncomment the following line to override default language (e.g., fr, de, es, he, nl, etc.)*/
    language: 'es', 
    /* uncomment the following line to override color set via widget (e.g., #053f67)*/
    /* color: '#053f67', */
    /* uncomment the following line to override type set via widget(1=person, 2=chair, 3=eye)*/
    /* type: '1', */
    /* uncomment the following line to override support on mobile devices*/
    /* mobile: true, */
    account: 'QNhyx3cvTc'
    };
    </script>
    <script type="text/javascript" src="https://cdn.userway.org/widget.js"></script>

</head>

    
   <body class="body-login-contrasena">
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../images/logo.png" alt="INAIPI" />
            </div>
        </div>
    </header>
    <section class="container">
        <div class ="tabla-login">
            
            <ul class="list-group">
                <li class="list-group-item active">Olvidó su contraseña</li>
                <li class="list-group-item">
                    <form>
                        <div class="form-row">                           
                   
               
                            <div class="form-group col-md-12">
                                <label for="DocumentoIdentidad">Número de Identidad:</label>
                                <input type="text" class="form-control" id="OlvidoDocumentoIdentidad" placeholder="Digitar sin guiones" runat="server" />
                            </div>

                           
                            <div class="form-group col-md-12">
                                <button type="submit" class="btn btn-primary btn-block" id="btnResetearContrasena" runat="server">Resetear Contraseña</button>
                            </div>
                        </div>
                    </form>
                </li>
            </ul>
        </div>
    </section>
</body>
</html>

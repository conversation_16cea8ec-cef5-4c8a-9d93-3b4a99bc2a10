﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using INAIPI.Core;
using SI_INAIPI.Controls.INS;
using SI_INAIPI.Models.INS;

namespace Formularios.Views.SolicitudesServicios
{
    public partial class actualizar_contrasena : System.Web.UI.Page
    {
        INS_SolicitudesServiciosCtrl ctrl = new INS_SolicitudesServiciosCtrl();

        private INS_SolicitudesServicios Solicitud
        {
            get { return (INS_SolicitudesServicios)Session["solicitud_Solicitud"]; }
            set { Session["solicitud_Solicitud"] = value; }
        }
        private string Token
        {
            get { return (string)Session["Token"]; }
            set { Session["Token"] = value; }
        }

        protected void Page_Load(object sender, EventArgs e)
        {

            if (!IsPostBack)
            {
                //consultar y validar token, si token está consumido arrojar alerta
                MsgTokenVencido.Visible = false;
                FormActualizarContrasena.Visible = false;
                //DocumentoIdentidad.Value = Solicitud.DocumentoIdentidad;

                string cedula = Request.QueryString.Get("cedula");
                DocumentoIdentidad.Value = cedula;

                string token = Request.QueryString.Get("token");
                Token = token;

                bool token_vencido = ObtenerToken(token);

                if (token_vencido)
                {
                    MsgTokenVencido.Visible = true;
                    FormActualizarContrasena.Visible = false;
                }
                else
                {
                    MsgTokenVencido.Visible = false;
                    FormActualizarContrasena.Visible = true;
                    ctrl.ConsumirToken(Token);
                }
            }
        }
        private bool ObtenerToken(string _token)
        {
            Resultado resultado = ctrl.ConsultarToken(_token);
            return resultado.TodoBien;
        }

        protected void BtnActualizarConstrasena_Click(object sender, EventArgs e)
        {
            if (NuevaContrasena.Value == string.Empty || ConfirmarNuevaContrasena.Value == string.Empty)
            {
                Modal.ShowOnPageLoad = true;
                modalContent.InnerHtml = "No puede dejar campos en vacíos";
            }
            else
            {
                //if (NuevaContrasena.Value == ConfirmarNuevaContrasena.Value)
                if (NuevaContrasena.Value == ConfirmarNuevaContrasena.Value)
                {
                    Solicitud = ctrl.ConsultarSolicitadaPorDocumentoIdentidad(DocumentoIdentidad.Value, false)?.Objeto;
                    ctrl.CambiarContrasena(Solicitud.IdSolicitud, NuevaContrasena.Value);
                    //ctrl.ConsumirToken(Token);
                    Response.Redirect("~/Views/SolicitudesServicios/ActualizacionClave.aspx");
                }
                else
                {
                    Modal.ShowOnPageLoad = true;
                    modalContent.InnerHtml = "Las contraseñas no coinciden";
                }
            }
        }
    }
}
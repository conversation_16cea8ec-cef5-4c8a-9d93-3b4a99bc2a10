﻿using SI_INAIPI.Controls.INS;
using SI_INAIPI.Models.INS;
using System;

namespace Formularios.Views.SolicitudesServicios
{
    public partial class SolicitudEnviada : System.Web.UI.Page
    {
        INS_SolicitudesServiciosCtrl ctrl = new INS_SolicitudesServiciosCtrl();

        #region PROPIEDADES
        private INS_SolicitudesServicios Solicitud
        {
            get { return (INS_SolicitudesServicios)Session["solicitud_Solicitud"]; }
            set { Session["solicitud_Solicitud"] = value; }
        }
        #endregion PROPIEDADES

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                NombreCompleto.InnerText = Solicitud?.Nombre1?.Trim().ToUpper() + " " + Solicitud?.Apellido1?.Trim().ToUpper();
                NumeroSolicitud.InnerText = Solicitud?.IdSolicitud.ToString("00-00-00");
                if (!string.IsNullOrEmpty(Solicitud?.CorreoElectronico))
                {
                    txtNotificacionCorreo.Visible = true;
                    txtNotificacionTelefono.Visible = false;
                    string String1 = Solicitud?.CorreoElectronico.Split('@')[0];
                    string String2 = Solicitud?.CorreoElectronico.Split('@')[1];

                    CorreoUsuario.InnerText = String1.Substring(0, 3) + "*****@" + String2;
                }
                else
                {
                    txtNotificacionCorreo.Visible = false;
                    txtNotificacionTelefono.Visible = true;
                    infoCorreo.InnerText = "";
                }

                int cantidadDias = CantidadDiasParaRespuestaSolicitud();
                if (cantidadDias > 0)
                {
                    txtCantidadDias.InnerText = $"{cantidadDias}";
                }
            }
        }

        private int CantidadDiasParaRespuestaSolicitud()
        {
            int cantidad = ctrl.CantidadDiasParaRespuestaSolicitud();

            return cantidad;
        }
    }
}
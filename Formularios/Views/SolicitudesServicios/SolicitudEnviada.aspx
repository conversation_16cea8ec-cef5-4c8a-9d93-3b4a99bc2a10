﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="SolicitudEnviada.aspx.cs" Inherits="Formularios.Views.SolicitudesServicios.SolicitudEnviada" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Solicitud Enviada</title>
    <meta charset="UTF-8" />
    <meta name="description" content="Solicitudes de Servicios" />
    <meta name="keywords" content="Solicitudes,Servicios,niños,niñas" />
    <%--<meta name="viewport" content="width=device-width, initial-scale=1.0" />--%>
    <link rel="icon" href="../../images/favicon.ico" />
    <link rel="stylesheet" href="/Content/bootstrap.min.css" />
    <%--<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />--%>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="/css/solicitudesservicios.css" />
    <!--Insertando el Jquery-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script src="/Scripts/bootstrap.min.js"></script>
   <%-- <script src="/js/solicitudesservicios.js"></script>--%>
    <script type="text/javascript">
        var _userway_config = {
            /* uncomment the following line to override default position*/
            /* position: '6', */
            /* uncomment the following line to override default size (values: small, large)*/
            /* size: 'small', */
            /* uncomment the following line to override default language (e.g., fr, de, es, he, nl, etc.)*/
            language: 'es',
            /* uncomment the following line to override color set via widget (e.g., #053f67)*/
            /* color: '#053f67', */
            /* uncomment the following line to override type set via widget(1=person, 2=chair, 3=eye)*/
            /* type: '1', */
            /* uncomment the following line to override support on mobile devices*/
            /* mobile: true, */
            account: 'QNhyx3cvTc'
        };
    </script>
    <script type="text/javascript" src="https://cdn.userway.org/widget.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../images/logo.png" alt="INAIPI" />
            </div>
        </div>
    </header>
    <span class="ir-arriba icon-keyboard_arrow_up"></span>
    <section class="container">
        <div class="main" style="position: relative">
            <h1>¡Gracias <span id="NombreCompleto" runat="server"></span>!</h1>
            <form action="Condiciones.aspx" method="post" id="Condiciones" name="Condiciones" runat="server">
                <h3>Solicitud Recibida</h3>
                <p class="text-justify">Su solicitud ha sido recibida satisfactoriamente. </p>
                <p class="text-justify">Agradecemos de antemano su interés y confianza en nuestra institución para el desarrollo integral de sus hijos de 0 a 5 años.</p>
                <p class="text-justify"><b>¡OJO!</b> Esta solicitud será tomada en cuenta para servicios CAIPI o CAFI según aplique.</p>
                <p class="text-justify">
                    Su número de solicitud para fines de seguimiento es <strong><u id="NumeroSolicitud" runat="server">00-00-01</u></strong>. <span id="infoCorreo" runat="server">Hemos envíado a su correo electrónico <strong><u id="CorreoUsuario" runat="server">May***@gmail.com</u></strong>, su número de solicitud.</span>
                    <span>Su solicitud será procesada dentro de los próximos <b><u id="txtCantidadDias" runat="server"></u></b>&nbsp;días.</span>
                </p>
                <p id="txtNotificacionCorreo" class="text-justify" visible="false" runat="server">Se le notificará por correo electrónico el estatus de la misma.</p>
                <p id="txtNotificacionTelefono" class="text-justify" visible="false" runat="server">Se le notificará vía telefónica el estatus de la misma.</p>
                <p class="text-justify">Para más información comunicarse al <b><u>Centro de Atención al Usuario</u></b> <b>(<u>CAU</u>)</b> al número: <b><u>(*************</u></b></p>
                <div class="row my-2">
                    <div class="col-sm-6">
                        <a href="https://www.inaipi.gob.do" class="btn btn-secondary  btn-block" runat="server"><i class="fa fa-home"></i>&nbsp;Volver al Portal</a>
                    </div>
                    <div class="col-sm-6"></div>
                </div>
            </form>
        </div>
    </section>
</body>
</html>

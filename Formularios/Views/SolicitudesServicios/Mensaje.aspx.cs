﻿using SI_INAIPI.Models.INS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Formularios.Views.SolicitudesServicios
{
    public partial class Mensaje : System.Web.UI.Page
    {
        private INS_SolicitudesServicios Solicitud
        {
            get { return (INS_SolicitudesServicios)Session["solicitud_Solicitud"]; }
            set { Session["solicitud_Solicitud"] = value; }
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            /*
             *  NombreCompleto.InnerText = Solicitud.Nombre1.Trim().ToUpper() + " " + Solicitud.Apellido1.Trim().ToUpper();
            NumeroSolicitud.InnerText = Solicitud.IdSolicitud.ToString("00-00-00");
            string String1 = Solicitud.CorreoElectronico.Split('@')[0];
            string String2 = Solicitud.CorreoElectronico.Split('@')[1];

            CorreoUsuario.InnerText = String1.Substring(0,3) + "*****@" + String2;
             * */
            string String1 = Solicitud.CorreoElectronico.Split('@')[0];
            string String2 = Solicitud.CorreoElectronico.Split('@')[1];

            spanCorreoElectronico.InnerHtml = String1.Substring(0, 3) + "*****@" + String2;
        }
    }
}
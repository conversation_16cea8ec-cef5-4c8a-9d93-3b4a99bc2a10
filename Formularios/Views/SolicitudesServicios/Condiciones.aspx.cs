﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Formularios.Views.SolicitudesServicios
{
    public partial class Condiciones : System.Web.UI.Page
    {
        private string AceptarAvertencia
        {
            get { return (string)Session["AceptarCondicion"]; }
            set { Session["AceptarCondicion"] = value;  }
            
        }
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void btnAceptar_Click(object sender, EventArgs e)
        {
            AceptarAvertencia = "true";
            Response.Redirect("~/Views/SolicitudesServicios/solicitudes_login.aspx");
           
        }
    }
}
﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Solicitudes.aspx.cs" Inherits="Formularios.Views.SolicitudesServicios.Solicitudes" %>

<%@ Register Assembly="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web" TagPrefix="dx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Solicitudes de Servicios</title>
    <meta charset="UTF-8" />
    <meta name="description" content="Solicitudes de Servicios" />
    <meta name="keywords" content="Solicitudes,Servicios,niños,niñas" />
    <%--<meta name="viewport" content="width=device-width, initial-scale=1.0" />--%>
    <link rel="icon" href="../../images/favicon.ico" />
    <link rel="stylesheet" href="/Content/bootstrap.min.css" />
    <%--<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />--%>
    <link rel="stylesheet" type="text/css" href="/css/solicitudesservicios.css" />
    <!--Insertando el Jquery-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script src="/Scripts/bootstrap.min.js"></script>
    <script src="/js/solicitudesservicios.js"></script>
    <script src="https://www.google.com/recaptcha/api.js"></script>
    <script type="text/javascript">
        var _userway_config = {
            /* uncomment the following line to override default position*/
            /* position: '6', */
            /* uncomment the following line to override default size (values: small, large)*/
            /* size: 'small', */
            /* uncomment the following line to override default language (e.g., fr, de, es, he, nl, etc.)*/
            language: 'es',
            /* uncomment the following line to override color set via widget (e.g., #053f67)*/
            /* color: '#053f67', */
            /* uncomment the following line to override type set via widget(1=person, 2=chair, 3=eye)*/
            /* type: '1', */
            /* uncomment the following line to override support on mobile devices*/
            /* mobile: true, */
            account: 'QNhyx3cvTc'
        };
    </script>
    <script type="text/javascript" src="https://cdn.userway.org/widget.js"></script>
    <script>
        function CambiarNombre() {
            console.log($("#TipoDocumentoIdentidad").val());
            var Documento = $("#TipoDocumentoIdentidad").val();
            switch (Documento) {
                case "0":
                    $("#DocumentoIdentidad").attr("placeholder", "Número de Pasaporte");
                    document.getElementById("lblDocumentoIdentidad").innerHTML = "Número de Pasaporte";
                    break;
                case "1":
                    $("#DocumentoIdentidad").attr("placeholder", "Número de Cédula");
                    document.getElementById("lblDocumentoIdentidad").innerHTML = "Número de Cédula";
                    break;
                case "2":
                    $("#DocumentoIdentidad").attr("placeholder", "Número de Teléfono");
                    document.getElementById("lblDocumentoIdentidad").innerHTML = "Número de Teléfono";
                    document.getElementById("lblCorreoElectronico").innerHTML = sessionStorage.lblCorreoElectronico;
                    break;
            }
        }
    </script>
</head>
<body class="bg-body">
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../images/logo.png" alt="INAIPI" />
            </div>
        </div>
    </header>
    <span class="ir-arriba icon-keyboard_arrow_up"></span>
    <section class="container">
        <div class="main" style="position: relative">
            <div id="HistorialPasos">Paso <span id="CantidadPasos">1</span> de 5</div>
            <h1>Solicitud de Servicios</h1>
            <!--<div class="solicitud_subtitulo">
                <span>Ingrese la información de contacto.</span>
            </div>-->
            <form action="Solicitudes.aspx" method="post" id="solicitudes" name="formulario" runat="server">
                <textarea id="DataJson" style="position: absolute; left: -400px; top: 0; visibility: hidden" cols="45" runat="server" rows="25"></textarea>
                <input type="hidden" id="IdSolicitud" name="IdSolicitud" runat="server" />

                <dx:BootstrapPopupControl runat="server" PopupElementCssSelector="#default-popup-control-5"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" ShowCloseButton="false" CloseAction="CloseButton" ID="Modal" Modal="true" HeaderTex="Mensaje de Alerta">
                    <ContentCollection>
                        <dx:ContentControl>
                            <p runat="server" id="modalContent"></p>
                            <dx:ASPxButton ID="btnCerrar" ClientInstanceName="btnCerrar" CssClass="btn btn-secundary" runat="server" AutoPostBack="false" Text="Cerrar">
                                <ClientSideEvents Click="function(s, e) { Modal.Hide(); }" />
                            </dx:ASPxButton>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>

                <!--Datos Usuario-->
                <div id="Paso1">
                    <h3>Solicitud de usuario</h3>
                    <div class="solicitud_subtitulo">
                        <span>Por favor ingrese la información necesaria para registrarse. Las informaciones ingresadas sólo serán utilizadas por el Departamento de INAIPI</span>
                    </div>

                    <div class="container_data">
                        <div class="form-row">
                            <div class="form-group required col-md-6">
                                <label for="TipoDocumentoIdentidad" class="control-label">Método de Identificación:</label>
                                <select class="form-control" onchange="CambiarNombre()" id="TipoDocumentoIdentidad" name="TipoDocumentoIdentidad" maxlength="20" required="required" disabled="disabled" runat="server">
                                    <option value="" selected="">Seleccione</option>
                                    <option value="1">Cédula</option>
                                    <option value="0">Pasaporte</option>
                                    <option value="2">Número de Teléfono</option>
                                </select>
                            </div>
                            <div class="form-group required col-md-6">
                                <label id="lblDocumentoIdentidad" for="DocumentoIdentidad" class="control-label">Número de Identificación:</label>
                                <input type="text" class="form-control" id="DocumentoIdentidad" placeholder="Digitar sin guiones" required="required" disabled="disabled" runat="server" />
                            </div>
                        </div>
                        <div class="form-row" id="hideGroup">
                            <div class="form-group required col-md-6">
                                <label for="Contrasena" class="control-label">Contraseña:</label>
                                <input type="password" class="form-control" id="Contrasena" runat="server" required="required" placeholder="mínimo 6 caracteres" maxlength="20" />
                            </div>
                            <div class="form-group required col-md-6">
                                <label for="ConfirmarContrasena" class="control-label">Confirme su contraseña:</label>
                                <input type="password" class="form-control" id="ConfirmarContrasena" runat="server" required="required" placeholder="mínimo 6 caracteres" maxlength="20" />
                            </div>
                            <small class="col-md-12 form-text text-muted">Por favor, recuerde su contraseña.</small>
                        </div>
                    </div>
                    <div class="row my-2">
                        <div class="col-sm-6">
                            <button type="button" onclick="VolverAtras()" class="btn btn-primary btn-block" id="btnVolverCondiciones" runat="server">Volver</button>
                        </div>
                        <div class="col-sm-6">
                            <button type="button" class="btn btn-primary btn-block" id="btnPaso1" runat="server">Siguiente</button>
                        </div>
                    </div>
                </div>

                <!--Datos Personales-->
                <div id="Paso2" class="OcultarPasos">
                    <h3>Datos Personales del Solicitante</h3>
                    <div class="solicitud_subtitulo">
                        <span>Ingrese la información requerida para continuar.</span>
                    </div>

                    <div class="container_data">
                        <!--Fecha que aplica-->
                        <div class="form-inline form-group" style="visibility: hidden; height: 0">
                            <label for="FechaRegistro">Fecha en la que aplica:</label>
                            <input type="text" class="form-control col-sm-4 mx-2" id="FechaRegistro" name="FechaRegistro" runat="server" />
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="TipoCedulaOPasaporte" class="control-label">Tipo de Identidad:</label>
                                <select class="form-control" id="TipoCedulaOPasaporte" name="TipoCedulaOPasaporte" maxlength="20" runat="server">
                                    <option value="" selected="">Seleccione</option>
                                    <option value="1">Cédula</option>
                                    <option value="0">Pasaporte</option>
                                </select>
                                <small class="col-md-12 form-text text-muted">*Cédula o Pasaporte*.</small>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="CedulaOPasaporte" class="control-label">Documento de Identidad:</label>
                                <input type="text" class="form-control" id="CedulaOPasaporte" placeholder="Digitar sin guiones" runat="server" />
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group required col-md-6">
                                <label for="Nombre1" class="control-label">Primer Nombre:</label>
                                <input type="text" class="form-control" id="Nombre1" onkeypress="GenerarJson();return permite(event, 'car')" required="required" maxlength="50" runat="server" />
                            </div>
                            <div class="form-group col-md-6">
                                <label for="Nombre2">Segundo Nombre:</label>
                                <input type="text" class="form-control" id="Nombre2" onkeypress="return permite(event, 'car')" maxlength="50" runat="server" />
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group required col-md-6">
                                <label for="Apellido1" class="control-label">Primer Apellido:</label>
                                <input type="text" class="form-control" id="Apellido1" onkeypress="return permite(event, 'car')" required="required" maxlength="50" runat="server" />
                            </div>
                            <div class="form-group col-md-6">
                                <label for="Apellido2">Segundo Apellido:</label>
                                <input type="text" class="form-control" id="Apellido2" onkeypress="return permite(event, 'car')" maxlength="50" runat="server" />
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label id="lblCorreoElectronico" for="CorreoElectronico" class="control-label">Correo Electrónico :</label>
                                <input type="email" class="form-control" id="CorreoElectronico" placeholder="Email" runat="server" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="form-group col-md-4">
                                <label for="TelefonoResidencial">Teléfono Residencia:</label>
                                <input type="tel" name="TelefonoResidencial" class="form-control" value="" id="TelefonoResidencial" placeholder="Número Residencial" pattern="[0-9]{10}" onkeypress="return permite(event, 'num')" runat="server" />
                            </div>
                            <div class="form-group col-md-4">
                                <label for="TelefonoMobil">Teléfono Celular:</label>
                                <input type="tel" name="TelefonoMobil" class="form-control" value="" id="TelefonoMobil" pattern="[0-9]{10}" placeholder="Número celular" onkeypress="return permite(event, 'num')" runat="server" />
                            </div>
                            <div class="form-group col-md-4">
                                <label for="TelefonoOtro">Otro:</label>
                                <input type="tel" title="TelefonoOtro" name="TelefonoOtro" class="form-control" value="" id="TelefonoOtro" placeholder="Otro Número" pattern="[0-9]{10}" onkeypress="return permite(event, 'num')" runat="server" />
                                <!--maxlength="10" autocomplete="off" pattern="[0-9]{10}" placeholder="Digitar sin guiones"-->
                            </div>
                            <small class="col-md-12 form-text text-muted">***Por favor, ingrese al menos uno de los siguientes 3 teléfonos***.</small>
                        </div>
                    </div>

                    <!--siguiente paso 2-->
                    <div class="row my-2">
                        <div class="col-sm-6">
                            <button type="button" class="btn btn-secondary  btn-block" id="btnVolverPaso1">Volver</button>
                        </div>
                        <div class="col-sm-6">
                            <button type="button" class="btn btn-primary  btn-block" id="btnPaso2">Siguiente</button>
                        </div>
                    </div>
                </div>

                <!--Dirección-->
                <div id="Paso3" class="OcultarPasos">
                    <h3>Datos de la Dirección de contacto</h3>
                    <div class="solicitud_subtitulo">
                        <span>Ingrese la información requerida para continuar.</span>
                    </div>
                    <div class="container_data">
                        <div class="form-row">
                            <div class="form-group required col-md-12">
                                <dx:BootstrapComboBox ID="cboMunicipios" Width="100%" AutoPostBack="false" EnableCallbackMode="false" ClientInstanceName="cboMunicipios" Caption="Municipio:" ViewStateMode="Disabled" EnableTheming="false" ValueField="Id" runat="server">
                                    <CaptionSettings Position="Before" />
                                    <ValidationSettings ErrorText="ESTE CAMPO ES REQUERIDO."></ValidationSettings>
                                </dx:BootstrapComboBox>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group required col-md-6">
                                <label for="Sector" class="control-label">Sector:</label>
                                <input type="text" class="form-control" id="Sector" required="required" maxlength="50" runat="server" />
                            </div>
                            <div class="form-group col-md-6">
                                <label for="Barrio">Barrio:</label>
                                <input type="text" class="form-control" id="Barrio" maxlength="50" runat="server" />
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group required col-md-6">
                                <label for="Calle" class="control-label">Direccion:</label>
                                <input type="text" class="form-control" id="Calle" placeholder="Calle o Avenida" required="required" maxlength="50" runat="server" />
                            </div>
                            <div class="form-group required col-md-6">
                                <label for="NumeroVivienda" class="control-label">Número Casa:</label>
                                <input type="text" class="form-control" id="NumeroVivienda" required="required" maxlength="50" runat="server" />
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-4">
                                <label for="Manzana">Manzana:</label>
                                <input type="text" class="form-control" id="Manzana" maxlength="50" runat="server" />
                            </div>
                            <div class="form-group col-md-4">
                                <label for="Apartamento">Apartamento:</label>
                                <input type="text" class="form-control" id="Apartamento" maxlength="50" runat="server" />
                            </div>
                            <div class="form-group col-md-4">
                                <label for="Edificio">Edificio:</label>
                                <input type="text" class="form-control" id="Edificio" maxlength="50" runat="server" />
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="EntreLaCalle">Entre la Calle:</label>
                                <input type="text" class="form-control" id="EntreLaCalle" maxlength="50" runat="server" />
                            </div>
                            <div class="form-group col-md-6">
                                <label for="YLaCalle">Y la Calle:</label>
                                <input type="text" class="form-control" id="YLaCalle" maxlength="50" runat="server" />
                            </div>
                        </div>
                    </div>

                    <!--Volver y siguente paso 3-->
                    <div class="row my-2">
                        <div class="col-sm-6">
                            <button type="button" class="btn btn-secondary  btn-block" id="btnVolverPaso2">Volver</button>
                        </div>
                        <div class="col-sm-6">
                            <button type="button" class="btn btn-primary  btn-block" id="btnPaso3">Siguiente</button>
                        </div>
                    </div>
                </div>

                <div id="Paso4" class="OcultarPasos">
                    <!--Datos de los niños-->
                    <h3>Datos de los niños/as del núcleo familiar</h3>
                    <div class="solicitud_subtitulo">
                        <span>Ingrese la información de los niños/as entre edades de 0 y 5 años o de las gestantes.</span>
                    </div>
                    <div class="container_data" id="cantidad_ninos">
                        <div class="form-group row">
                            <label for="CantidadNinos" class="col-auto col-form-label">¿Cuántos niños menores de 5 años en su familia necesitan el servicio?</label>
                            <label id="lblCantidadNNs" class="col-12 col-form-label" runat="server"></label>
                            <div id="divCantidadNNs" class="col-md-2" runat="server">
                                <input type="number" class="form-control" id="CantidadNinos" min="0" runat="server" />
                            </div>
                        </div>
                    </div>
                    <div id="contenedor_form_ninos" runat="server">
                    </div>
                    <button id="btnAgregarNino" style="margin-top: 10px" class="btn btn-primary btn-block" type="button" runat="server">Agregar otro niño</button>
                    <div class="container_data" id="info_gestante" style="margin-top: 10px;">
                        <div class="form-group row">
                            <label for="cboRespuestaGestante" class="col-auto col-form-label">¿En el hogar hay alguna gestante?</label>
                            <div class="col-md-2">
                                <select id="cboRespuestaGestante" name="cboRespuestaGestante" runat="server" class="form-control" onchange="HabilitarPanelGestante()">
                                    <option value="0" selected="selected">NO</option>
                                    <option value="1">SI</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div id="contenedor_form_gestante" runat="server" style="display: none; margin-top: 10px;">
                        <div class="container_data" id="cantidad_gestantes">
                            <div class="form-group row">
                                <label for="CantidadGestantes" class="col-auto col-form-label">¿Cuántas gestantes en su familia necesitan el servicio?</label>
                                <label id="lblCantidadGestantes" class="col-12 col-form-label" runat="server"></label>
                                <div id="divCantidadGestantes" class="col-md-2" runat="server">
                                    <input type="number" class="form-control" id="CantidadGestantes" min="0" runat="server" />
                                </div>
                            </div>
                        </div>
                        <div id="contenedor_form_info_gestante" runat="server">
                        </div>
                        <button id="btnAgregarGestante" style="margin-top: 10px" class="btn btn-primary btn-block" type="button" runat="server">Agregar otra gestante</button>
                    </div>
                    <div class="row my-2">
                        <div class="col-sm-6">
                            <button type="button" class="btn btn-secondary  btn-block" id="btnVolverPaso3">Volver</button>
                        </div>
                        <div class="col-sm-6">
                            <button type="button" class="btn btn-primary  btn-block" id="btnPaso4">Siguiente</button>
                        </div>
                    </div>
                    <!--volver y siguiente paso 4-->
                    <!--<div class="row my-2">                   
                    <div class="col-sm-6"><button type="button" class="btn btn-secondary  btn-block" id="btnVolver4" runat="server" >Volver</button></div>
                    <div class="col-sm-6"><button type="button" class="btn btn-primary  btn-block" id="btnSiguiente4" runat="server">Siguiente</button></div>
                </div>-->
                </div>

                <div id="Paso5" class="OcultarPasos">
                    <!--Verificacion del Captcha-->
                    <h3>Verificación de Seguridad</h3>
                    <div class="solicitud_subtitulo">
                        <span>Verificación de captcha para enviar correctamente su solicitud.</span>
                    </div>

                    <div class="container_data">
                        <div class="form-group row">
                            <%--click="HabilitarBotonEnviar()"--%>
                            <div class="g-recaptcha form-group col-md-4" data-sitekey="6Ld62FIUAAAAAPPMDUBZVZVDChhOaKv4MIqjyRGz"></div>
                            <div class="alert alert-danger form-group col-md-4" id="mensaje-captcha" style="margin-left: 10px; display: none">
                                <strong>Error</strong> Debe seleccionar el recaptcha
                            </div>
                        </div>
                    </div>

                    <!--Enviar formulario-->
                    <div class="row my-2">
                        <div class="col-sm-6">
                            <button type="button" class="btn btn-secondary  btn-block" id="btnVolverPaso4" runat="server">Volver al paso 4</button>
                        </div>
                        <div class="col-sm-6">
                            <asp:Button ID="btnEnviar" CssClass="btn btn-primary  btn-block" Text="Enviar Solicitud" OnClick="btnEnviar_Click" runat="server" />
                        </div>
                    </div>
                </div>
            </form>
            <!--final del contenedor y la seccion-->
        </div>
    </section>

    <!-- Modal Mensaje -->
    <div class="modal fade" id="ModalMensaje" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">Mensaje</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p id="msjmodal">Usted tiene registro creado. Si desea hacer alguna modificación favor escriba su contraseña y presione buscar para obtener los datos del registro.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btn-cerrar" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>
    <script>
            if ($("#IdSolicitud").val() != "") {
                $("#Contrasena").val("------");
                $("#ConfirmarContrasena").val("------");
            }
    </script>
    <!--Captcha-->
    <script src="https://www.google.com/recaptcha/api.js?render=explicit" async defer></script>

    <script type="text/javascript"> 
            var onloadCallback = function () {
                alert("grecaptcha is ready!");
            };
            //onloadCallback();

            var response = grecaptcha.getResponse();
            if (response.length == 0) {
                alert("Captcha no verificado");
                //$("#msjmodal").text("Captcha no verificado");
                //$("#ModalVerificarCedula").modal("show");
                //$("#mensaje-captcha").show();
                //$("#mensaje-captcha").html('Captcha no verificado')
            }
    </script>

    <!--Fin  Captcha-->
</body>
</html>


﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="solicitudes_login.aspx.cs" Inherits="Formularios.Views.SolicitudesServicios.solicitudes_login" %>

<%@ Register Assembly="DevExpress.Web.Bootstrap.v20.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.v20.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web" TagPrefix="dx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Solicitudes de Servicios</title>
    <link rel="icon" href="../../images/favicon.ico" />
    <meta charset="UTF-8" />
    <meta name="description" content="Solicitudes de Servicios" />
    <meta name="keywords" content="Solicitudes,Servicios,niños,niñas" />

    
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/Content/bootstrap.min.css" />

    <!--Insertando el Jquery-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>
  <%--  <script src="/js/solicitudesservicios.js"></script>--%>
    <script src="/js/sweetalert2.min.js"></script>



    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css" href="/css/solicitudesservicios.css" />


    <!-- Latest compiled JavaScript -->

    <script type="text/javascript">
        var _userway_config = {
            /* uncomment the following line to override default position*/
            /* position: '6', */
            /* uncomment the following line to override default size (values: small, large)*/
            /* size: 'small', */
            /* uncomment the following line to override default language (e.g., fr, de, es, he, nl, etc.)*/
            language: 'es',
            /* uncomment the following line to override color set via widget (e.g., #053f67)*/
            /* color: '#053f67', */
            /* uncomment the following line to override type set via widget(1=person, 2=chair, 3=eye)*/
            /* type: '1', */
            /* uncomment the following line to override support on mobile devices*/
            /* mobile: true, */
            account: 'QNhyx3cvTc'
        };
    </script>
    <script type="text/javascript" src="https://cdn.userway.org/widget.js"></script>
    <script>
        function CambiarNombre() {
            console.log($("#TipoDocumentoIdentidad").val());
            var Documento = $("#TipoDocumentoIdentidad").val();
            switch (Documento) {
                case "0":
                    $("#DocumentoIdentidad").attr("placeholder", "Número de Pasaporte");
                    document.getElementById("lblDocumentoIdentidad").innerHTML = "Número de Pasaporte";
                    break;
                case "1":
                    $("#DocumentoIdentidad").attr("placeholder", "Número de Cédula");
                    document.getElementById("lblDocumentoIdentidad").innerHTML = "Número de Cédula";
                    break;
                case "2":
                    $("#DocumentoIdentidad").attr("placeholder", "Número de Teléfono");
                    document.getElementById("lblDocumentoIdentidad").innerHTML = "Número de Teléfono";
                    break;
            }            
        }
    </script>


</head>
<body class="body-login">
    <form method="post" name="formulario_login" runat="server">
        <dx:BootstrapPopupControl runat="server" PopupElementCssSelector="#default-popup-control-5"
            PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" HeaderText="INFORMACIÓN" ShowCloseButton="false" CloseAction="CloseButton" ID="Modal" Modal="true">
            <ContentCollection>
                <dx:ContentControl>
                    <p runat="server" id="modalContent">Los campos están vacíos. Debe seleccionar el tipo de documento y llenar el campo de método de identidad</p>
                    <dx:ASPxButton ID="btnCerrar" ClientInstanceName="btnCerrar" CssClass="btn btn-secundary" runat="server" AutoPostBack="false" Text="Cerrar">
                        <ClientSideEvents Click="function(s, e) { Modal.Hide(); }" />
                    </dx:ASPxButton>
                </dx:ContentControl>
            </ContentCollection>
        </dx:BootstrapPopupControl>

        <header>
            <div class="container">
                <div class="logo">
                    <img src="../../images/logo.png" alt="INAIPI" />
                </div>
            </div>
        </header>

        <section class="container">
            <div class="tabla-login">
                <ul class="list-group">
                    <li class="list-group-item active">Bienvenidos a Solicitudes de Servicios INAIPI</li>
                    <li class="list-group-item">
                        <div class="form-group col-md-12">
                            <label for="TipoDocumentoIdentidad">Método de Identificación:</label>
                            <select class="form-control" onchange="CambiarNombre()" id="TipoDocumentoIdentidad" name="TipoDocumentoIdentidad" maxlength="20" runat="server">
                                <option value="" disabled="" selected="">Seleccione</option>
                                <option value="1">Cédula</option>
                                <option value="0">Pasaporte</option>
                                <option value="2">Número de Teléfono</option>
                            </select>
                        </div>
                        <div class="form-group col-md-12">
                            <label id="lblDocumentoIdentidad" for="DocumentoIdentidad">Número de Identificación:</label>
                            <input type="text" class="form-control" id="DocumentoIdentidad" placeholder="Digitar sin guiones" runat="server" />
                        </div>
                        <div class="form-group col-md-12">
                            <asp:Button CssClass="btn btn-primary btn-md btn-block" ID="btnContinuarContrasena" OnClick="btnContinuarContrasena_Click" Text="Continuar" runat="server" />
                            <%--<button class="btn btn-primary btn-md btn-block" id="btnContinuarContrasena">Continuar</button>--%>
                        </div>
                    </li>
                    <!-- <li class="list-group-item">
                     <div class="form-group col-md-12">
                      <a href="solicitudes.aspx" class="btn btn-link btn-md btn-block">Registrarse como nuevo Usuario</a>
                    </div>
               </li>-->
                </ul>
            </div>
        </section>
    </form>
    <%--<script>
        function Alerta(s, e) {
            var title = "UNA OBSERVACIÓN";
            var text = s.cp_text;
            var type = s.cp_type;
            var pc = s.cp_pc;

            swal('Any fool can use a computer')
        }
    </script>--%>
    <script type="text/javascript" src="/js/data.js"></script>
    <script type="text/javascript" src="/js/form.js"></script>
    <script type="text/javascript" src="/js/app.js"></script>
</body>
</html>


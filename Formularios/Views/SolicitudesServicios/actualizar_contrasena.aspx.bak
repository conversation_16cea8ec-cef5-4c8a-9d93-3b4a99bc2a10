﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="actualizar_contrasena.aspx.cs" Inherits="Formularios.Views.SolicitudesServicios.actualizar_contrasena" %>

<%@ Register Assembly="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web" TagPrefix="dx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Actualizar contraseña</title>
    <link rel="icon" href="../../images/favicon.ico" />
    <meta charset="UTF-8" />
    <meta name="description" content="Restablecer contraseña" />
    <meta name="keywords" content="Solicitudes,Servicios,niños,niñas" />
     <link rel="stylesheet" href="/Content/bootstrap.min.css" />
    
    <!--Insertando el Jquery-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script src="/js/solicitudesservicios.js"></script>
    <script src="/js/sweetalert2.min.js"></script>


    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/css/solicitudesservicios.css" />

    <script type="text/javascript">
    var _userway_config = {
    /* uncomment the following line to override default position*/
    /* position: '6', */
    /* uncomment the following line to override default size (values: small, large)*/
    /* size: 'small', */
    /* uncomment the following line to override default language (e.g., fr, de, es, he, nl, etc.)*/
    language: 'es', 
    /* uncomment the following line to override color set via widget (e.g., #053f67)*/
    /* color: '#053f67', */
    /* uncomment the following line to override type set via widget(1=person, 2=chair, 3=eye)*/
    /* type: '1', */
    /* uncomment the following line to override support on mobile devices*/
    /* mobile: true, */
    account: 'QNhyx3cvTc'
    };
    </script>
    <script type="text/javascript" src="https://cdn.userway.org/widget.js"></script>




</head>
<body>
    <form runat="server">
        <dx:BootstrapPopupControl runat="server" PopupElementCssSelector="#default-popup-control-5"
            PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" ShowCloseButton="false" CloseAction="CloseButton" ID="Modal" Modal="true" HeaderTex="Mensaje de Alerta">
            <ContentCollection>
                <dx:ContentControl>
                    <p runat="server" id="modalContent">Las constraseñas no coinciden</p>
                    <dx:ASPxButton ID="btnCerrar" ClientInstanceName="btnCerrar" CssClass="btn btn-secundary" runat="server" AutoPostBack="false" Text="Cerrar">
                        <ClientSideEvents Click="function(s, e) { Modal.Hide(); }" />
                    </dx:ASPxButton>
                </dx:ContentControl>
            </ContentCollection>
        </dx:BootstrapPopupControl>


        <header>
            <div class="container">
                <div class="logo">
                    <img src="../../images/logo.png" alt="INAIPI" />
                </div>
            </div>
        </header>
        <span class="ir-arriba icon-keyboard_arrow_up"></span>


        <section class="container">
            <div class="main" style="position: relative" id="MsgTokenVencido" runat="server">

                <h1>Error 404</h1>

                <!-- <h3>Este token ha caducado.</h3>-->
                <p>Este enlace ya no está disponible.</p>


                <div class="row my-2">
                    <div class="col-sm-6">
                        <a href="https://www.inaipi.gob.do" class="btn btn-secondary  btn-block" runat="server"><i class="fa fa-home"></i>Volver al Portal</a>
                    </div>
                    <div class="col-sm-6">
                        <a href="/Views/SolicitudesServicios/Solicitudes.aspx" class="btn btn-primary  btn-block" runat="server">Volver al Formulario</a>

                    </div>
                </div>


            </div>

            <div style="position: relative" id="FormActualizarContrasena" runat="server">
       
                    <div class="tabla-login" style="margin-top:50px">

                        <ul class="list-group">
                            <li class="list-group-item active">Actualizar constraseña</li>
                            <li class="list-group-item">

                                <div class="form-row">


                                    <div class="form-group col-md-12">
                                        <label for="DocumentoIdentidad">Número de Identidad:</label>
                                        <input readonly type="text" class="form-control" placeholder="Digitar sin guiones" runat="server" id="DocumentoIdentidad" />
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label for="DocumentoIdentidad">Nueva contraseña:</label>
                                        <input type="password" class="form-control" placeholder="" runat="server" id="NuevaContrasena" maxlength="45" />
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label for="DocumentoIdentidad">Confirmar nueva constraseña:</label>
                                        <input type="password" class="form-control" placeholder="" runat="server" id="ConfirmarNuevaContrasena" maxlength="45" />
                                    </div>

                                    <div class="form-group col-md-12">
                                        <asp:Button OnClick="BtnActualizarConstrasena_Click" CssClass="btn btn-primary btn-block" ID="BtnActualizarConstrasena" runat="server" Text="Actualizar Contraseña"></asp:Button>
                                    </div>
                                </div>

                            </li>
                        </ul>
                    </div>
             
            </div>

        </section>


    </form>
</body>
</html>

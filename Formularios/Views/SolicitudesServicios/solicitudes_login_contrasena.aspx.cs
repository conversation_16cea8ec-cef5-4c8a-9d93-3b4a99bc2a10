﻿using SI_INAIPI.Models.INS;
using System;

namespace Formularios.Views.SolicitudesServicios
{
    public partial class solicitudes_login_contrasena : System.Web.UI.Page
    {
        private INS_SolicitudesServicios Solicitud
        {
            get { return (INS_SolicitudesServicios)Session["solicitud_Solicitud"]; }
            set { Session["solicitud_Solicitud"] = value; }
        }
        private bool EntradaPermitida
        {
            get { return (bool)(Session["solicitud_EntradaPermitida"] ?? false); }
            set { Session["solicitud_EntradaPermitida"] = value; }
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void btnContinuarSolicitud_Click(object sender, EventArgs e)
        {
            if(Solicitud.Contrasena == Contrasena.Value)
            {
                EntradaPermitida = true;
                Response.Redirect("~/Views/SolicitudesServicios/Solicitudes.aspx");
            }
            else
            {
                Modal.ShowOnPageLoad = true;
            }
        }
    }
}
﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="solicitudes_login_contrasena.aspx.cs" Inherits="Formularios.Views.SolicitudesServicios.solicitudes_login_contrasena" %>

<%@ Register Assembly="DevExpress.Web.Bootstrap.v20.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.v20.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web" TagPrefix="dx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Solicitudes de Servicios</title>
    <meta charset="UTF-8" />
    <meta name="description" content="Solicitudes de Servicios" />
    <meta name="keywords" content="Solicitudes,Servicios,niños,niñas" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="../../images/favicon.ico" />
    <link rel="stylesheet" href="../../Content/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="../../css/solicitudesservicios.css" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />
    <!--Insertando el Jquery-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>
  <%--  <script src="../../js/solicitudesservicios.js"></script>--%>

    <script type="text/javascript">
        var _userway_config = {
            /* uncomment the following line to override default position*/
            /* position: '6', */
            /* uncomment the following line to override default size (values: small, large)*/
            /* size: 'small', */
            /* uncomment the following line to override default language (e.g., fr, de, es, he, nl, etc.)*/
            language: 'es',
            /* uncomment the following line to override color set via widget (e.g., #053f67)*/
            /* color: '#053f67', */
            /* uncomment the following line to override type set via widget(1=person, 2=chair, 3=eye)*/
            /* type: '1', */
            /* uncomment the following line to override support on mobile devices*/
            /* mobile: true, */
            account: 'QNhyx3cvTc'
        };
    </script>
    <script type="text/javascript" src="https://cdn.userway.org/widget.js"></script>
</head>
<body class="body-login-contrasena">
    <form runat="server">
        <dx:BootstrapPopupControl runat="server" PopupElementCssSelector="#default-popup-control-5"
            PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" ShowCloseButton="false" CloseAction="CloseButton" ID="Modal" Modal="true">
            <ContentCollection>
                <dx:ContentControl>
                    <p runat="server" id="modalContent">La contraseña está incorrecta</p>
                    <dx:ASPxButton ID="btnCerrar" ClientInstanceName="btnCerrar" CssClass="btn btn-secundary" runat="server" AutoPostBack="false" Text="Cerrar">
                        <ClientSideEvents Click="function(s, e) { Modal.Hide(); }" />
                    </dx:ASPxButton>
                </dx:ContentControl>
            </ContentCollection>
        </dx:BootstrapPopupControl>
        <header>
            <div class="container">
                <div class="logo">
                    <img src="../../images/logo.png" alt="INAIPI" />
                </div>
            </div>
        </header>
        <section class="container">
            <div class="tabla-login">
                <ul class="list-group">
                    <li class="list-group-item active">Bienvenidos a Solicitudes de Servicios INAIPI</li>
                    <li class="list-group-item">
                        <div class="form-group col-md-12">
                            <label for="Contrasena">Ingrese su Contraseña:</label>
                            <input type="password" class="form-control" id="Contrasena" placeholder="mínimo 6 caracteres" runat="server" />
                        </div>
                    </li>
                    <li class="list-group-item">
                        <div class="form-group col-md-6">
                            <button type="button" onclick="VolverAtras()" class="button btn btn-primary btn-md btn-block" id="btnVolverCondiciones" runat="server">Volver</button>
                        </div>
                        <div class="form-group col-md-6">
                            <asp:Button CssClass="button btn btn-primary btn-md btn-block" Text="Continuar" OnClick="btnContinuarSolicitud_Click" runat="server" ID="btnContinuarSolicitud" />
                        </div>
                    </li>
                    <li class="list-group-item">
                        <div class="form-group col-md-12">
                            <a href="recuperar_contrasena.aspx" class="btn btn-link btn-md btn-block" id="EnviarOlvidoContrasena" runat="server">Olvidó su Contraseña?</a>
                        </div>
                    </li>
                </ul>
            </div>
        </section>
    </form>
</body>
</html>

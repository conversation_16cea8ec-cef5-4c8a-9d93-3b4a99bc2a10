﻿using INAIPI.Core;
using SI_INAIPI.Controls.INS;
using SI_INAIPI.Models.INS;
using System;
using System.Collections.Generic;
using System.Web.UI;

namespace Formularios.Views.SolicitudesServicios
{
    public partial class Solicitudes : BasePage
    {
        INS_SolicitudesServiciosCtrl ctrl = new INS_SolicitudesServiciosCtrl();

        #region PROPIEDADES
        private List<SI_INAIPI.Models.COMUN.COMUN_Municipio> Municipios
        {
            get { return (List<SI_INAIPI.Models.COMUN.COMUN_Municipio>)Session["SolicitudesServicios_Municipios"]; }
            set { Session["SolicitudesServicios_Municipios"] = value; }
        }
        private INS_SolicitudesServicios Solicitud
        {
            get { return (INS_SolicitudesServicios)Session["solicitud_Solicitud"]; }
            set { Session["solicitud_Solicitud"] = value; }
        }
        private string IdDocumentoIdentidad
        {
            get { return (string)(Session["solicitud_IdDocumentoIdentidad"] ?? ""); }
            set { Session["solicitud_IdDocumentoIdentidad"] = value; }
        }
        private byte IdTipoDocumentoIdentidad
        {
            get { return (byte)(Session["solicitud_IdTipoDocumentoIdentidad"] ?? 0); }
            set { Session["solicitud_IdTipoDocumentoIdentidad"] = value; }
        }
        private bool EntradaPermitida
        {
            get { return (bool)(Session["solicitud_EntradaPermitida"] ?? false); }
            set { Session["solicitud_EntradaPermitida"] = value; }
        }
        #endregion PROPIEDADES

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Solicitud != null)
            {
                ScriptManager.RegisterStartupScript(this, typeof(Page), "invocarfuncion", "$('#hideGroup').css('visibility','hidden'); $('#hideGroup').css('height','0px');", true);
            }

            if (!IsPostBack && !EntradaPermitida)
            {
                Response.Redirect("~/Views/SolicitudesServicios/solicitudes_login.aspx");
            }
            else
            {
                EntradaPermitida = false;
            }

            if (!IsPostBack && Solicitud != null)
            {
                BindiarFormulario();
            }

            TipoDocumentoIdentidad.Value = IdTipoDocumentoIdentidad.ToString();
            DocumentoIdentidad.Value = IdDocumentoIdentidad;

            if (!IsPostBack)
            {
                byte.TryParse(TipoDocumentoIdentidad.Value?.ToString(), out byte idTipodocumento);
                if (idTipodocumento == 2)
                {
                    TelefonoOtro.Disabled = true;
                    TelefonoOtro.Value = IdDocumentoIdentidad;
                }
                else
                {
                    TipoCedulaOPasaporte.Disabled = true;
                    CedulaOPasaporte.Disabled = true;

                    TipoCedulaOPasaporte.Value = IdTipoDocumentoIdentidad.ToString();
                    CedulaOPasaporte.Value = IdDocumentoIdentidad;
                }
            }
        }

        protected void Page_Init(object sender, EventArgs e)
        {
            ListarMunicipios(!IsPostBack);
        }

        private void BindiarFormulario()
        {
            IdSolicitud.Value = Solicitud.IdSolicitud.ToString();

            //paso 1
            TipoDocumentoIdentidad.Value = Solicitud.TipoDocumentoIdentidad.ToString();
            DocumentoIdentidad.Value = Solicitud.DocumentoIdentidad;
            TipoCedulaOPasaporte.Value = Solicitud.TipoCedulaOPasaporte?.ToString();
            CedulaOPasaporte.Value = Solicitud.CedulaOPasaporte;
            Contrasena.Attributes.Remove("type");
            ConfirmarContrasena.Attributes.Remove("type");
            Contrasena.Attributes.Add("type", "text");
            Contrasena.Attributes.Add("value", "-");
            ConfirmarContrasena.Attributes.Add("type", "text");

            //paso 2
            Nombre1.Value = Solicitud.Nombre1.ToCapital();
            Nombre2.Value = Solicitud.Nombre2.ToCapital();
            Apellido1.Value = Solicitud.Apellido1.ToCapital();
            Apellido2.Value = Solicitud.Apellido2.ToCapital();
            CorreoElectronico.Value = Solicitud.CorreoElectronico.ToLower();
            TelefonoResidencial.Value = Solicitud.TelefonoResidencial;
            TelefonoMobil.Value = Solicitud.TelefonoMobil;
            TelefonoOtro.Value = Solicitud.TelefonoOtro;
            cboMunicipios.Value = Solicitud.IdMunicipio.ToString() ?? "";

            //paso 3
            Sector.Value = Solicitud.Sector;
            Barrio.Value = Solicitud.Barrio;
            Calle.Value = Solicitud.Calle;
            NumeroVivienda.Value = Solicitud.NumeroVivienda;
            Manzana.Value = Solicitud.Manzana;
            Apartamento.Value = Solicitud.Apartamento;
            Edificio.Value = Solicitud.Edificio;
            EntreLaCalle.Value = Solicitud.EntreLaCalle;
            YLaCalle.Value = Solicitud.YLaCalle;

            if (Solicitud.NNs.Count > 0)
            {
                CantidadNinos.Value = Solicitud.NNs.Count.ToString();
                CantidadNinos.Attributes.Add("readOnly", "true");
                divCantidadNNs.Style.Add("display", "none");
                //lblCantidadNNs.Visible = true;
                btnPaso4.Disabled = false;
                btnAgregarNino.Visible = true;

                if (Solicitud.NNs.Count > 1)
                {
                    lblCantidadNNs.InnerHtml = $"Está solicitando servicio para <b><u>{Solicitud.NNs.Count}</u></b>&nbsp;niños/as.";
                }
                else
                {
                    lblCantidadNNs.InnerHtml = $"Está solicitando servicio para <b><u>{Solicitud.NNs.Count}</u></b>&nbsp;niño/a.";
                }
            }

            btnAgregarNino.Attributes.Add("display", "block");
            btnAgregarGestante.Attributes.Add("display", "inline");

            if (Solicitud.Gestantes.Count > 0)
            {
                cboRespuestaGestante.Attributes.Add("readonly", "true");
                cboRespuestaGestante.SelectedIndex = 1;
                Page.ClientScript.RegisterStartupScript(this.GetType(), "HabilitarPanelGestante", "HabilitarPanelGestante()", true);
                //contenedor_form_gestante.Attributes.Add("display", "block !important");
                //contenedor_form_gestante.Visible = true;

                CantidadGestantes.Value = Solicitud.Gestantes.Count.ToString();
                CantidadGestantes.Attributes.Add("readonly", "true");
                divCantidadGestantes.Style.Add("display", "none");

                //lblCantidadGestantes.Visible = true;

                contenedor_form_info_gestante.Style.Add("display", "block !important");

                if (Solicitud.Gestantes.Count > 1)
                {
                    lblCantidadGestantes.InnerHtml = $"Está solicitando servicio para <b><u>{Solicitud.Gestantes.Count}</u></b>&nbsp;gestantes.";
                }
                else
                {
                    lblCantidadGestantes.InnerHtml = $"Está solicitando servicio para <b><u>{Solicitud.Gestantes.Count}</u></b>&nbsp;gestante.";
                }
            }

            contenedor_form_ninos.InnerHtml = "";
            contenedor_form_info_gestante.InnerHtml = "";

            Solicitud.NNs.ForEach(n =>
            {
                contenedor_form_ninos.InnerHtml += FormularioNinos(n.IdNN.ToString(), n.IdNN.ToString(), n.Nombre1, n.Nombre2, n.Apellido1, n.Apellido2, n.Sexo.ToString(), n.Edad.ToString(), n.FechaNacimiento.ToString("yyyy-MM-dd"));
            });

            Solicitud.Gestantes.ForEach(g =>
            {
                contenedor_form_info_gestante.InnerHtml += FormularioGestantes(g.IdGestante.ToString(), g.IdGestante.ToString(), g.Nombre1, g.Nombre2, g.Apellido1, g.Apellido2, g.Sexo.ToString(), g.FechaNacimiento.ToString("yyyy-MM-dd"), g.DocumentoIdentidad);
            });
        }

        private string FormularioNinos(string indice, string IdNN, string Nombre1, string Nombre2, string Apellido1, string Apellido2, string Sexo, string Edad, string FechaNacimiento)
        {
            string script = "";
            if (Sexo != "")
            {
                script = "<script>$(\"#SexoN" + IdNN + " option[value = " + Sexo + "]\").attr(\"selected\", \"selected\");</script>";
            }
            if (Edad != "")
            {
                script += "<script>$(\"#EdadN" + IdNN + " option[value = " + Edad + "]\").attr(\"selected\", \"selected\");</script>";
            }

            string formulario = "";

            formulario += "<div class=\"form_ninos\" id=\"form_nino" + indice + "\">";
            formulario += "<div class=\"container_data\">";
            formulario += "<h1 class=\"header-form-nino\">Datos del niño/a #<span id=\"titulo" + indice + "\">" + indice + "</span><button class=\"btn-close-form-ninos\" type=\"button\" onclick=\"Eliminar(" + IdNN + ");\">Eliminar</button></h1>";
            formulario += "<div class=\"form-row\"> ";
            formulario += "<div class=\"form-group col-md-3 required\">";
            formulario += "<label for=\"NombreN1\">Primer Nombre:</label> ";
            formulario += "<input type=\"text\" onkeyup=\"Actualizar(" + IdNN + ")\" class=\"form-control\" id=\"Nombre1N" + IdNN + "\" name=\"Nombre1N" + IdNN + "\" value=\"" + Nombre1 + "\" required=\"required\" /> ";
            formulario += "</div> ";
            formulario += " <div class=\"form-group col-md-3\"> ";
            formulario += "   <label for=\"NombreN2\">Segundo Nombre:</label>";
            formulario += "    <input type=\"text\" onkeyup=\"Actualizar(" + IdNN + ")\" class=\"form-control\" id=\"Nombre2N" + IdNN + "\" value=\"" + Nombre2 + "\" /> ";
            formulario += "   </div> ";
            formulario += "    <div class=\"form-group col-md-3 required\"> ";
            formulario += "      <label for=\"ApellidoN1\">Primer Apellido:</label> ";
            formulario += "      <input type=\"text\" onkeyup=\"Actualizar(" + IdNN + ")\" class=\"form-control\" id=\"Apellido1N" + IdNN + "\" value=\"" + Apellido1 + "\" required=\"required\" /> ";
            formulario += "   </div> ";
            formulario += "   <div class=\"form-group col-md-3\"> ";
            formulario += "      <label for=\"ApellidoN2\">Segundo Apellido:</label> ";
            formulario += "      <input type=\"text\" onkeyup=\"Actualizar(" + IdNN + ")\" class=\"form-control\" id=\"Apellido2N" + IdNN + "\" value=\"" + Apellido2 + "\" /> ";
            formulario += "   </div> ";
            formulario += " </div> ";
            formulario += " <div class=\"form-group row\"> ";
            formulario += "  <div class=\"form-group col-md-4 required\"> ";
            formulario += "   <label for=\"Sexo\">Sexo:</label> ";
            formulario += "    <select class=\"form-control\" id=\"SexoN" + IdNN + "\" name=\"SexoN" + IdNN + "\" onchange=\"Actualizar(" + IdNN + ")\" required=\"required\"> ";
            formulario += "      <option value=\"\" disabled=\"\" selected=\"\">Seleccione</option> ";
            formulario += "       <option value=\"1\">Masculino</option> ";
            formulario += "        <option value=\"2\">Femenino</option> ";
            formulario += "    </select> ";
            formulario += "  </div> ";
            formulario += "  <div class=\"form-group col-md-4 required\"> ";
            formulario += "     <label for=\"FechaNacimiento\">Fecha de Nacimiento:</label> ";
            formulario += "     <input type=\"date\" onkeyup=\"Actualizar(" + IdNN + ")\" class=\"form-control\" value=\"" + FechaNacimiento + "\" id=\"FechaNacimientoN" + IdNN + "\" name=\"FechaNacimientoN" + IdNN + "\" required=\"required\" /> ";
            formulario += "  </div> ";
            formulario += "  <div class=\"form-group col-md-4 required\"> ";
            formulario += "      <label for=\"Edad\">Edad:</label> ";
            formulario += "    <select class=\"form-control\" id=\"EdadN" + IdNN + "\" name=\"EdadN" + IdNN + "\" onchange=\"Actualizar(" + IdNN + "); VerificarEdad(" + IdNN + ")\" required=\"required\"> ";
            formulario += "      <option value=\"\" disabled=\"\" selected=\"\">Seleccione</option> ";
            formulario += "       <option value=\"0\">0 años</option> ";
            formulario += "        <option value=\"1\">1 año</option> ";
            formulario += "        <option value=\"2\">2 años</option> ";
            formulario += "        <option value=\"3\">3 años</option> ";
            formulario += "        <option value=\"4\">4 años</option> ";
            formulario += "    </select> ";
            formulario += "   </div> ";
            formulario += " </div> ";
            formulario += "</div > ";
            formulario += " </div>";
            formulario += script;
            //formulario += "<script> GuadarRegistroAlDescargar('"+IdNN+"', '"+Nombre1+"', '"+Nombre2+"', '"+Apellido1+"', '"+Apellido2+"', '"+Sexo+"', '"+FechaNacimiento+"')</script>";

            return formulario;
        }

        private string FormularioGestantes(string indice, string idGestante, string nombre1, string nombre2, string apellido1, string apellido2, string sexo, string fechaNacimiento, string documentoIdentidad)
        {
            string script = "";
            if (sexo != "")
            {
                script = "<script>$(\"#SexoG" + idGestante + " option[value = " + sexo + "]\").attr(\"selected\", \"selected\");</script>";
            }

            string formulario = "";

            formulario += "<div class=\"form_gestantes\" id=\"form_gestante" + indice + "\">";
            formulario += "<div class=\"container_data\">";
            formulario += "<h1 class=\"header-form-gestante\">Datos de la gestante #<span id=\"titulo" + indice + "\">" + indice + "</span><button class=\"btn-close-form-gestantes\" type=\"button\" onclick=\"EliminarGestante(" + idGestante + ");\">Eliminar</button></h1>";
            formulario += "<div class=\"form-row\"> ";
            formulario += "<div class=\"form-group col-md-3 required\">";
            formulario += "<label for=\"Nombre1G\">Primer Nombre:</label> ";
            formulario += "<input type=\"text\" onkeyup=\"ActualizarGestante(" + idGestante + ")\" class=\"form-control\" id=\"Nombre1G" + idGestante + "\" name=\"Nombre1G" + idGestante + "\" value=\"" + nombre1 + "\" required=\"required\" /> ";
            formulario += "</div> ";
            formulario += " <div class=\"form-group col-md-3\"> ";
            formulario += "   <label for=\"Nombre2G\">Segundo Nombre:</label>";
            formulario += "    <input type=\"text\" onkeyup=\"ActualizarGestante(" + idGestante + ")\" class=\"form-control\" id=\"Nombre2G" + idGestante + "\" value=\"" + nombre2 + "\" /> ";
            formulario += "   </div> ";
            formulario += "    <div class=\"form-group col-md-3 required\"> ";
            formulario += "      <label for=\"Apellido1G\">Primer Apellido:</label> ";
            formulario += "      <input type=\"text\" onkeyup=\"ActualizarGestante(" + idGestante + ")\" class=\"form-control\" id=\"Apellido1G" + idGestante + "\" value=\"" + apellido1 + "\" required=\"required\" /> ";
            formulario += "   </div> ";
            formulario += "   <div class=\"form-group col-md-3\"> ";
            formulario += "      <label for=\"Apellido2G\">Segundo Apellido:</label> ";
            formulario += "      <input type=\"text\" onkeyup=\"ActualizarGestante(" + idGestante + ")\" class=\"form-control\" id=\"Apellido2G" + idGestante + "\" value=\"" + apellido2 + "\" /> ";
            formulario += "   </div> ";
            formulario += " </div> ";
            formulario += " <div class=\"form-group row\"> ";
            formulario += "  <div class=\"form-group col-md-4 required\"> ";
            formulario += "   <label for=\"Sexo\">Sexo:</label> ";
            formulario += "    <select class=\"form-control\" id=\"SexoG" + idGestante + "\" name=\"SexoG" + idGestante + "\" onchange=\"ActualizarGestante(" + idGestante + ")\" required=\"required\" disabled=\"disabled\"> ";
            formulario += "      <option value=\"\" disabled=\"\" selected=\"\">Seleccione</option> ";
            formulario += "       <option value=\"1\">Masculino</option> ";
            formulario += "        <option value=\"2\">Femenino</option> ";
            formulario += "    </select> ";
            formulario += "  </div> ";
            formulario += "  <div class=\"form-group col-md-4 required\"> ";
            formulario += "     <label for=\"FechaNacimientoG\">Fecha de Nacimiento:</label> ";
            formulario += "     <input type=\"date\" onkeyup=\"Actualizar(" + idGestante + ")\" class=\"form-control\" value=\"" + fechaNacimiento + "\" id=\"FechaNacimientoG" + idGestante + "\" name=\"FechaNacimientoG" + idGestante + "\" required=\"required\" /> ";
            formulario += "  </div> ";
            formulario += "  <div class=\"form-group col-md-4 required\"> ";
            formulario += "      <label for=\"DocumentoIdentidadG\">Documento Identidad:</label> ";
            formulario += "      <input type=\"text\" onkeyup=\"ActualizarGestante(" + idGestante + ")\" class=\"form-control\" id=\"DocumentoIdentidadG" + idGestante + "\" value=\"" + documentoIdentidad + "\" /> ";
            formulario += "   </div> ";
            formulario += " </div> ";
            formulario += "</div > ";
            formulario += " </div>";
            formulario += script;

            return formulario;
        }

        private void ListarMunicipios(bool getData)
        {
            if (getData)
            {
                Municipios = ctrl.ListarMunicipios();
            }

            cboMunicipios.DataSource = Municipios;
            cboMunicipios.DataBind();
        }

        protected void btnEnviar_Click(object sender, EventArgs e)
        {
            //TODO: BINDIAR Y VALIDAR EL MODELO Solicitud con la nueva informaci'on
            BindiarSolicitud();

            Resultado r = new Resultado();
            if (Solicitud != null && Solicitud.IdSolicitud > 0)
            {
                r = ctrl.Modificar(Solicitud);
            }
            else
            {
                r = ctrl.Insertar(Solicitud);
            }

            if (r.TodoBien)
            {
                //TODO: REFIEREN A LA PAGINA QUE DICE QUE EST'A TODO BIEN Y LE MUESTRAN SU NUMERO DE SOLICITUD (r.ID) PARA FINES DE SEGUIMIENTO Y LE ENVIAN UN CORREO ELECTR'ONICO CON LA INFORMACI'ON
                Response.Redirect("~/Views/SolicitudesServicios/SolicitudEnviada.aspx");
            }
            else
            {
                modalContent.InnerHtml = r.strError.ToString();
                Modal.ShowOnPageLoad = true;
            }
        }

        private void BindiarSolicitud()
        {
            Solicitud = DataJson.Value.JsonToModel<INS_SolicitudesServicios>();
        }
    }
}
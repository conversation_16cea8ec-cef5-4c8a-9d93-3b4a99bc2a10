﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <sectionGroup name="devExpress">
      <section name="themes" type="DevExpress.Web.ThemesConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="compression" type="DevExpress.Web.CompressionConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="settings" type="DevExpress.Web.SettingsConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="errors" type="DevExpress.Web.ErrorsConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="resources" type="DevExpress.Web.ResourcesConfigurationSection, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <system.web>
    <customErrors mode="Off" />
    <compilation debug="true" targetFramework="4.6.1">
      <assemblies>
        <add assembly="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
      </assemblies>
    </compilation>
    <httpRuntime targetFramework="4.6.1" />
    <httpModules>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
    </httpModules>
    <httpHandlers>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" validate="false" />
    </httpHandlers>
  </system.web>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <handlers>
      <remove name="ChartImageHandler" />
      <add name="ChartImageHandler" preCondition="integratedMode" verb="GET,HEAD,POST" path="ChartImg.axd" type="System.Web.UI.DataVisualization.Charting.ChartHttpHandler, System.Web.DataVisualization, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" name="ASPxHttpHandlerModule" preCondition="integratedMode" />
      <add name="ASPxUploadProgressHandler" preCondition="integratedMode" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
      <add name="XafHttpHandler" preCondition="integratedMode" verb="*" path="DXX.axd" type="DevExpress.ExpressApp.Web.XafHttpHandler, DevExpress.ExpressApp.Web.v18.1, Version=********, culture=neutral, PublicKeyToken=b88d1754d700e49a" />
      <add name="ASPxHttpHandlerModuleXAF" preCondition="integratedMode" verb="GET,POST" path="DXXRD.axd" type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" name="ASPxWebDocumentViewerHandlerModule" preCondition="integratedMode" />
    </handlers>
    <defaultDocument>
      <files>
        <add value="MenuPrincipal.aspx" />
        <!--<remove value="iisstart.htm" />
        <remove value="index.html" />
        <remove value="index.htm" />
        <remove value="Default.asp" />
        <remove value="Default.htm" />-->
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
    <modules>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
      <add name="XafHttpModule" type="DevExpress.ExpressApp.Web.XafHttpModule, DevExpress.ExpressApp.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    </modules>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="500000000" />
      </requestFiltering>
    </security>
  </system.webServer>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
  <connectionStrings>
    <!--DB_PRUBA-->
    <!--<add name="MySqlConn" connectionString="server=inaipi.gob.do;User ID=inaipigob;Password=**********;Database=inaipigo_qec;default command timeout=300;" providerName="MySql.Data.MysqlClient"/>-->
    <add name="SqlCon" connectionString="Data Source=SERTEST001;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=600;Max Pool Size=3000;" providerName="System.Data.SqlClient" />
    <add name="SqlConn" connectionString="Data Source=SERTEST001;Initial Catalog=GESHUM;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=0;Max Pool Size=3000;" providerName="System.Data.SqlClient" />
    <!--DB_PRODUCCION-->
    <!--<add name="SqlConn" connectionString="Data Source=SERDB001;Initial Catalog=GESHUM;User ID=sa;Password=************;Connection Timeout=0;Max Pool Size=3000;" providerName="System.Data.SqlClient" />
    <add name="SqlCon"  connectionString="Data Source=SERDB001;Initial Catalog=QEC;   User ID=sa;Password=************;Connection Timeout=600;Max Pool Size=3000;" providerName="System.Data.SqlClient" />-->
    <!--DB_LOCAL-->
    <!--<add name="SqlConn" connectionString="Data Source=EST0101TIC0007;Initial Catalog=GESHUM;User ID=sa;Password=************;Connection Timeout=0;Max Pool Size=3000;" providerName="System.Data.SqlClient" />
    <add name="SqlCon"  connectionString="Data Source=EST0101TIC0007;Initial Catalog=QEC; User ID=sa;Password=************;Connection Timeout=600;Max Pool Size=3000;" providerName="System.Data.SqlClient" />-->
    <!--<add name="Excel03ConString" connectionString="Provider=Microsoft.Jet.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES'" />
    <add name="Excel07ConString" connectionString="Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES'" />-->
    <!---->
  </connectionStrings>
  <devExpress>
    <!--<bootstrap mode="Bootstrap4" iconSet="Embedded" />-->
    <themes enableThemesAssembly="true" styleSheetTheme="" theme="Default" customThemeAssemblies="" baseColor="" font="" />
    <compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true" />
    <settings doctypeMode="Xhtml" rightToLeft="false" embedRequiredClientLibraries="false" ieCompatibilityVersion="edge" accessibilityCompliant="false" checkReferencesToExternalScripts="false" protectControlState="true" bootstrapMode="Bootstrap3" />
    <!--bootstrapMode="Bootstrap3"-->
    <errors callbackErrorRedirectUrl="" />
    <resources>
      <add type="ThirdParty" />
      <add type="DevExtreme" />
    </resources>
  </devExpress>
  <!--<devExpress>
    <bootstrap mode="Bootstrap4" iconSet="Embedded" />
    <themes enableThemesAssembly="true" styleSheetTheme="" theme="" customThemeAssemblies="" baseColor="" font="" />
    <compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true" />
    <settings accessibilityCompliant="false"  doctypeMode="Html5" rightToLeft="false" checkReferencesToExternalScripts="true" protectControlState="true" ieCompatibilityVersion="edge" />
    --><!--bootstrapMode="Bootstrap3"--><!--
    <errors callbackErrorRedirectUrl="" />
  </devExpress>-->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30AD4FE6B2A6AEED" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AI.DependencyCollector" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.11.2.0" newVersion="2.11.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
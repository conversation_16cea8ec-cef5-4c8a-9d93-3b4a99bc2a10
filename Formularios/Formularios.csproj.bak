﻿<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.5\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.5\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.2.1.0\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.2.1.0\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{13EC0903-2E3C-4AD8-A913-45D3AAB12717}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Formularios</RootNamespace>
    <AssemblyName>Formularios</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TypeScriptToolsVersion>2.3</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ConvertirNumerosALetras">
      <HintPath>..\..\..\ConvertirNumerosALetras.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v18.1.Export, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Office.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v18.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.ASPxThemes.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.Resources.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=1.0.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.5\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <Content Include="images\bg-contrasena.png" />
    <Content Include="images\bg-portada1.jpg" />
    <Content Include="images\bg-portada1.png" />
    <Content Include="images\bg_page.png" />
    <Content Include="js\sweetalert2.js" />
    <Content Include="js\sweetalert2.min.js" />
    <Content Include="Scripts\jquery-3.0.0.slim.min.map" />
    <Content Include="Scripts\jquery-3.0.0.min.map" />
    <None Include="Properties\PublishProfiles\Produccion.pubxml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="css\solicitudesservicios.css" />
    <Content Include="Global.asax" />
    <Content Include="images\bg-contrasena.jpg" />
    <Content Include="images\bg_page.jpg" />
    <Content Include="images\favicon.ico" />
    <Content Include="images\logo.png" />
    <Content Include="js\solicitudesservicios.js" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\esm\popper-utils.js" />
    <Content Include="Scripts\esm\popper-utils.min.js" />
    <Content Include="Scripts\esm\popper.js" />
    <Content Include="Scripts\esm\popper.min.js" />
    <Content Include="Scripts\esm\popper.min.js.map" />
    <Content Include="Scripts\esm\popper.js.map" />
    <Content Include="Scripts\esm\popper-utils.min.js.map" />
    <Content Include="Scripts\esm\popper-utils.js.map" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\bootstrap-reboot.css.map" />
    <Content Include="Content\bootstrap-grid.min.css.map" />
    <Content Include="Content\bootstrap-grid.css.map" />
    <None Include="Scripts\jquery-3.0.0-vsdoc.js" />
    <Content Include="Scripts\jquery-3.0.0.js" />
    <Content Include="Scripts\jquery-3.0.0.min.js" />
    <Content Include="Scripts\jquery-3.0.0.slim.js" />
    <Content Include="Scripts\jquery-3.0.0.slim.min.js" />
    <Content Include="Scripts\popper-utils.js" />
    <Content Include="Scripts\popper-utils.min.js" />
    <Content Include="Scripts\popper.js" />
    <Content Include="Scripts\popper.min.js" />
    <Content Include="Scripts\umd\popper-utils.js" />
    <Content Include="Scripts\umd\popper-utils.min.js" />
    <Content Include="Scripts\umd\popper.js" />
    <Content Include="Scripts\umd\popper.min.js" />
    <Content Include="Views\SolicitudesServicios\ActualizacionClave.aspx" />
    <Content Include="Views\SolicitudesServicios\actualizar_contrasena.aspx" />
    <Content Include="Views\SolicitudesServicios\Condiciones.aspx" />
    <Content Include="Views\SolicitudesServicios\Mensaje.aspx" />
    <Content Include="Views\SolicitudesServicios\olvidocontrasena.aspx" />
    <Content Include="Views\SolicitudesServicios\recuperar_contrasena.aspx" />
    <Content Include="Views\SolicitudesServicios\SolicitudEnviada.aspx" />
    <Content Include="Views\SolicitudesServicios\Solicitudes.aspx" />
    <Content Include="Views\SolicitudesServicios\solicitudes_login.aspx" />
    <Content Include="Views\SolicitudesServicios\solicitudes_login_contrasena.aspx" />
    <Content Include="Web.config" />
    <Content Include="Scripts\umd\popper.min.js.map" />
    <Content Include="Scripts\umd\popper.js.map" />
    <Content Include="Scripts\umd\popper-utils.min.js.map" />
    <Content Include="Scripts\umd\popper-utils.js.map" />
    <Content Include="Scripts\README.md" />
    <Content Include="Scripts\popper.min.js.map" />
    <Content Include="Scripts\popper.js.map" />
    <Content Include="Scripts\popper-utils.min.js.map" />
    <Content Include="Scripts\popper-utils.js.map" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Views\SolicitudesServicios\ActualizacionClave.aspx.cs">
      <DependentUpon>ActualizacionClave.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\ActualizacionClave.aspx.designer.cs">
      <DependentUpon>ActualizacionClave.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\actualizar_contrasena.aspx.cs">
      <DependentUpon>actualizar_contrasena.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\actualizar_contrasena.aspx.designer.cs">
      <DependentUpon>actualizar_contrasena.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\Condiciones.aspx.cs">
      <DependentUpon>Condiciones.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\Condiciones.aspx.designer.cs">
      <DependentUpon>Condiciones.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\Mensaje.aspx.cs">
      <DependentUpon>Mensaje.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\Mensaje.aspx.designer.cs">
      <DependentUpon>Mensaje.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\olvidocontrasena.aspx.cs">
      <DependentUpon>olvidocontrasena.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\olvidocontrasena.aspx.designer.cs">
      <DependentUpon>olvidocontrasena.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\recuperar_contrasena.aspx.cs">
      <DependentUpon>recuperar_contrasena.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\recuperar_contrasena.aspx.designer.cs">
      <DependentUpon>recuperar_contrasena.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\SolicitudEnviada.aspx.cs">
      <DependentUpon>SolicitudEnviada.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\SolicitudEnviada.aspx.designer.cs">
      <DependentUpon>SolicitudEnviada.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\Solicitudes.aspx.cs">
      <DependentUpon>Solicitudes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\Solicitudes.aspx.designer.cs">
      <DependentUpon>Solicitudes.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\solicitudes_login.aspx.cs">
      <DependentUpon>solicitudes_login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\solicitudes_login.aspx.designer.cs">
      <DependentUpon>solicitudes_login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\solicitudes_login_contrasena.aspx.cs">
      <DependentUpon>solicitudes_login_contrasena.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Views\SolicitudesServicios\solicitudes_login_contrasena.aspx.designer.cs">
      <DependentUpon>solicitudes_login_contrasena.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="DevExpress\Web\Bootstrap\" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="Scripts\index.d.ts" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\INAIPI.Core\INAIPI.Core.csproj">
      <Project>{1921ea28-5fef-404f-96a5-1e590c659942}</Project>
      <Name>INAIPI.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\SI_INAIPI\SI_INAIPI.csproj">
      <Project>{de2d6d29-be5f-4ef2-b58c-b0161ee8f9cf}</Project>
      <Name>SI_INAIPI</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>46247</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:46247/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.2.1.0\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.2.1.0\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.5\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.5\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
/*
 Copyright (C) <PERSON> 2018
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */function a(a,b){if(1!==a.nodeType)return[];var c=getComputedStyle(a,null);return b?c[b]:c}function b(a){return'HTML'===a.nodeName?a:a.parentNode||a.host}function c(d){if(!d)return document.body;switch(d.nodeName){case'HTML':case'BODY':return d.ownerDocument.body;case'#document':return d.body;}var e=a(d),f=e.overflow,g=e.overflowX,h=e.overflowY;return /(auto|scroll|overlay)/.test(f+h+g)?d:c(b(d))}var d={},e=function(){var a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:'all';return(a=a.toString(),d.hasOwnProperty(a))?d[a]:('11'===a?d[a]=-1!==navigator.userAgent.indexOf('Trident'):'10'===a?d[a]=-1!==navigator.appVersion.indexOf('MSIE 10'):'all'===a?d[a]=-1!==navigator.userAgent.indexOf('Trident')||-1!==navigator.userAgent.indexOf('MSIE'):void 0,d.all=d.all||Object.keys(d).some(function(a){return d[a]}),d[a])};function f(b){if(!b)return document.documentElement;for(var c=e(10)?document.body:null,d=b.offsetParent;d===c&&b.nextElementSibling;)d=(b=b.nextElementSibling).offsetParent;var g=d&&d.nodeName;return g&&'BODY'!==g&&'HTML'!==g?-1!==['TD','TABLE'].indexOf(d.nodeName)&&'static'===a(d,'position')?f(d):d:b?b.ownerDocument.documentElement:document.documentElement}function g(a){var b=a.nodeName;return'BODY'!==b&&('HTML'===b||f(a.firstElementChild)===a)}function h(a){return null===a.parentNode?a:h(a.parentNode)}function j(a,b){if(!a||!a.nodeType||!b||!b.nodeType)return document.documentElement;var c=a.compareDocumentPosition(b)&Node.DOCUMENT_POSITION_FOLLOWING,d=c?a:b,e=c?b:a,i=document.createRange();i.setStart(d,0),i.setEnd(e,0);var k=i.commonAncestorContainer;if(a!==k&&b!==k||d.contains(e))return g(k)?k:f(k);var l=h(a);return l.host?j(l.host,b):j(a,h(b).host)}function k(a){var b=1<arguments.length&&arguments[1]!==void 0?arguments[1]:'top',c='top'===b?'scrollTop':'scrollLeft',d=a.nodeName;if('BODY'===d||'HTML'===d){var e=a.ownerDocument.documentElement,f=a.ownerDocument.scrollingElement||e;return f[c]}return a[c]}function l(a,b){var c=2<arguments.length&&void 0!==arguments[2]&&arguments[2],d=k(b,'top'),e=k(b,'left'),f=c?-1:1;return a.top+=d*f,a.bottom+=d*f,a.left+=e*f,a.right+=e*f,a}function m(a,b){var c='x'===b?'Left':'Top',d='Left'==c?'Right':'Bottom';return parseFloat(a['border'+c+'Width'],10)+parseFloat(a['border'+d+'Width'],10)}function n(a,b,c,d){return Math.max(b['offset'+a],b['scroll'+a],c['client'+a],c['offset'+a],c['scroll'+a],e(10)?c['offset'+a]+d['margin'+('Height'===a?'Top':'Left')]+d['margin'+('Height'===a?'Bottom':'Right')]:0)}function o(){var a=document.body,b=document.documentElement,c=e(10)&&getComputedStyle(b);return{height:n('Height',a,b,c),width:n('Width',a,b,c)}}var p=Object.assign||function(a){for(var b,c=1;c<arguments.length;c++)for(var d in b=arguments[c],b)Object.prototype.hasOwnProperty.call(b,d)&&(a[d]=b[d]);return a};function q(a){return p({},a,{right:a.left+a.width,bottom:a.top+a.height})}function r(b){var c={};try{if(e(10)){c=b.getBoundingClientRect();var d=k(b,'top'),f=k(b,'left');c.top+=d,c.left+=f,c.bottom+=d,c.right+=f}else c=b.getBoundingClientRect()}catch(a){}var g={left:c.left,top:c.top,width:c.right-c.left,height:c.bottom-c.top},h='HTML'===b.nodeName?o():{},i=h.width||b.clientWidth||g.right-g.left,j=h.height||b.clientHeight||g.bottom-g.top,l=b.offsetWidth-i,n=b.offsetHeight-j;if(l||n){var p=a(b);l-=m(p,'x'),n-=m(p,'y'),g.width-=l,g.height-=n}return q(g)}function s(b,d){var f=Math.max,g=2<arguments.length&&void 0!==arguments[2]&&arguments[2],h=e(10),i='HTML'===d.nodeName,j=r(b),k=r(d),m=c(b),n=a(d),o=parseFloat(n.borderTopWidth,10),p=parseFloat(n.borderLeftWidth,10);g&&'HTML'===d.nodeName&&(k.top=f(k.top,0),k.left=f(k.left,0));var s=q({top:j.top-k.top-o,left:j.left-k.left-p,width:j.width,height:j.height});if(s.marginTop=0,s.marginLeft=0,!h&&i){var t=parseFloat(n.marginTop,10),u=parseFloat(n.marginLeft,10);s.top-=o-t,s.bottom-=o-t,s.left-=p-u,s.right-=p-u,s.marginTop=t,s.marginLeft=u}return(h&&!g?d.contains(m):d===m&&'BODY'!==m.nodeName)&&(s=l(s,d)),s}function t(a){var b=Math.max,c=1<arguments.length&&void 0!==arguments[1]&&arguments[1],d=a.ownerDocument.documentElement,e=s(a,d),f=b(d.clientWidth,window.innerWidth||0),g=b(d.clientHeight,window.innerHeight||0),h=c?0:k(d),i=c?0:k(d,'left'),j={top:h-e.top+e.marginTop,left:i-e.left+e.marginLeft,width:f,height:g};return q(j)}function u(c){var d=c.nodeName;return'BODY'===d||'HTML'===d?!1:!('fixed'!==a(c,'position'))||u(b(c))}function v(b){if(!b||!b.parentElement||e())return document.documentElement;for(var c=b.parentElement;c&&'none'===a(c,'transform');)c=c.parentElement;return c||document.documentElement}function w(a,d,e,f){var g=4<arguments.length&&void 0!==arguments[4]&&arguments[4],h={top:0,left:0},i=g?v(a):j(a,d);if('viewport'===f)h=t(i,g);else{var k;'scrollParent'===f?(k=c(b(d)),'BODY'===k.nodeName&&(k=a.ownerDocument.documentElement)):'window'===f?k=a.ownerDocument.documentElement:k=f;var l=s(k,i,g);if('HTML'===k.nodeName&&!u(i)){var m=o(),n=m.height,p=m.width;h.top+=l.top-l.marginTop,h.bottom=n+l.top,h.left+=l.left-l.marginLeft,h.right=p+l.left}else h=l}return h.left+=e,h.top+=e,h.right-=e,h.bottom-=e,h}function x(a){var b=a.width,c=a.height;return b*c}function y(a,b,c,d,e){var f=5<arguments.length&&arguments[5]!==void 0?arguments[5]:0;if(-1===a.indexOf('auto'))return a;var g=w(c,d,f,e),h={top:{width:g.width,height:b.top-g.top},right:{width:g.right-b.right,height:g.height},bottom:{width:g.width,height:g.bottom-b.bottom},left:{width:b.left-g.left,height:g.height}},i=Object.keys(h).map(function(a){return p({key:a},h[a],{area:x(h[a])})}).sort(function(c,a){return a.area-c.area}),j=i.filter(function(a){var b=a.width,d=a.height;return b>=c.clientWidth&&d>=c.clientHeight}),k=0<j.length?j[0].key:i[0].key,l=a.split('-')[1];return k+(l?'-'+l:'')}for(var z='undefined'!=typeof window&&'undefined'!=typeof document,A=['Edge','Trident','Firefox'],B=0,C=0;C<A.length;C+=1)if(z&&0<=navigator.userAgent.indexOf(A[C])){B=1;break}function i(a){var b=!1;return function(){b||(b=!0,window.Promise.resolve().then(function(){b=!1,a()}))}}function D(a){var b=!1;return function(){b||(b=!0,setTimeout(function(){b=!1,a()},B))}}var E=z&&window.Promise,F=E?i:D;function G(a,b){return Array.prototype.find?a.find(b):a.filter(b)[0]}function H(a,b,c){if(Array.prototype.findIndex)return a.findIndex(function(a){return a[b]===c});var d=G(a,function(a){return a[b]===c});return a.indexOf(d)}function I(a){var b;if('HTML'===a.nodeName){var c=o(),d=c.width,e=c.height;b={width:d,height:e,left:0,top:0}}else b={width:a.offsetWidth,height:a.offsetHeight,left:a.offsetLeft,top:a.offsetTop};return q(b)}function J(a){var b=getComputedStyle(a),c=parseFloat(b.marginTop)+parseFloat(b.marginBottom),d=parseFloat(b.marginLeft)+parseFloat(b.marginRight),e={width:a.offsetWidth+d,height:a.offsetHeight+c};return e}function K(a){var b={left:'right',right:'left',bottom:'top',top:'bottom'};return a.replace(/left|right|bottom|top/g,function(a){return b[a]})}function L(a,b,c){c=c.split('-')[0];var d=J(a),e={width:d.width,height:d.height},f=-1!==['right','left'].indexOf(c),g=f?'top':'left',h=f?'left':'top',i=f?'height':'width',j=f?'width':'height';return e[g]=b[g]+b[i]/2-d[i]/2,e[h]=c===h?b[h]-d[j]:b[K(h)],e}function M(a,b,c){var d=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null,e=d?v(b):j(b,c);return s(c,e,d)}function N(a){for(var b=[!1,'ms','Webkit','Moz','O'],c=a.charAt(0).toUpperCase()+a.slice(1),d=0;d<b.length;d++){var e=b[d],f=e?''+e+c:a;if('undefined'!=typeof document.body.style[f])return f}return null}function O(a){return a&&'[object Function]'==={}.toString.call(a)}function P(a,b){return a.some(function(a){var c=a.name,d=a.enabled;return d&&c===b})}function Q(a,b,c){var d=G(a,function(a){var c=a.name;return c===b}),e=!!d&&a.some(function(a){return a.name===c&&a.enabled&&a.order<d.order});if(!e){var f='`'+b+'`';console.warn('`'+c+'`'+' modifier is required by '+f+' modifier in order to work, be sure to include it before '+f+'!')}return e}function R(a){return''!==a&&!isNaN(parseFloat(a))&&isFinite(a)}function S(a){var b=a.ownerDocument;return b?b.defaultView:window}function T(a,b){return S(a).removeEventListener('resize',b.updateBound),b.scrollParents.forEach(function(a){a.removeEventListener('scroll',b.updateBound)}),b.updateBound=null,b.scrollParents=[],b.scrollElement=null,b.eventsEnabled=!1,b}function U(a,b,c){var d=void 0===c?a:a.slice(0,H(a,'name',c));return d.forEach(function(a){a['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');var c=a['function']||a.fn;a.enabled&&O(c)&&(b.offsets.popper=q(b.offsets.popper),b.offsets.reference=q(b.offsets.reference),b=c(b,a))}),b}function V(a,b){Object.keys(b).forEach(function(c){var d=b[c];!1===d?a.removeAttribute(c):a.setAttribute(c,b[c])})}function W(a,b){Object.keys(b).forEach(function(c){var d='';-1!==['width','height','top','right','bottom','left'].indexOf(c)&&R(b[c])&&(d='px'),a.style[c]=b[c]+d})}function X(a,b,d,e){var f='BODY'===a.nodeName,g=f?a.ownerDocument.defaultView:a;g.addEventListener(b,d,{passive:!0}),f||X(c(g.parentNode),b,d,e),e.push(g)}function Y(a,b,d,e){d.updateBound=e,S(a).addEventListener('resize',d.updateBound,{passive:!0});var f=c(a);return X(f,'scroll',d.updateBound,d.scrollParents),d.scrollElement=f,d.eventsEnabled=!0,d}var Z={computeAutoPlacement:y,debounce:F,findIndex:H,getBordersSize:m,getBoundaries:w,getBoundingClientRect:r,getClientRect:q,getOffsetParent:f,getOffsetRect:I,getOffsetRectRelativeToArbitraryNode:s,getOuterSizes:J,getParentNode:b,getPopperOffsets:L,getReferenceOffsets:M,getScroll:k,getScrollParent:c,getStyleComputedProperty:a,getSupportedPropertyName:N,getWindowSizes:o,isFixed:u,isFunction:O,isModifierEnabled:P,isModifierRequired:Q,isNumeric:R,removeEventListeners:T,runModifiers:U,setAttributes:V,setStyles:W,setupEventListeners:Y};export{y as computeAutoPlacement,F as debounce,H as findIndex,m as getBordersSize,w as getBoundaries,r as getBoundingClientRect,q as getClientRect,f as getOffsetParent,I as getOffsetRect,s as getOffsetRectRelativeToArbitraryNode,J as getOuterSizes,b as getParentNode,L as getPopperOffsets,M as getReferenceOffsets,k as getScroll,c as getScrollParent,a as getStyleComputedProperty,N as getSupportedPropertyName,o as getWindowSizes,u as isFixed,O as isFunction,P as isModifierEnabled,Q as isModifierRequired,R as isNumeric,T as removeEventListeners,U as runModifiers,V as setAttributes,W as setStyles,Y as setupEventListeners};export default Z;
//# sourceMappingURL=popper-utils.min.js.map

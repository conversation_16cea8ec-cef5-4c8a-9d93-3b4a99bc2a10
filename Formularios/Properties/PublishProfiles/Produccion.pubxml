<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>http://SERIIS002:94/</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <MSDeployServiceURL>https://SERIIS002:8172/msdeploy.axd</MSDeployServiceURL>
    <DeployIisAppPath>Solicitudes_Servicios</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <UserName>inaipi\jairo.moreno</UserName>
    <_SavePWD>False</_SavePWD>
    <PublishDatabaseSettings>
      <Objects xmlns="">
        <ObjectGroup Name="SqlConn" Order="1" Enabled="False">
          <Destination Path="Data Source=********;Initial Catalog=GESHUM;Persist Security Info=True;User ID=sa;Password=************" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=SERTEST001;Initial Catalog=GESHUM;User ID=sa;Password=InaipiSqlTest-01;Max Pool Size=3000;Connect Timeout=0" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\SqlConn_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Data Source=SERTEST001;Initial Catalog=GESHUM;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=0;Max Pool Size=3000;" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="SqlCon" Order="2" Enabled="False">
          <Destination Path="Data Source=********;Initial Catalog=QEC;Persist Security Info=True;User ID=sa;Password=************" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=SERTEST001;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01;Max Pool Size=3000;Connect Timeout=600" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\SqlCon_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Data Source=SERTEST001;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=600;Max Pool Size=3000;" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
      </Objects>
    </PublishDatabaseSettings>
  </PropertyGroup>
  <ItemGroup>
    <MSDeployParameterValue Include="SqlConn-Web.config Connection String">
      <ParameterValue>Data Source=********;Initial Catalog=GESHUM;Persist Security Info=True;User ID=sa;Password=************</ParameterValue>
    </MSDeployParameterValue>
    <MSDeployParameterValue Include="SqlCon-Web.config Connection String">
      <ParameterValue>Data Source=********;Initial Catalog=QEC;Persist Security Info=True;User ID=sa;Password=************</ParameterValue>
    </MSDeployParameterValue>
  </ItemGroup>
</Project>
﻿body {
    margin-top: 0;
    font-family: Cabin, sans-serif;
    background-color: #0070cd;
    color: #5c666f;
    /*background-image: url(../images/bg-top-app.inaipi.png);
    background-position: top;
    background-repeat: no-repeat;
    background-size: contain;
    background-attachment: fixed;*/
    margin: 0;
    padding: 0;
}

.bg-body {
    background-image: url(../images/bg-contrasena.png);
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
}

a:link, a:visited {
    text-decoration: none;
}

.main {
    margin: 1.5rem auto;
    width: 70%;
    background-color: #fff;
    padding: 3rem 4rem 1rem 4rem;
    position: relative;
}

h3 {
    color: #FFA137;
    font-weight: bold;
    font-size: 1.4rem;
    width: 100%;
    /* margin-left: 10px; */
    margin-bottom: 0;
    margin-top: 1.5rem;
}

.solicitud_subtitulo {
    padding-bottom: 1rem;
}

    .solicitud_subtitulo span {
        padding-bottom: 1rem;
        color: #9A9A9A;
    }

.container_data {
    margin-right: 0;
    margin-left: 0;
    border-width: .2rem;
    background-color: #ECF0F4;
    padding: 20px;
    border-radius: 20px;
}

header {
    background-color: #fff;
    /*margin-left:-20px;
    margin-right:-20px;*/
}

.logo {
    padding-top: 1rem;
    padding-bottom: 1rem;
    width: 200px;
}

.ir-arriba {
    display: none;
    padding: 10px;
    background: #006FB5;
    font-size: 20px;
    color: #fff;
    cursor: pointer;
    position: fixed;
    bottom: 20px;
    right: 20px;
    border-radius: 3px;
}

.form_ninos {
    padding: 20px;
    background-color: #ECF0F4;
    margin-top: 10px;
    border-radius: 20px;
}

.header-form-nino {
    padding: 0;
    margin: -34px;
    border-bottom: 1px solid #ccc;
    font-size: 20px;
    margin-bottom: 40px;
    padding-bottom: 10px;
    padding-left: 10px;
    padding-top: 10px;
    background-color: #D1DAE4;
    position: relative;
    border-radius: 15px;
}

.btn-close-form-ninos {
    position: absolute;
    right: 15px;
    margin: 0;
    margin-top: -3px;
    padding: 5px;
    border: none;
    cursor: pointer;
    background: round;
    border-radius: 5px;
    background-color: red;
    color: white;
    font-size: medium;
}

.form_gestantes {
    padding: 20px;
    background-color: #ECF0F4;
    margin-top: 20px;
    border-radius: 20px;
}

.header-form-gestante {
    padding: 0;
    margin: -34px;
    border-bottom: 1px solid #ccc;
    font-size: 20px;
    margin-bottom: 40px;
    padding-bottom: 10px;
    padding-left: 10px;
    padding-top: 10px;
    background-color: #D1DAE4;
    position: relative;
    border-radius: 15px;
}

.btn-close-form-gestantes {
    position: absolute;
    right: 15px;
    margin: 0;
    margin-top: -3px;
    padding: 5px;
    border: none;
    cursor: pointer;
    background: round;
    border-radius: 5px;
    background-color: red;
    color: white;
    font-size: medium;
}

/*login*/
.tabla-login {
    margin-top: 15%;
    margin-right: 33%;
    margin-left: 33%;
    box-shadow: 0 0 5px #333;
}

.list-group-item {
    border: none;
}

    .list-group-item label {
        margin-top: 1rem;
    }

    .list-group-item .button.btn.btn-primary.btn-md {
        padding: .500rem 3rem;
    }

    .list-group-item .button.btn.btn-secondary.btn-md {
        padding: .500rem 3rem;
    }

.body-login {
    background-image: url(../images/bg_page.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #0070cd;
}

.body-login-contrasena {
    background-image: url(../images/bg-portada1.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #0070cd;
    /* overflow:hidden;*/
    padding: 0;
    background-position: 0px 100px;
}

input:invalid {
    border: 1px solid red;
}

input:valid {
    border: 1px solid green;
}

.form-group .required .control-label:after {
    color: #d00;
    content: "*";
    position: absolute;
    margin-left: 8px;
    top: 7px;
}

.OcultarPasos {
    visibility: hidden;
    height: 0;
}

h1 {
    margin: 0;
    padding: 0; /* margin-top:-30px;*/
}

h3 {
    margin: 0;
    padding: 0;
    margin-top: 10px;
}

#CantidadNinos {
    text-align: center;
}

#CantidadGestantes {
    text-align: center;
}

#HistorialPasos {
    position: absolute;
    right: 60px;
    top: 40px;
    /*border: 1px solid #ccc;*/
    padding: 10px;
    background-color: #fff;
    box-shadow: 2px 2px 2px #000000;
    background-color: #007bff;
    color: #fff;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

div.modal-header {
    text-align: left !important;
}

.form-group.required .control-label:before {
    color: red;
    content: "*";
    position: absolute;
    margin-left: -.6rem;
    font-family: 'Glyphicons Halflings';
    font-weight: normal;
    font-size: .8rem;
}

#TipoDocumentoIdentidad {
    height: 38px;
}

/*------------------------------------ Móviles en vertical ------------------------------------- */
@media (max-width: 480px) {
    .main {
        margin: .2rem auto;
        width: 90%;
        background-color: #fff;
        padding: .5rem;
    }

    .tabla-login {
        margin-top: 15%;
        margin-right: 10%;
        margin-left: 10%;
        box-shadow: 0 0 5px #333;
    }
}

.input-group-btn {
    position: absolute;
    right: 0;
    top: 0;
    width: 40px;
}

    .input-group-btn a {
        border-bottom: 1px solid #ccc;
        display: block;
        font-size: 14px;
        padding-left: 5px;
        padding-right: 5px;
    }

        .input-group-btn a:hover {
            background-color: #007bff;
            color: #fff;
        }

.btn-agregar{
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}

.classProvincia {
    position: relative;
}

#cantidad_gestantes {
    margin-bottom: 10px !important;
}
/* ------------------------------------ Tablets en horizonal y escritorios normales -------------------------------------- */
@media (min-width: 768px) and (max-width: 1199px) {
    .main {
        /*margin: .2rem auto;*/
        width: 100%;
        background-color: #fff;
        padding: .5rem;
    }

    .tabla-login {
        margin-top: 15%;
        margin-right: 20%;
        margin-left: 20%;
        box-shadow: 0 0 5px #333;
    }
}

/* ------------------------------------ Móviles en horizontal o tablets en vertical ------------------------------------- */
@media (min-width: 481px) and (max-width: 767px) {

    .main {
        margin: .2rem auto;
        width: 90%;
        background-color: #fff;
        padding: .5rem;
    }

    .tabla-login {
        margin-top: 15%;
        margin-right: 15%;
        margin-left: 15%;
        box-shadow: 0 0 5px #333;
    }
}

#btnAgregarCantidadNNs {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}

#divAlertCantidadNNs {
    display: none;
}
.validacion{
    border:1px solid red !important;
}
/*#contenedor_form_gestante{
    display:none;
}
*/
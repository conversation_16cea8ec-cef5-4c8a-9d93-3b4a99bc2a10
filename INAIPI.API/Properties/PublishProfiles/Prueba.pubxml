<?xml version="1.0" encoding="utf-8"?>
<!--
Este archivo se usa en el proceso de publicación y empaquetado del proyecto web. Para personalizar el comportamiento de este proceso,
edite el archivo MSBuild. Visite https://go.microsoft.com/fwlink/?LinkID=208121 para obtener más información. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>http://sigepi.inaipi.gob.do:99/</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <MSDeployServiceURL>https://********:8172/msdeploy.axd</MSDeployServiceURL>
    <DeployIisAppPath>INAIPIAPP_API_TEST</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <UserName>inaipi\Luis.mejia</UserName>
    <_SavePWD>False</_SavePWD>
    <PublishDatabaseSettings>
      <Objects xmlns="">
        <ObjectGroup Name="SqlCon" Order="2" Enabled="False">
          <Destination Path="Data Source=*********;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=*********;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01;Max Pool Size=3000;Connect Timeout=600" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\SqlCon_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Data Source=*********;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=600;Max Pool Size=3000;" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
      </Objects>
    </PublishDatabaseSettings>
  </PropertyGroup>
  <ItemGroup>
    <MSDeployParameterValue Include="SqlCon-Web.config Connection String">
      <ParameterValue>Data Source=*********;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01</ParameterValue>
    </MSDeployParameterValue>
  </ItemGroup>
</Project>
﻿<?xml version="1.0" encoding="utf-8"?>

<!-- For more information on using Web.config transformation visit https://go.microsoft.com/fwlink/?LinkId=301874 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    En el ejemplo siguiente, la transformación de "SetAttributes" cambiará el valor de
    "connectionString" para usar "ReleaseSQLServer" solo cuando el localizador "Match"
    encuentre un atributo "name" con el valor "MyDB".

    <connectionStrings>
      <add name="MyDB"
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True"
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
    <!--
      En el siguiente ejemplo, la transformación de "Replace" reemplazará toda la sección de
      <customErrors> del archivo Web.config.
      Tenga en cuenta que dado que solo hay una sesión customErrors en el nodo 
      <system.web>, no es necesario usar el atributo "xdt:Locator".

      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
  </system.web>
</configuration>

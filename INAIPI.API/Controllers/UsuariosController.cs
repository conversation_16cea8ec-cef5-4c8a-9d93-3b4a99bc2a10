﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;

namespace INAIPI.API.Controllers
{
    public class UsuariosController : ApiController
    {

        SIGEPI.Core.QEC.INAIPIAPP.APP_UsuariosCtrl ctrl = new SIGEPI.Core.QEC.INAIPIAPP.APP_UsuariosCtrl();

        //[HttpPost]
        //[Route("api/Usuarios/ObtenerToken")]
        //[AllowAnonymous]
        //[ResponseType(typeof(IHttpActionResult))]
        //public IHttpActionResult ObtenerToken([FromBody] SIGEPI.Common.Models.QEC.INAIPIAPP.APP_Usuarios usuarioInfo)
        //{
        //    if (!string.IsNullOrEmpty(usuarioInfo.Correo) && string.IsNullOrEmpty(usuarioInfo.Clave))
        //    {
        //        var rst = ctrl.Login(usuarioInfo);
        //        if (rst.TodoBien)
        //        {
        //            return Ok(new { Usuario = rst.Objeto, Token = GenerarTokenJWT(rst.Objeto) });
        //        }
        //        else
        //        {
        //            return BadRequest(rst.strError);
        //        }
        //    }
        //    else
        //    {
        //        return Ok(new { Usuario =  usuarioInfo, Token = GenerarTokenJWT(usuarioInfo) });
        //    }

        //}

        [HttpPost]
        [Route("api/Usuarios/Login")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult Login([FromBody] SIGEPI.Common.Models.QEC.INAIPIAPP.APP_Usuarios.APP_UsuariosLoginView usuarioInfo)
        {
            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.Authority}";
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";

            var rst = ctrl.Login(usuarioInfo, host);

            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest("El Correo o la Contraseña no son correctos");
            }
        }

        [HttpPost]
        [Route("api/Usuarios/Registrar")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult Registrar([FromBody] SIGEPI.Common.Models.QEC.INAIPIAPP.APP_Usuarios.APP_UsuariosRegistroView usuarioInfo)
        {
            var rstCorreo = ctrl.ValidarCorreo(usuarioInfo.Correo);

            if (!rstCorreo.TodoBien)
            {
                return BadRequest("Ya existe una cuenta con este correo");
            }

            var rst = ctrl.Registrar(usuarioInfo);

            if (rst.TodoBien)
            {
                return Ok();
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [Route("api/Usuarios/RecuperarContrasena")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult RecuperarContrasena([FromUri] string correo)
        {
            var rst = ctrl.RecuperarContrasena(correo);

            if (rst.TodoBien)
            {
                return Ok(rst.ID);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [Route("api/Usuarios/ValidarCodigoRecuperacion")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult ValidarCodigoRecuperacion([FromUri] string codigo)
        {
            var rst = ctrl.ValidarCodigoRecuperacion(codigo.ToUpper().Trim());

            if (rst.TodoBien)
            {
                return Ok(rst.ID);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpPost]
        [Route("api/Usuarios/CambioContrasena")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult CambioContrasena([FromBody] SIGEPI.Common.Models.QEC.INAIPIAPP.APP_Usuarios.APP_UsuariosCambioContrasenaView usuarioInfo)
        {
            if (!ModelState.IsValid)
            {
                string message = string.Join("; ", ModelState.Values
                                        .SelectMany(x => x.Errors)
                                        .Select(x => x.ErrorMessage));

                return BadRequest(message);
            }

            if (usuarioInfo.Clave != usuarioInfo.RepitaClave)
            {
                return BadRequest("Las contraseñas no coinciden");
            }

            var rst = ctrl.CambiarContrasena(usuarioInfo.Clave, usuarioInfo.Codigo.ToUpper());

            if (rst.TodoBien)
            {
                return Ok();
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }


        //GENERAMOS EL TOKEN CON LA INFORMACIÓN DEL USUARIO
        private string GenerarTokenJWT(SIGEPI.Common.Models.QEC.INAIPIAPP.APP_Usuarios usuarioInfo)
        {
            // RECUPERAMOS LAS VARIABLES DE CONFIGURACIÓN
            var _ClaveSecreta = ConfigurationManager.AppSettings["ClaveSecreta"];
            var _Issuer = ConfigurationManager.AppSettings["Issuer"];
            var _Audience = ConfigurationManager.AppSettings["Audience"];
            if (!Int32.TryParse(ConfigurationManager.AppSettings["Expires"], out int _Expires))
                _Expires = 720;


            // CREAMOS EL HEADER //
            var _symmetricSecurityKey = new SymmetricSecurityKey(
                    Encoding.UTF8.GetBytes(_ClaveSecreta));
            var _signingCredentials = new SigningCredentials(
                    _symmetricSecurityKey, SecurityAlgorithms.HmacSha256
                );
            var _Header = new JwtHeader(_signingCredentials);

            // CREAMOS LOS CLAIMS //
            var _Claims = new[] {
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim("IdUsuario", usuarioInfo?.IdUsuario.ToString()),
                new Claim(JwtRegisteredClaimNames.Email, usuarioInfo?.Correo),
                new Claim("CodigoUsuario", usuarioInfo.CodigoUsuario)
                //new Claim("nombre", usuarioInfo.Nombre),
                //new Claim("apellidos", usuarioInfo.Apellidos),
                
            };

            // CREAMOS EL PAYLOAD //
            var _Payload = new JwtPayload(
                    issuer: _Issuer,
                    audience: _Audience,
                    claims: _Claims,
                    notBefore: DateTime.UtcNow,
                    // Exipra en 30 dias
                    expires: DateTime.UtcNow.AddHours(_Expires)
                );

            // GENERAMOS EL TOKEN //
            var _Token = new JwtSecurityToken(
                    _Header,
                    _Payload
                );

            return new JwtSecurityTokenHandler().WriteToken(_Token);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Description;

namespace INAIPI.API.Controllers
{
    public class PostController : ApiController
    {
        SIGEPI.Core.QEC.INAIPIAPP.APP_PostCtrl ctrl = new SIGEPI.Core.QEC.INAIPIAPP.APP_PostCtrl();

        [HttpGet]
        [Route("api/Post/ListarPublicadas")]
        [Authorize]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult ListarPublicadas()
        {
            var rst = ctrl.Listar(IdEstatus: 2);

            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

    }
}

﻿using SIGEPI.Core.QEC.INAIPIAPP;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Description;

namespace INAIPI.API.Controllers
{
    public class NNsController : ApiController
    {
        APP_NNsCtrl ctrl = new APP_NNsCtrl();

        [HttpPost]
        [Route("api/NNs/CrearNN")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult CrearNN([FromBody] SIGEPI.Common.Models.QEC.INAIPIAPP.APP_NNs.APP_NNsCreateView model)
        {
            if (!ModelState.IsValid)
            {
                string message = string.Join("; ", ModelState.Values
                                        .SelectMany(x => x.Errors)
                                        .Select(x => x.ErrorMessage));

                return BadRequest(message);
            }

            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.Authority}";
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";

            model.PathPhysical = System.Web.Hosting.HostingEnvironment.MapPath("~/FileServer");

            var rst = ctrl.Crear(model, host);

            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpPut]
        [Route("api/NNs/ModificarNN")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult ModificarNN([FromBody] SIGEPI.Common.Models.QEC.INAIPIAPP.APP_NNs.APP_NNsUpdateView model)
        {
            if (!ModelState.IsValid)
            {
                string message = string.Join("; ", ModelState.Values
                                        .SelectMany(x => x.Errors)
                                        .Select(x => x.ErrorMessage));

                return BadRequest(message);
            }

            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.Authority}";
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";

            model.PathPhysical = System.Web.Hosting.HostingEnvironment.MapPath("~/FileServer");

            var rst = ctrl.Modificar(model, host);

            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [Route("api/NNs/ListarNNs")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult ListarNNs(int IdUsuario)
        {
            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.Authority}";
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";

            var rst = ctrl.ListarNNs(IdUsuario, host);

            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpPut]
        [Route("api/NNs/AnularNN")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult AnularNN([FromBody] SIGEPI.Common.Models.QEC.INAIPIAPP.APP_NNs.APP_NNsUpdateView model)
        {
            if (!ModelState.IsValid)
            {
                string message = string.Join("; ", ModelState.Values
                                        .SelectMany(x => x.Errors)
                                        .Select(x => x.ErrorMessage));

                return BadRequest(message);
            }

            var rst = ctrl.AnularNN(model);

            if (rst.TodoBien)
            {
                return Ok();
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }
    }
}

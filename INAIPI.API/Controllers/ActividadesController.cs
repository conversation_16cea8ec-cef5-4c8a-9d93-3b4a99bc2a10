﻿using SIGEPI.Common.Models.QEC.INAIPIAPP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Description;

namespace INAIPI.API.Controllers
{
    public class ActividadesController : ApiController
    {
        SIGEPI.Core.QEC.INAIPIAPP.APP_ActividadesCtrl ctrl = new SIGEPI.Core.QEC.INAIPIAPP.APP_ActividadesCtrl();
        private readonly int portNumber = 83;

        [HttpGet]
        [AllowAnonymous]
        [Route("api/Actividades/All")]
        public IHttpActionResult GetAll([FromUri] int pageNumber = 1, int pageSize = 100)
        {
            if (pageSize > 100)
            {
                return BadRequest($"La cantidad de registros no puede ser mayor a 100");
            }

            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}";
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";
            //var host = $"http://sigepi.inaipi.gob.do";

            var rst = ctrl.Listar(pageNumber, pageSize, host);
            if (rst.TodoBien)
            {
                return Ok(rst);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("api/Actividades/All")]
        public IHttpActionResult GetAll([FromUri] int idComponente, int idRangoEdad, int idUsuario = 0, int pageNumber = 1, int pageSize = 100)
        {
            if (pageSize > 100)
            {
                return BadRequest("La cantidad de registros no puede ser mayor a 20");
            }

            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}";
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";
            //var host = $"http://sigepi.inaipi.gob.do";

            var rst = ctrl.Listar(idComponente, idRangoEdad, idUsuario, pageNumber, pageSize, host);
            if (rst.TodoBien)
            {
                return Ok(rst);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Get(int id)
        {
            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}";
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";
            //var host = $"http://sigepi.inaipi.gob.do";

            var rst = ctrl.ConsultarPublicadas(id, host);
            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        [Route("api/Actividades/SearchActividades")]
        public IHttpActionResult Get(string nombreActividad, int? idRangoEdad, int pageNumber, int pageSize = 10)
        {
            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}";
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";
            //var host = $"http://sigepi.inaipi.gob.do";

            var rst = ctrl.BusquedaActividad(nombreActividad, idRangoEdad, pageNumber, pageSize, host);
            if (rst.TodoBien)
            {
                return Ok(rst);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Get([FromUri] int id, int IdUsuario)
        {
            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}";
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";
            //var host = $"http://sigepi.inaipi.gob.do";

            var rst = ctrl.Consultar(id, IdUsuario, host);
            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("api/Actividades/ListarComponentes")]
        public IHttpActionResult ListarComponentes()
        {
            var rst = ctrl.ListarComponentes();
            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("api/Actividades/ListarRangoEdades")]
        public IHttpActionResult ListarRangoEdades()
        {
            var rst = ctrl.ListarRangoEdades();
            if (rst.TodoBien)
            {
                return Ok(rst.Objeto);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpPost]
        [Route("api/Actividades/GuardarAccionesUsuarios")]
        [AllowAnonymous]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult GuardarAccionesUsuarios([FromBody] SIGEPI.Common.Models.QEC.INAIPIAPP.APP_ActividadUsuarioAcciones Acc)
        {
            var rst = ctrl.GuardarAcciones(Acc);

            if (rst.TodoBien)
            {
                return Ok();
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }
    }
}

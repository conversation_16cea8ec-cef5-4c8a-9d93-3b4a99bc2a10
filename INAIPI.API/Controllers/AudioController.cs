﻿using SIGEPI.Core.QEC.INAIPIAPP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;

namespace INAIPI.API.Controllers
{
    public class AudioController : ApiController
    {
        APP_AudiosCtrl Ctrl = new APP_AudiosCtrl();

        [HttpGet]
        [AllowAnonymous]
        [Route("api/Audio/AllAudios")]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult GetAllAudios([FromUri] int pageNumber = 1, int pageSize = 100)
        {
            if (pageSize > 100)
            {
                return BadRequest($"La cantidad de registros no puede ser mayor a 100");
            }

            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.Authority}";
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";

            var rst = Ctrl.ListarAudioApi(pageNumber, pageSize, host);
            if (rst.TodoBien)
            {
                return Ok(rst);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("api/Audio/AllAudios")]
        public IHttpActionResult GetAllAudios([FromUri] int idRangoEdad, int pageNumber = 1, int pageSize = 100)
        {
            if (pageSize > 100)
            {
                return BadRequest("La cantidad de registros no puede ser mayor a 20");
            }

            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.Authority}";
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";

            var rst = Ctrl.ListarAudioApi(idRangoEdad, pageNumber, pageSize, host);
            if (rst.TodoBien)
            {
                return Ok(rst);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("api/Audio/AllAudiosDetalles")]
        [ResponseType(typeof(IHttpActionResult))]
        public IHttpActionResult GetAllAudiosDetalles([FromUri] int IdAudio)
        {
            //TODO: Cuando se pase a produccion hay que poner el puerto dinamico
            //var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.Authority}";
            var host = $"{Request.RequestUri.Scheme}://{Request.RequestUri.DnsSafeHost}:83";

            var rst = Ctrl.ListarAudioDetalles(IdAudio, host);
            if (rst.TodoBien)
            {
                return Ok(rst);
            }
            else
            {
                return BadRequest(rst.strError);
            }
        }

    }
}
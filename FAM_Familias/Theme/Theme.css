﻿body {
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    line-height: 1.42857143;
    color: #333;
    background-color: #fff;
}


#topModulo {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    border-bottom: 1px solid #0159a2;
    height: 25px;
    background-color: #0070cd;
}

#headerModulo {
    position: absolute;
    left: 0;
    right: 0;
    top: 25px;
    border-bottom: 1px solid #ccc;
    height: 76px;
}

#toolModulo {
    position: absolute;
    left: 0;
    right: 0;
    border-bottom: 1px solid #999;
    padding-left: 10px;
    line-height: 43px;
    font-weight: bold;
    height: 45px;
    background-color:#fff;
}

#contenidoModulo {
    position: absolute;
    left: 0;
    right: 0;
    top: 101px;
    bottom: 0;
    background-color: #DFDFDF;
}

#logo {
    width: 200px;
    height: 75px;
    background-image: url(../Images/logo-ianipi.png);
    background-size: cover;
    /* border: 1px solid #ccc; */
    position: absolute;
}

#headerModuloTitulo {
    width: 400px;
    height: 75px;
    /* border: 1px solid #ccc; */
    margin-left: 200px;
    position: absolute;
}

#headerModuloTitulo h1 {
    font-family: 'Segoe UI', Helvetica, sans-serif;
    font-weight: normal;
    font-size: 26px;
    font-variant: small-caps;
    color: #0081c2;
    line-height: 22px;
    padding-left: 10px;
    margin: 0;
    margin-top: 10px;
    margin-bottom: 10px;
 }

#headerModuloTitulo h2 {
    font-family: 'Segoe UI', Helvetica, sans-serif;
    font-weight: 200;
    font-size: 22px;
    font-variant: small-caps;
    color: #ff9f35;
    line-height: 22px;
    padding-left: 10px;
    margin: 0;
    padding: 0;
    outline: 0;
    font-weight: inherit;
    font-style: inherit;
    font-size: 100%;
    font-family: inherit;
    vertical-align: baseline;
    display: block;
    margin-left: 10px;
}
#area-user {
    position: absolute;
    right: 10px;
    /*border: 1px solid #f00;*/ 
    top: 7px;
    bottom: 10px;
    width:360px;
}

#imgUsuario {
    /* border: 1px solid; */
    width: 60px;
    height: 60px;
    border-radius: 100px;
    position: absolute;
    top: 0px;
    background-image: url(../Images/img-user-default.png);
    background-image: url(file://serstr001/d/sistemas/Colaboradores/8650/ProfileImage/ProfileImage.JPG);
    background-position: center;
    background-size: contain;
    overflow: hidden;
    border: 3px solid #999;
    right:0;
}
#InfoDatosUsuario{
    position:absolute;
    right:70px;
}
#InfoDatosUsuario h3 {
    color: #0070cd;
    font-size: 14px;
    font-weight: bold;
    text-transform: capitalize;
    text-align:right;
}
#InfoDatosUsuario h3, #InfoDatosUsuario h4 {
    margin: 0;
    padding: 0;
    margin-top: 5px;
    font-size: 16px;
    margin-left: 70px;
    text-align:right;
}
#InfoDatosUsuario h4 {
    font-size:12px;
}
#toolModuloBotones {
    position: absolute;
    top: 0;
    /*border:1px solid #f00;*/
    left:300px;
    padding:0; margin:0;
}
.btn {
    font-size: 12px;
    /*margin:0; padding:0;*/
    border-radius:0;
}
.btn-success {
    background-color: #77bc1f;
    border: 1px solid #5B9218;
}
.btn-default {
    border: 1px solid #ccc;
}
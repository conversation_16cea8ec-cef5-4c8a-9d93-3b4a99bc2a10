﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterFAM_Familias.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="FAM_Familias.Default" %>
<%@ Register Assembly="DevExpress.Web.Bootstrap.v20.1, Version=20.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
   <div id="toolModulo">
        <span class="glyphicon glyphicon-th-list"></span>
        <span id="">LEVANTAMIENTOS REGISTRADOS</span>
        <div id="toolModuloBotones">
            <a href="Views/capturar/capturar.aspx" class="btn btb-default">Nuevo</a>
             <dx:BootstrapButton  runat="server" Text="NUEVO" AutoPostBack="false">
                 <SettingsBootstrap RenderOption="Success" />
                <ClientSideEvents Click="function(s,e) { window.open('borrar.aspx','_parent'); }" />
            </dx:BootstrapButton>
             <dx:BootstrapButton runat="server" Text="SALVAR" AutoPostBack="false">
                 <SettingsBootstrap RenderOption="Success" />
                <ClientSideEvents Click="function(s,e) { dxbsDemo.showToast('The button has been clicked.'); }" />
            </dx:BootstrapButton>
             <dx:BootstrapButton runat="server" Text="ACTUALIZAR" AutoPostBack="false">
                 <SettingsBootstrap RenderOption="Success" />
                <ClientSideEvents Click="function(s,e) { dxbsDemo.showToast('The button has been clicked.'); }" />
            </dx:BootstrapButton>
             <dx:BootstrapButton runat="server" Text="REGISTROS" AutoPostBack="false">
                 <SettingsBootstrap RenderOption="Default" />
                <ClientSideEvents Click="function(s,e) { dxbsDemo.showToast('The button has been clicked.'); }" />
            </dx:BootstrapButton>
             <dx:BootstrapButton runat="server" Text="ANULAR" AutoPostBack="false">
                 <SettingsBootstrap RenderOption="Danger" />
                <ClientSideEvents Click="function(s,e) { dxbsDemo.showToast('The button has been clicked.'); }" />
            </dx:BootstrapButton>
        </div>
    </div>
</asp:Content>

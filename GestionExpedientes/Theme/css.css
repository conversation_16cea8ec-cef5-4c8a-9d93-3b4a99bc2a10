body {
}


.form-control {
    font-size: 12px;
    margin: 0;
    height: 30px;
}


.form-group {
    margin: 0;
}


.input-group {
    margin: 0;
}

.panel-heading {
    line-height: 30px;
    padding: 0;
    padding-left: 10px;
}

.panel-body {
    padding-bottom: 0px;
}

.modal-header {
    background-color: #0070cd;
    color: #fff;
    line-height: 25px;
}

.modal-title {
    /*border:1px solid #fff;
	left:10px;
	margin:0;*/
}

.modal-header .close {
    color: #fff;
    /*border:1px solid #fff;*/
    margin: 0;
    padding: 0;
    position: absolute;
    right: 15px;
    top: 15px;
}

select {
    font-size: 12px;
    margin: 0;
    height: 45px;
    padding: 10px;
}

#contenedor-principal {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

#navegacion {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    border-bottom: 1px solid #0159a2;
    height: 25px;
    background-color: #0070cd;
}

#header-principal {
    position: absolute;
    left: 0;
    right: 0;
    top: 25px;
    border-bottom: 1px solid #ccc;
    height: 76px;
}

#header-modulo {
    position: absolute;
    left: 0;
    right: 0;
    top: 101px;
    border-bottom: 1px solid #999;
    padding-left: 10px;
    line-height: 50px;
    font-weight: bold;
}

.header-modulo-tool {
    position: absolute;
    left: 310px;
    top: -2px;
}

#contenido {
    position: absolute;
    left: 0;
    right: 0;
    top: 148px;
    bottom: 0;
    background-color: #DFDFDF;
    border-top: 1px solid #999;
}

.vista-modulo {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    border-top: 2px solid #ccc;
}

.data-grid {
    position: absolute;
    left: 5px;
    right: 5px;
    top: 5px;
    bottom: 5px;
    border: 1px solid #999;
    background: #fff;
    overflow: hidden;
}

.data-grid-header {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: 50px;
    border-bottom: 1px solid #ccc;
}

.data-grid-contenido {
    position: absolute;
    left: 0;
    top: 50px;
    right: 0;
    bottom: 0;
    overflow: none;
    /*border-top:1px solid #999;*/
}

.input-group.input-group-unstyled input.form-control {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.input-group-unstyled .input-group-addon {
    border-radius: 4px;
    border: 0px;
    background-color: transparent;
}

.data-grid-buscador {
    position: absolute;
    left: 10px;
    top: 7px;
    width: 500px;
}

#ContentPlaceHolder1_grdListadoExpedientes_DXMainTable th {
    border-bottom: 1px solid #999;
    border-right: 1px solid #999;
    text-align: center;
    height: 25px;
    background: linear-gradient(#E4E4E4,#ccc);
    font-size: 12px;
}

.dxgvHeader_MetropolisBlue {
    border-bottom: 1px solid #999;
    border-right: 1px solid #999;
    text-align: center;
    height: 25px;
    background: linear-gradient(#E4E4E4,#ccc);
    font-size: 12px;
    font-weight: bold;
}

#ContentPlaceHolder1_grdListadoExpedientes_DXMainTable tbody th {
    /*border-bottom:1px solid #999;
	border-right:1px solid #999;
	text-align:center;
	color:#000;
	font-size:12px;*/
    font-weight: bold;
}

.data-grid-contenido table tbody th a {
    /*color:#000;
	text-decoration:none;
	font-weight:normal;*/
}

.data-grid-contenido table tbody td {
    /*border-bottom:1px solid #ccc;
	border-right:1px solid #ccc;
	padding-left:10px;
	padding-right:10px;
	font-size:12px;
	line-height:24px;*/
}

tr.dxgvDataRow_MetropolisBlue:hover td {
    background-color: #ec971f;
    color: #fff;
    border-right: none;
    cursor: pointer;
    border-bottom: 1px solid #ec971f;
    border-right: 1px solid #ec971f;
}

tr.dxgvDataRow_MetropolisBlue:active td {
    background-color: #0070cd;
    color: #fff;
    border-right: none;
    cursor: pointer;
    border-bottom: none;
}

#ContentPlaceHolder1_grdListadoExpedientes_DXMainTable {
    width: 100%;
}

    #ContentPlaceHolder1_grdListadoExpedientes_DXMainTable tbody tr:nth-child(odd) {
        background: #fff;
    }

tr:nth-child(even) {
    background: #E4E4E4;
}

#vista1 {
    z-index: 1;
}

#vista2 {
    z-index: 2;
    background-color: #ccc;
    /*display:none;*/
}

.btn {
    font-weight: bold;
    border-radius: 0;
}

.formulario {
    position: absolute;
    left: 5px;
    right: 5px;
    top: 5px;
    bottom: 5px;
    border: 1px solid #999;
    background: #fff;
    overflow: hidden;
}

.header-formulario {
    /*border-bottom:1px solid #cccc;*/
    height: 39px;
    position: relative;
}

.content-formulario {
    position: absolute;
    left: 10px;
    top: 40px;
    right: 10px;
    bottom: 10px;
    padding-top: 10px;
    /*border:1px solid #cccc;*/
}

.header-formulario .nav-tabs li a {
    font-weight: bold;
    color: #000;
    border-radius: 0;
}

.logo {
    width: 200px;
    height: 75px;
    background-image: url('../Images/logo-ianipi.png');
    background-size: cover;
    /*border:1px solid #ccc;*/
    position: absolute;
}

.header-principal-titulo {
    width: 400px;
    height: 75px;
    /*border:1px solid #ccc;*/
    margin-left: 200px;
    position: absolute;
}

    .header-principal-titulo h1 {
        font-family: 'Segoe UI', Helvetica, sans-serif;
        font-weight: normal;
        font-size: 26px;
        font-variant: small-caps;
        color: #0081c2;
        line-height: 22px;
        padding-left: 10px;
        margin: 0;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .header-principal-titulo h2 {
        font-family: 'Segoe UI', Helvetica, sans-serif;
        font-weight: 200;
        font-size: 22px;
        font-variant: small-caps;
        color: #ff9f35;
        line-height: 22px;
        padding-left: 10px;
        margin: 0;
        padding: 0;
        outline: 0;
        font-weight: inherit;
        font-style: inherit;
        font-size: 100%;
        font-family: inherit;
        vertical-align: baseline;
        display: block;
        margin-left: 10px;
        /* text-shadow:0.5px 0.5px #000;*/
    }

.form-group {
    padding: 0;
    margin: 0;
    margin-bottom: 10px;
}

    .form-group input {
        padding: 0;
        margin: 0;
        padding-left: 10px;
    }

.input-group input {
    padding: 0;
    margin: 0;
    padding-left: 10px;
}

input, select, textarea {
    font-weight: normal;
    font-family: Arial;
    font-size: 11px;
    text-transform: uppercase;
}

.col-sm-2 {
    padding: 0;
    margin: 0;
    padding-left: 10px;
}

.col-sm-8 {
    padding: 0;
    margin: 0;
    padding-left: 10px;
}

.col-sm-6 {
    padding: 0;
    margin: 0;
    padding-left: 10px;
    padding-right: 10px;
}

.col-sm-10 {
    padding: 0;
    margin: 0;
    padding-left: 10px;
    padding-right: 10px;
}

.col-sm-12 {
    padding: 0;
    margin: 0;
    padding-left: 10px;
    padding-right: 10px;
}

.col-sm-4 {
    padding: 0;
    margin: 0;
    padding-left: 15px;
    padding-right: 0px;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.requerido {
    border: 1px solid #f00;
}

::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    /*border-radius: 10px;*/
    background-color: #CCC;
}

::-webkit-scrollbar-thumb {
    background-color: #FFF;
    border: 1px solid #999;
    box-shadow: 0px 0px 2px #999;
    background-color: #EBEBEB;
}

.btn-success {
    background-color: #0070cd;
    border: 1px solid #0159a2;
}

    .btn-success:hover {
        background-color: #0159a2;
        border: 1px solid #0159a2;
    }

.modal-body .data-grid {
    margin: -5px;
    border: none;
    margin-right: -6px;
}

input:focus, textarea:focus, select:focus {
    background-color: #CCE8FF;
    color: #000;
}

input[type=date]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    display: none;
}

th {
    padding-left: 5px;
    padding-right: 5px;
}

.dxgvPagerBottomPanel_MetropolisBlue {
    /*display:none;*/
    border-top: 1px solid #ccc;
}

.dxgvCSD {
    /*position:absolute;
	bottom:-50px;
	top:79px;
	z-index:10000;*/
}

#ContentPlaceHolder1_grdListadoExpedientes_DXSE_I {
    padding: 5px;
    border-radius: 10px;
}
/*
.panel{
	border:1px solid #999;
}
.panel-body{
	background-color:#EAEAEA;
	border-top:1px solid #999;
}
.panel-heading{
	background: linear-gradient(#E4E4E4,#ccc);
	
}
*/
#ContentPlaceHolder1_cbPrincipal_grdListProcedencias_DXSearchPanel {
    display: none;
}

.btn {
    cursor: pointer;
}

i.glyphicon-search {
    cursor: pointer;
}

.red {
    background-color: #f00;
}

.activo {
    background-color: #0070cd;
    border: 1px solid #0159a2;
    color: #fff;
}

.panel-primary > .panel-heading {
    color: #fff;
    background-color: #055ba5;
    border-color: #337ab7;
}

#ContentPlaceHolder1_cbPrincipal_BootstrapButton3 {
    position: absolute;
    right: 0;
    top: 0;
}

#ContentPlaceHolder1_cbPrincipal_Procedencia_I {
    margin: 0;
    padding: 0;
    border: 2px solid #f00;
}

input#ContentPlaceHolder1_cbPrincipal_txtProcedencia_I {
    border-radius: 0;
    height: 34px;
    user-select: none;
}

input#ContentPlaceHolder1_cbPrincipal_txtEntregadoPor_I {
    border-radius: 0;
    height: 34px;
}

input#ContentPlaceHolder1_cbPrincipal_txtBeneficiario_I {
    border-radius: 0;
    height: 34px;
   
}

input#ContentPlaceHolder1_cbPrincipal_selectEstatus_I {
    border-radius: 0;
    height: 34px;
}

input#ContentPlaceHolder1_cbPrincipal_selectDestinatario_I {
    border-radius: 0;
    height: 34px;
}

body {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.input-group input#ContentPlaceHolder1_cbPrincipal_FechaEmision_I {
    border-radius: 0;
    height: 34px;
}

input#ContentPlaceHolder1_cbPrincipal_txtNumeroDocumento_I {
    border-radius: 0;
    height: 34px;
}

input#ContentPlaceHolder1_cbPrincipal_txtTipoDocumento_I {
    border-radius: 0;
    height: 34px;
}

input#ContentPlaceHolder1_cbPrincipal_FechaEntrega_I {
    border-radius: 0;
    height: 34px;
}

input#ContentPlaceHolder1_cbPrincipal_HoraEntrega_I {
    border-radius: 0;
    height: 34px;
}

input#ContentPlaceHolder1_cbPrincipal_txtTipoPago_I {
    border-radius: 0;
    height: 34px;
}

.select {
    border-radius: 0;
    height: 34px;
}

#ContentPlaceHolder1_cbPrincipal_btnCloseFormulario {
    background-color: transparent;
}

.input-group-addon {
    font-weight: bold;
    text-transform: uppercase;
    font-size: 11px;
    font-family: Arial;
}

ul li a {
    text-transform: uppercase;
    font-size: 12px;
    font-family: Arial;
    font-weight: normal;
}

ul.dxbs-listbox li a:hover {
    background-color: #0070cd;
    color: #fff;
}

#ContentPlaceHolder1_cbPrincipal_btnCloseFormulario {
    position: absolute;
    right: -10px;
    top: -8px;
    z-index: 100;
}

.panel-data-registro p {
    text-transform: uppercase;
}

td {
    text-transform: uppercase;
}

.btn {
    font-size: 12px;
}

.glyphicon-search {
    font-size: 17px;
}

.dropdown-toggle {
    font-size: 13px;
}

.dxeEditArea_MetropolisBlue {
    padding: 10px;
}

#area-user {
    position: absolute;
    right: 10px;
    /*border:1px solid #f00;*/
    width: auto;
    top: 7px;
    bottom: 10px;
}

#ContentPlaceHolder1_cbPrincipal_BootstrapButton1 {
    border: none;
    color: #337AB7;
}

#ContentPlaceHolder1_cbPrincipal_grdTiposDocumentos_DXSE_I {
    width: 238px;
    padding: 5px;
}

#ContentPlaceHolder1_cbPrincipal_grdConsultarExpedientes_DXSE_I {
    padding: 5px;
}

table#ContentPlaceHolder1_cbPrincipal_grdTiposDocumentos tbody tr td:focus {
    background-color: #ec971f;
    color
}

#ContentPlaceHolder1_cbPrincipal_grdTiposDocumentos_DXMainTable tbody tr td.dxgv:focus {
    background-color: none;
}

#ContentPlaceHolder1_cbPrincipal_grdTiposDocumentos_DXMainTable tbody tr:nth-child(odd) {
    background-color: none;
}

table tr:nth-child(odd) {
    background-color: none;
}

table tr:nth-child(even) {
    background-color: none;
}

tr.dxgvDataRow_MetropolisBlue td:focus {
}

#ContentPlaceHolder1_cbPrincipal_grdConsultarExpedientes_DXFooterRow td {
    border-top: 2px solid #ccc;
}

#ContentPlaceHolder1_cbPrincipal_grdConsultarExpedientes_DXSE {
    margin-left: 450px;
}

#ContentPlaceHolder1_cbPrincipal_grdConsultarExpedientes_DXMainTable {
}

.dxgvPHEC {
    font-weight: bold;
}

#ContentPlaceHolder1_cbPrincipal_grdListadoExpedientesRecibidos_DXSE_I {
    padding: 5px;
}

#ContentPlaceHolder1_cbPrincipal_btnNuevo {
    background-color: #77bc1f;
    border: 1px solid #5B9218;
}

#ContentPlaceHolder1_cbPrincipal_btnActualizar {
    background-color: #77bc1f;
    border: 1px solid #5B9218;
}

#ContentPlaceHolder1_cbPrincipal_grdListadoExpedientes1_DXSE_I {
    padding: 5px;
}

#ContentPlaceHolder1_cbPrincipal_grdListEnviados_DXSE_I {
    padding: 5px;
}

#ContentPlaceHolder1_cbPrincipal_ModalActualizarRegistro_TPCFm1_ctl00 {
    background-color: #e2231a;
    border: 1px solid #9C1A12;
    color: #fff;
}

#ContentPlaceHolder1_cbPrincipal_btnSalvar {
    background-color: #77bc1f;
    border: 1px solid #5B9218;
}

#ContentPlaceHolder1_cbPrincipal_ModalSalvarRegistro_TPCFm1_ctl00 {
    background-color: #e2231a;
    border: 1px solid #9C1A12;
    color: #fff;
}

#ContentPlaceHolder1_cbPrincipal_btnEnviar {
    background-color: #77bc1f;
    border: 1px solid #5B9218;
}

#area-user-info {
    position: absolute;
   /* border: 1px solid #ccc;*/
    right: 240px;
    width: 300px;
    height:60px;
    padding-top:7px;
}
   
    #area-user-info h3, #area-user-info h4 {
        margin: 0;
        padding: 0;
        margin-top: 5px;
        font-size: 16px;
        margin-left: 70px;
    }
    #area-user-info h3 {
      
        color: #0070cd;
        font-size: 14px;
        font-weight: bold;
        text-transform:capitalize;
    }
    #area-user-info h4 {
        color: #0070cd;
        font-size: 12px;
        font-weight: normal;
        color:#000;

    }
#imgUsuario {
    /*border: 1px solid;*/
    width: 60px;
    height: 60px;
    border-radius: 100px;
    position: absolute;
    top: 0px;
    background-image: url('../Images/img-user-default.png');
    background-position:center;
    background-size:contain;
}
.nav-tabs {
    background-color: #E4E4E4;
    border-bottom: 1px solid #C0C0C0;
}
    .nav-tabs li {
        border-left: 1px solid #C0C0C0;
        border-right: 1px solid #C0C0C0;
    }
    .nav-tabs li.active {
        /*border-left: 1px solid #C0C0C0;*/
        border-right: 1px solid #C0C0C0;
        border-left:none;
    }
#ContentPlaceHolder1_cbPrincipal_Archivo {
    /*padding: 0;
    margin: 0;
    height: 25px;*/
    height:35px;
    padding:2px;
}
#ContentPlaceHolder1_cbPrincipal_Archivo_UploadInputs {
    padding: 2px;
    margin: 0;
    height: 40px;
    margin-top:-14px;
}
#ContentPlaceHolder1_cbPrincipal_Archivo_FI0{
    padding:2px;    
}
#ContentPlaceHolder1_cbPrincipal_cbDocumentos_ModalConfirmacion{
    z-index:10000;
}

    /*
.dxgvControl_MetropolisBlue {
    border-left:none;
    border-top:none;
    border-right:none;
}
.dxgvCSD {
    border-left: 1px solid #C0C0C0;
    border-right: 1px solid #C0C0C0;
}
#ContentPlaceHolder1_cbPrincipal_grdConsultarExpedientes_DXHeaderTable {
    border-left: 1px solid #C0C0C0;
    border-right: 1px solid #C0C0C0;
}*/
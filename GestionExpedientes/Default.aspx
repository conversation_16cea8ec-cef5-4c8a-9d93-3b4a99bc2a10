﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterGestionExpedientes.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="GestionExpedientes.Default" %>

<%@ Register Assembly="DevExpress.Web.Bootstrap.v20.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">


    <script type="text/javascript">
                
        function OncustomButtonClick(s, e) {
            //var sender = s;
            //var event = e;
            //var key = sender.GetRowKey(e.visibleIndex);

            //alert(key.);
            e.processOnServer = false;
            switch (e.buttonID) {
                case 'btnEliminar':
                    //$("#ContentPlaceHolder1_cbPrincipal_cbDocumentos_ModalConfirmacion").modal('show');

                    var r = confirm("Quiere eliminar el archivo!");
                    if (r == true) {
                        cbDocumentos.PerformCallback('ELIMINAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex));
                    }
                    break;

                case 'btnVisualizar':
                    doProcessClick = false;
                    //alert('visualizar');
                    $("#PreviewDocumento").show();
                    $("#PreviewDocumentoBg").show();
                    //cbDocumentos.PerformCallback('VISUALIZAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex));
                    
                    e.processOnServer = true;
                    break;

                case 'btnDescargar':
                    //alert('visualizar');
                    //cbDocumentos.PerformCallback('DESCARGAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex));
                    e.processOnServer = true;
                    //e.processOnServer = true;
                    break;
            }
        }
        function EliminarArchivo(s, e) {
            cbDocumentos.PerformCallback('ELIMINAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex));
            /*swal({
                title: "¡CONFIRMACIÓN!",
                text: "¿ESTÁ SEGURO DE QUERER ELIMINAR ESTE ARCHIVO?",
                type: 'question',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'SI',
                cancelButtonText: 'NO',
                confirmButtonClass: 'btn btn-success',
                cancelButtonClass: 'btn btn-danger',
                buttonsStyling: true,
                allowOutsideClick: false,
                allowEscapeKey: false
            }).then(function () {
                cbDocumentos.PerformCallback('ELIMINAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex));
            })*/
        }

        function onUploadControlFileUploadComplete(s, e) {
            e.processOnServer = false;
            if (e.isValid) {
                cbDocumentos.PerformCallback('SUBIR_ARCHIVO');
            } else {
                //setElementVisible("uploadedImage", e.isValid);
            }
        }


        function FormatearCantidad(selector) {
            var number = numeral($(selector).val());

            number.format();
            // '1,000'

            numeral.defaultFormat('0,0.00');

            $(selector).val(number.format());
        }
        function LimpiarNumero(selector) {
            var numero = $(selector).val();
            numero_limpio = numero.replace(/\,/g, '');
            return numero_limpio;
        }
        function SeleccionarTipoDocumento() {
            //alert(txtTipoDocumento.GetValue());
            if (txtTipoDocumento.GetValue() === '2') {
                txtBeneficiario.SetEnabled(false);  
                txtTipoPago.SetEnabled(false); 
            }
            if (txtTipoDocumento.GetValue() === '1') {
                txtBeneficiario.SetEnabled(true);  
                txtTipoPago.SetEnabled(true); 
            }
            
        }
       

        function ExecuteSelectors() {
            //alert($("#ContentPlaceHolder1_cbPrincipal_verArchivo").val());
            //var idExpediente = $("#ContentPlaceHolder1_idExpediente").val();
            //alert(idExpediente);
            //if (idExpediente != '') {
            //    alert("no es igual a vacio");
            //}

            $("#PreviewDocumento").hide();
            //SeleccionarTipoDocumento();
            $("#btnCerrarVentanaPreviewDocumento").click(function () {
                $("#ContentPlaceHolder1_cbPrincipal_PreviewDocumento").hide();
                $("#PreviewDocumentoBg").hide();
            });
            /*  $(function() {
                $(document).keydown(function(e){
                 var code = (e.keyCode ? e.keyCode : e.which);
                 if(code == 116) {
                  e.preventDefault();
                  jConfirm('¿Deseas recargar la página?', 'Confirmación', function(r) {
                      if(r)
                       location.reload();
                  });
                 }
                });
               });*/
            //FormatearCantidad("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I");
            //FormatearCantidad("#ContentPlaceHolder1_cbPrincipal_Impuesto_I");
            // FormatearCantidad("#ContentPlaceHolder1_cbPrincipal_Monto_I"); 



            /*$("#ContentPlaceHolder1_cbPrincipal_grdListadoExpedientes1_DXSE_I").addClass("form-control");
            $("#ContentPlaceHolder1_cbPrincipal_grdListadoExpedientes1_DXSE_I").attr("autocomplete", "off");*/
            // $("#ContentPlaceHolder1_cbPrincipal_ASPxGridView1_DXSE_I").addClass("form-control");


            $(function () {
                $(document).bind("contextmenu", function (e) {
                    return false;
                });
            });
            validarTipoDocumento = $("#ContentPlaceHolder1_cbPrincipal_txtTipoDocumento_I").val();

            if (validarTipoDocumento === 'REQUISICION DE COMPRAS') {
                $("#ContentPlaceHolder1_cbPrincipal_NCF_I").prop('disabled', true);
                $("#ContentPlaceHolder1_cbPrincipal_txtBeneficiario_I").prop('disabled', true);
                $("#ContentPlaceHolder1_cbPrincipal_BeneficiarioCorreo_I").prop('disabled', true);
                $("#ContentPlaceHolder1_cbPrincipal_txtTipoPago_I").prop('disabled', true);
                $("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I").prop('disabled', true);
                $("#ContentPlaceHolder1_cbPrincipal_Impuesto_I").prop('disabled', true);
                $("#ContentPlaceHolder1_cbPrincipal_cbTotalCxP_Monto_I").prop('disabled', true);

            }
            if (validarTipoDocumento === 'FACTURA') {
                $("#divNumeroRequerimiento").show();
            }

            $("#ContentPlaceHolder1_cbPrincipal_txtTipoDocumento").click(function () {
                validarTipoDocumento = $("#ContentPlaceHolder1_cbPrincipal_txtTipoDocumento_I").val();

                if (validarTipoDocumento === 'FACTURA') {
                    $("#ContentPlaceHolder1_cbPrincipal_NCF_I").prop('disabled', false);
                    $("#ContentPlaceHolder1_cbPrincipal_txtBeneficiario_I").prop('disabled', false);
                    $("#ContentPlaceHolder1_cbPrincipal_BeneficiarioCorreo_I").prop('disabled', false);
                    $("#ContentPlaceHolder1_cbPrincipal_txtTipoPago_I").prop('disabled', false);
                    $("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I").prop('disabled', false);
                    $("#ContentPlaceHolder1_cbPrincipal_Impuesto_I").prop('disabled', false);
                    $("#ContentPlaceHolder1_cbPrincipal_cbTotalCxP_Monto_I").prop('disabled', false);
                    $("#divNumeroRequerimiento").show();
                }
            });
            

            $("#ContentPlaceHolder1_cbPrincipal_txtTipoDocumento").click(function () {
                validarTipoDocumento = $("#ContentPlaceHolder1_cbPrincipal_txtTipoDocumento_I").val();

                if (validarTipoDocumento === 'REQUISICION DE COMPRAS') {
                    $("#ContentPlaceHolder1_cbPrincipal_NCF_I").prop('disabled', true);
                    $("#ContentPlaceHolder1_cbPrincipal_txtBeneficiario_I").prop('disabled', true);
                    $("#ContentPlaceHolder1_cbPrincipal_BeneficiarioCorreo_I").prop('disabled', true);
                    $("#ContentPlaceHolder1_cbPrincipal_txtTipoPago_I").prop('disabled', true);
                    $("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I").prop('disabled', true);
                    $("#ContentPlaceHolder1_cbPrincipal_Impuesto_I").prop('disabled', true);
                    $("#ContentPlaceHolder1_cbPrincipal_cbTotalCxP_Monto_I").prop('disabled', true);
                    $("#divNumeroRequerimiento").hide();
                }
            });

            $("#ContentPlaceHolder1_cbPrincipal_Monto_I").attr("readonly", "true");

            /*
            $("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I").on('input', function () {
                this.value = this.value.replace(/[^0-9.,]/g, '');
            });
            $("#ContentPlaceHolder1_cbPrincipal_Impuesto_I").on('input', function () {
                this.value = this.value.replace(/[^0-9.,]/g, '');
            })
            */
            /*
            $("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I").change(function () {
                CalcularMonto();
            });
            $("#ContentPlaceHolder1_cbPrincipal_Impuesto_I").change(function () {
                CalcularMonto();
            });
            */
            $("#ContentPlaceHolder1_cbPrincipal_HoraEntrega_I").keyup(function () {

                if ($(this).val().length === 2) {
                    $(this).val($(this).val() + ':');
                }
                if ($(this).val().length === 5) {
                    $(this).val($(this).val() + ':00');
                }

            });
        }
        function CalcularMonto() {
            /*FormatearCantidad("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I");
            FormatearCantidad("#ContentPlaceHolder1_cbPrincipal_Impuesto_I");
            FormatearCantidad("#ContentPlaceHolder1_cbPrincipal_Monto_I");  */

            //var valorFactura = Number(LimpiarNumero("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I"));
            //var impuesto = Number(LimpiarNumero("#ContentPlaceHolder1_cbPrincipal_Impuesto_I"));

            var valorFactura = Number($("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I").val());
            var impuesto = Number($("#ContentPlaceHolder1_cbPrincipal_Impuesto_I").val());

            //var valorImpuesto = valorFactura * impuesto; 
            console.log(valorFactura + impuesto);
            $("#ContentPlaceHolder1_cbPrincipal_Monto_I").val(valorFactura + impuesto);
            /*
            var number = numeral($("#ContentPlaceHolder1_cbPrincipal_Monto_I").val());
            number.format();
            numeral.defaultFormat('0,0.00');
            $("#ContentPlaceHolder1_cbPrincipal_Monto_I").val(number.format());*/
        }
        function OnInit(s, e) {
           
            ExecuteSelectors();
        }
        function OnEndCallback(s, e) {
            //$("#PreviewDocumento").show();
            
            ExecuteSelectors();
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <input runat="server" style="position: absolute; z-index: 1000; bottom: 10px" type="hidden" id="idExpediente" name="idExpediente">

    <dx:ASPxCallbackPanel ID="cbPrincipal" runat="server" ClientInstanceName="cbPrincipal" OnCallback="cbPrincipal_Callback">

        <ClientSideEvents Init="OnInit" EndCallback="OnEndCallback" />
        <PanelCollection>
            <dx:PanelContent>
                <div id="PreviewDocumento" visible="false" style="position: absolute; left: 0; right: 0; top: 0; bottom: 0; background-color: #fff; z-index: 1000000; border: 1px solid #000; overflow: hidden;" runat="server">
                    <div style="position: absolute; left: 0; right: 0; height: 25px; top: 0; background-color: #0070cd">
                        <div style="position: absolute; right: 5px; color: #fff;">
                           <button type="button" style="background: none; border: none" id="btnCerrarVentanaPreviewDocumento">X</button></div>
                       
                       
                    </div>
                    <div style="position: absolute; left: 0; right: 0; bottom: 0; top: 25px;">
                        <iframe id="Iframe" runat="server" width="100%" height="100%" style="height: 100%; border: none"></iframe>
                    </div>
                </div>
                    <div id="PreviewDocumentoBg" style="position: absolute; left: 0; right: 0; bottom: 0; top: 0; background-color: rgba(0, 0, 0, 0.6); z-index: 999; display: none"></div>

                <div id="output"></div>
                <div id="contenedor-principal">
                    <div id="navegacion"></div>
                    <div id="header-principal">
                        <div class="logo"></div>
                        <div class="header-principal-titulo">
                            <h1>SISTEMA DE INFORMACION</h1>
                            <h2>GESTIÓN DE PAGOS Y MANEJO DE CORRESPONDENCIAS</h2>
                        </div>
                        <div id="area-user">
                            <div id="area-user-info">
                                <div id="imgUsuario"></div>
                                <dx:BootstrapBinaryImage ID="ImgUsuario" runat="server"></dx:BootstrapBinaryImage>
                                <div id="InfoDatosUsuario">
                                    <h3 style="margin-top: -20px" runat="server" id="LoginUser"></h3>
                                    <h4 runat="server" id="LoginDepartamento"></h4>
                                </div>
                            </div>
                            <a href="#" runat="server" onserverclick="Unnamed_ServerClick" style="text-align: center; margin-right: 10px; display: block; padding: 5px; text-transform: uppercase; float: left; text-decoration: none"><span style="font-size: 35px;" class="glyphicon glyphicon-th"></span>
                                <br />
                                <span style="font-family: Arial; font-size: 10px; font-weight: bold">Menu Principal</span></a>
                            <!--<i class="material-icons" style="font-size:px;color:red">error</i>-->
                            <div style="display: block; float: right; width: 97px; height: 62px; text-align: center; padding-top: 9px">
                                <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-search" ID="BootstrapButton1" runat="server" UseSubmitBehavior="false" AutoPostBack="false">
                                    <SettingsBootstrap RenderOption="Default" />
                                    <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('CONSULTAR-EXPEDIENTES|');}" />
                                </dx:BootstrapButton>
                                <br />
                                <span style="font-family: Arial; font-size: 10px; font-weight: bold; text-transform: uppercase; color: #337AB7">Consultar</span>
                            </div>
                        </div>
                    </div>
                    <div id="header-modulo">
                        <span class="glyphicon glyphicon-th-list"></span><span runat="server" id="SeccionSeleccionada"></span>
                        <div class="header-modulo-tool">
                            <button onserverclick="btNuevoExpediente" runat="server" id="btnNuevo" type="button" class="btn btn-warning">
                                <span class="glyphicon glyphicon-plus"></span>Nuevo</button>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-check" ID="btnSalvar" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text=" Salvar" Visible="false">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('SALVAR|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-check" ID="btnActualizar" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text=" Actualizar">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('ACTUALIZAR|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnRegistros" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Registrados">
                                <SettingsBootstrap RenderOption="Default" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-REGISTRADOS|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnRecibidos" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Recibidos">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-RECIBIDOS|');}" />
                            </dx:BootstrapButton>

                            <!--
                            <button runat="server" onclick="function(s, e) { cbPrincipal.PerformCallback('ABRIR-RECIBIDO|');$('#vista3').hide();}" type="button" class="btn btn-default btnExpedientesRecibidos">
                                <span class="glyphicon glyphicon-file"></span>Recibidos <span runat="server" class="badge recibidos" id="labelTotalRecibidos"></span>
                            </button>
                        
                            <button runat="server" onserverclick="btn_Click_OpenExpedientesEnviados" id="Button2" type="button" class="btn btn-default btnExpedientesEnviados">
                                <span class="glyphicon glyphicon-list"></span>Enviados</button>    -->

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnEnviados" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Enviados">
                                <SettingsBootstrap RenderOption="Default" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-ENVIADOS|');}" />
                            </dx:BootstrapButton>


                        </div>
                    </div>
                    <div id="contenido">
                        <div class="vista-modulo" id="vista1" runat="server">
                            <div class="data-grid">
                                <!--<div class="data-grid-header">
                            <i id="loading-filtrar" style="position: absolute; left: 520px; top: 10px; font-size: 28px; display: none" class="fa fa-circle-o-notch fa-spin"></i>
                            <div class="input-group input-group-unstyled data-grid-buscador">
                                <input type="text" class="form-control" name="FiltarRegistros" id="filtrar-registros" placeholder="Buscar expediente" />
                                <div style="position: absolute; right: 10px"><span class="glyphicon glyphicon-search form-control-feedback"></span></div>
                            </div>
                            
                        </div>-->
                                <div class="data-grid-contenido" style="top: 0">



                                    <dx:ASPxGridView ID="grdListadoExpedientesRecibidos" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" KeyFieldName="IdRegistro" HorizontalScrollBarMode="Auto" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">
                                        <%--<ClientSideEvents RowDblClick="OpenRegistro()" />--%>
                                        <ClientSideEvents CustomButtonClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR-RECIBIDO|&#39;+ s.GetRowKey(e.visibleIndex));}"
                                            RowDblClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR-RECIBIDO|&#39;+ s.GetRowKey(e.visibleIndex));}"></ClientSideEvents>

                                        <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                        <Settings ShowFooter="true" ShowHeaderFilterButton="false" VerticalScrollBarMode="Hidden" VerticalScrollableHeight="400" VerticalScrollBarStyle="VirtualSmooth" />
                                        <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                        <SettingsSearchPanel Visible="True"></SettingsSearchPanel>
                                        <Columns>
                                            <dx:GridViewCommandColumn Width="40" VisibleIndex="0" Name="gcIdExpediente">
                                                <CustomButtons>
                                                    <dx:GridViewCommandColumnCustomButton Image-IconID="actions_pagenext_16x16devav">
                                                    </dx:GridViewCommandColumnCustomButton>
                                                </CustomButtons>
                                            </dx:GridViewCommandColumn>
                                            <dx:GridViewDataTextColumn CellStyle-HorizontalAlign="Center" Width="100" FieldName="IdExpediente" Name="gcIdExpediente" Caption="Expediente" VisibleIndex="1"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="TipoDocumento" Name="gcTipoDocumento" Caption="Tipo" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="NumeroDocumento" Name="gcNumeroDocumento" Caption="Número" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="Asunto" Name="gcAsunto" Caption="Asunto" VisibleIndex="2"></dx:GridViewDataTextColumn>

                                            <dx:GridViewDataTextColumn FieldName="DepartamentoRemitente" Name="gcDepartamentoRemitente" Caption="Departamento Remitente" VisibleIndex="3"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="UsuarioRemitente" Name="gcUsuarioRemitente" Caption="Usuario Remitente" VisibleIndex="4"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="DepartamentoDestinatario" Name="gcDepartamentoDestinatario" Caption="Departamento Destinatario" VisibleIndex="5"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" CellStyle-HorizontalAlign="Center" FieldName="FechaEnviado" Name="gcFechaEnviado" Caption="Fecha" VisibleIndex="6"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="LabelUbicacion" Name="gcLabelUbicacion" Caption="Ubicaci&#243;n Actual" VisibleIndex="7"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="LabelEstatus" Name="gcLabelEstatus" Caption="Estatus" VisibleIndex="8"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="c2" Width="100" FieldName="Balance" Name="gcBalance" Caption="Balance" VisibleIndex="9"></dx:GridViewDataTextColumn>
                                        </Columns>
                                    </dx:ASPxGridView>

                                </div>
                            </div>
                        </div>
                        <div class="vista-modulo ocultar-vista" id="vista2" runat="server" visible="false">

                            <input style="position: absolute; z-index: 1000; bottom: 10px; left: 300px" type="hidden" id="TxtIdUsuario" name="TxtIdUsuario" value="1" runat="server">
                            <input type="text" id="IdDepartamento" name="IdDepartamento" value="" runat="server" style="display: none">
                            <input style="position: absolute; z-index: 1000; bottom: 10px; right: 100px" type="hidden" id="Option" name="Option">
                            <div class="formulario">
                                <div class="header-formulario">

                                    <ul class="nav nav-tabs">
                                        <li class="active">
                                            <a data-toggle="tab" href="#home">Expediente #
                                        <span runat="server" id="labelIdExpediente"></span>
                                            </a>
                                        </li>
                                        <li>
                                            <a runat="server" id="btnCronologia" data-toggle="tab" href="#menu1">Cronología</a>
                                        </li>
                                        <li>
                                            <a runat="server" data-toggle="tab" href="#menu2" id="btnGestionar">Remitir</a>
                                        </li>
                                        <li>
                                            <a runat="server" data-toggle="tab" href="#menu3" id="btnDocumentos">Documentos</a>
                                        </li>
                                    </ul>
                                    <div style="position: absolute; right: 12px; top: 10px; z-index: 3">



                                        <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-remove" ID="btnCloseFormulario" runat="server" UseSubmitBehavior="false" AutoPostBack="false">
                                            <SettingsBootstrap RenderOption="None" />
                                            <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('CERRAR|');}" />
                                        </dx:BootstrapButton>

                                    </div>
                                </div>
                                <div class="content-formulario">
                                    <div class="tab-content">

                                        <div id="home" class="tab-pane fade in active">
                                            <div>
                                                <div class="row">
                                                    <div class="col-sm-4">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <strong>Datos del documento</strong>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <input type="hidden" id="verArchivo" runat="server" />
                                                                    <%--<dx:BootstrapTextBox ClientInstanceName="PArchivo" ID="PArchivo" runat="server"></dx:BootstrapTextBox>--%>

                                                                    <div class="col-sm-12">
                                                                        <div class="input-group" style="margin-bottom: 10px; width:100%">
                                                                            <span class="input-group-addon">Tipo de Documento</span>
                                                                            <input runat="server" maxlength="11" readonly type="hidden" class="form-control" id="IdTipoDocumento" name="IdTipoDocumento">

                                                                            <input runat="server" readonly type="hidden" class="form-control" name="TipoDocumento" id="TipoDocumento">

                                                                            <dx:BootstrapComboBox ClientInstanceName="txtTipoDocumento" Width="100%" AutoPostBack="false" ID="txtTipoDocumento" runat="server" TextField="TipoDocumento" ValueField="IdTipoDocumento">
                                                                            <ClientSideEvents ValueChanged="function(){ SeleccionarTipoDocumento(); }" />
                                                                            </dx:BootstrapComboBox>


                                                                        </div>
                                                                    </div>

                                                                </div>
                                                                <div class="row" style="margin-bottom: 10px; display:none" id="divNumeroRequerimiento">
                                                                    <div class="col-sm-12">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">No. de Requisición</span>
                                                                            <dx:BootstrapTextBox ClientInstanceName="txtNumeroRequisicion" MaxLength="50" ID="txtNumeroRequisicion" runat="server"></dx:BootstrapTextBox>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="row" style="margin-bottom: 10px">

                                                                    <div class="col-sm-6" style="padding-right: 30px">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">Emision</span>

                                                                            <dx:BootstrapDateEdit Width="135" ID="FechaEmision" runat="server"></dx:BootstrapDateEdit>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-6">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">No.</span>
                                                                            <dx:BootstrapTextBox MaxLength="50" ID="txtNumeroDocumento" runat="server"></dx:BootstrapTextBox>
                                                                        </div>
                                                                    </div>

                                                                </div>

                                                                <div class="row" style="margin-bottom: 10px">
                                                                    <div class="col-sm-12">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">Asunto</span>

                                                                            <dx:BootstrapMemo ID="Asunto" runat="server" MaxLength="60"></dx:BootstrapMemo>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                                <div class="row" style="margin-bottom: 10px">
                                                                    <div class="col-sm-12">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">NCF</span>

                                                                            <dx:BootstrapTextBox MaxLength="50" ID="NCF" runat="server"></dx:BootstrapTextBox>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                                <div class="row" style="margin-bottom: 10px">
                                                                    <div class="col-sm-12">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">Descripción</span>
                                                                            <textarea runat="server" maxlength="256" class="form-control Descripcion" id="Descripcion" name="Descripcion" placeholder="Descripción"></textarea>
                                                                        </div>
                                                                    </div>

                                                                </div>

                                                                <div class="row" style="margin-bottom: 10px; ">
                                                                    <div class="col-sm-12">

                                                                        <div class="input-group" style="width:100%">
                                                                            <span class="input-group-addon" style="z-index: 10">Procedencia</span>
                                                                            <input runat="server" maxlength="11" readonly type="hidden" class="form-control IdProcedencia" id="IdProcedencia" name="IdProcedencia" placeholder="Id">

                                                                            <input runat="server" readonly id="Procedencia" type="hidden" class="form-control Procedencia" name="Procedencia" placeholder="Procedencia Interna">

                                                                            <dx:BootstrapComboBox CssClasses-Control="IdProcedencia" Width="100%" AutoPostBack="false" CssClasses-IconDropDownButton="glyphicon glyphicon-search" ID="txtProcedencia" runat="server" TextField="Procedencia" ValueField="IdProcedencia">
                                                                            </dx:BootstrapComboBox>

                                                                        </div>

                                                                    </div>

                                                                </div>
                                                                <div class="row" style="margin-bottom: 10px">
                                                                    <div class="col-sm-12">
                                                                        <div class="input-group" style="width:100%">
                                                                            <span class="input-group-addon">Entregado por</span>
                                                                            <input style="display: none" readonly type="hidden" runat="server" maxlength="11" class="form-control" id="IdEntregadoPor" name="IdEntregadoPor">

                                                                            <input runat="server" readonly type="hidden" class="form-control " id="EntregadoPor" name="EntregadoPor" placeholder="Entregado por">

                                                                            <dx:BootstrapComboBox CallbackPageSize="15" Width="100%" AutoPostBack="false" CssClasses-Control="EntregadoPor" CssClasses-IconDropDownButton="glyphicon glyphicon-search" ID="txtEntregadoPor" runat="server" TextField="EntregadoPor" ValueField="IdEntregadoPor">
                                                                            </dx:BootstrapComboBox>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                                <div class="row" style="margin-bottom: 10px">

                                                                    <div class="col-sm-6">
                                                                        <div class="input-group" style="z-index: 10">
                                                                            <span class="input-group-addon">Fecha entrega</span>

                                                                            <dx:BootstrapDateEdit Width="120" runat="server" EditFormat="Date" ID="FechaEntrega">
                                                                            </dx:BootstrapDateEdit>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <div class="input-group" style="padding-left: 30px;">
                                                                            <span class="input-group-addon">Hora</span>



                                                                            <dx:BootstrapTextBox MaxLength="8" Width="110" ID="HoraEntrega" runat="server"></dx:BootstrapTextBox>

                                                                        </div>
                                                                    </div>

                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-4">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <strong>Datos del Beneficiario</strong>
                                                            </div>
                                                            <div class="panel-body">

                                                                <div class="row" style="margin-bottom: 10px">
                                                                    <div class="col-sm-12">
                                                                        <div class="input-group" style="width:100%">
                                                                            <span class="input-group-addon">Suplidor</span>
                                                                            <input style="display: none" runat="server" maxlength="11" type="hidden" class="form-control IdBeneficiario" id="IdBeneficiario" name="IdBeneficiario" placeholder="Id">

                                                                            <input runat="server" type="hidden" readonly class="form-control Beneficiario" id="Beneficiario" name="Beneficiario" placeholder="Beneficiario">

                                                                            <dx:BootstrapComboBox ClientInstanceName="txtBeneficiario" CallbackPageSize="15" Width="100%" AutoPostBack="false" CssClasses-IconDropDownButton="glyphicon glyphicon-search" ID="txtBeneficiario" runat="server" TextField="Beneficiario" ValueField="IdBeneficiario">
                                                                            </dx:BootstrapComboBox>
                                                                        </div>
                                                                    </div>

                                                                </div>


                                                                <div class="row" style="margin-bottom: 10px">

                                                                    <div class="col-sm-12">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">Correo</span>
                                                                            <dx:BootstrapTextBox MaxLength="50" ClientInstanceName="BeneficiarioCorreo" ID="BeneficiarioCorreo" runat="server"></dx:BootstrapTextBox>
                                                                        </div>
                                                                    </div>

                                                                </div>


                                                                <div class="row" style="margin-bottom: 10px">

                                                                    <div class="col-sm-12">
                                                                        <div class="input-group" style="width:100%">
                                                                            <input style="display: none" runat="server" maxlength="11" type="hidden" class="form-control" id="IdTipoPago" name="IdTipoPago">

                                                                            <input runat="server" type="hidden" readonly class="form-control Beneficiario" id="TipoPago" name="TipoPago">

                                                                            <span class="input-group-addon">Tipo de Pago</span>


                                                                            <dx:BootstrapComboBox ClientInstanceName="txtTipoPago" Width="100%" AutoPostBack="false" ID="txtTipoPago" runat="server" TextField="TipoPago" ValueField="IdTipoPago">
                                                                            </dx:BootstrapComboBox>
                                                                        </div>
                                                                    </div>

                                                                </div>

                                                                <div class="row">

                                                                    <div class="col-sm-4">
                                                                        <div class="form-group">
                                                                            VALOR FACTURA
                                                                        </div>
                                                                    </div>



                                                                    <div class="col-sm-4">
                                                                        <div class="form-group">
                                                                            IMPUESTO
                                                                        </div>
                                                                    </div>


                                                                    <div class="col-sm-4">
                                                                        <div class="form-group">
                                                                            MONTO
                                                                        </div>
                                                                    </div>

                                                                </div>


                                                                <div class="row" style="margin-bottom: 10px">

                                                                    <div class="col-sm-4">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">$</span>

                                                                            <%--<dx:BootstrapTextBox  MaxLength="10" ID="ValorFactura" ma runat="server"></dx:BootstrapTextBox>--%>
                                                                            <dx:BootstrapSpinEdit DisplayFormatString="n2" SpinButtons-ClientVisible="false" NumberType="Float" Number="0.00" ID="ValorFactura" runat="server" AllowNull="false" AllowMouseWheel="false">
                                                                                <ClientSideEvents ValueChanged="function(s, e) { cbTotalCxP.PerformCallback(); }" />
                                                                            </dx:BootstrapSpinEdit>
                                                                        </div>
                                                                    </div>



                                                                    <div class="col-sm-4">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">$</span>

                                                                            <dx:BootstrapSpinEdit DisplayFormatString="n2" SpinButtons-ClientVisible="false" NumberType="Float" Number="0.00" ID="Impuesto" runat="server" AllowNull="false" AllowMouseWheel="false">
                                                                                <ClientSideEvents ValueChanged="function(s, e) { cbTotalCxP.PerformCallback(); }" />
                                                                            </dx:BootstrapSpinEdit>
                                                                        </div>
                                                                    </div>


                                                                    <div class="col-sm-4">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">$</span>




                                                                            <dx:ASPxCallbackPanel ID="cbTotalCxP" ClientInstanceName="cbTotalCxP" OnCallback="cbTotalCxP_Callback" runat="server">
                                                                                <ClientSideEvents></ClientSideEvents>
                                                                                <PanelCollection>
                                                                                    <dx:PanelContent>
                                                                                        <dx:BootstrapSpinEdit ReadOnly="true" DisplayFormatString="n2" SpinButtons-ClientVisible="false" NumberType="Float" Number="0.00" ID="Monto" runat="server" AllowNull="false" AllowMouseWheel="false"></dx:BootstrapSpinEdit>
                                                                                    </dx:PanelContent>
                                                                                </PanelCollection>
                                                                            </dx:ASPxCallbackPanel>





                                                                        </div>
                                                                    </div>

                                                                </div>




                                                            </div>
                                                        </div>
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <strong>Observación</strong>
                                                            </div>
                                                            <div class="panel-body">

                                                                <div class="row">
                                                                    <div class="col-sm-12">
                                                                        <div class="form-group" style="padding-bottom: 10px">
                                                                            <textarea runat="server" maxlength="512" rows="4" class="form-control Observacion" id="Observacion" name="Observacion" placeholder="Observación"></textarea>
                                                                        </div>
                                                                    </div>

                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-4" style="padding-right: 15px">
                                                        <!-- One of three columns-->

                                                        <div class="panel panel-default panel-data-registro">
                                                            <div class="panel-heading">
                                                                <strong>Datos del Registro</strong>
                                                            </div>
                                                            <div class="panel-body" style="padding-bottom: 10px;">

                                                                <div class="row">
                                                                    <div class="col-sm-12">
                                                                        <p>Fecha de Registro: <strong><span runat="server" id="fechaRegistro"></span></strong></p>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-12">
                                                                        <p>Registrado por: <strong><span runat="server" id="registradoPor"></span></strong></p>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-12">
                                                                        <p>Ultima modificación: <strong><span runat="server" id="ultimaModificacion"></span></strong></p>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-12">
                                                                        <p>Modificado por: <strong><span runat="server" id="modificadoPor"></span></strong></p>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-12">
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>


                                                        <div class="panel panel-default panel-data-registro">
                                                            <div class="panel-heading">
                                                                <strong>Antigüedad</strong>
                                                            </div>
                                                            <div class="panel-body" style="padding-bottom: 10px;">
                                                                <p>Ubicación: <strong><span runat="server" id="ultimaUbicacion"></span></strong></p>

                                                                <p>Estatus: <strong><span runat="server" id="ultimoEstatus"></span></strong></p>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>


                                        </div>
                                        <div id="menu1" class="tab-pane fade">
                                            <!--- cronologia ----->
                                            <div class="data-grid" style="border: none; left: 0px; right: 0px; top: 10px; bottom: 0px;">

                                                <div class="data-grid-contenido" style="top: 0">

                                                    <dx:ASPxGridView ID="grdCronologia" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" HorizontalScrollBarMode="Auto" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">


                                                        <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                                        <Settings ShowFooter="true" ShowHeaderFilterButton="false" VerticalScrollBarMode="Auto" VerticalScrollableHeight="300" VerticalScrollBarStyle="VirtualSmooth" />
                                                        <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                                        <Columns>

                                                            <dx:GridViewDataTextColumn FieldName="Ubicacion" Name="gcUbicacion" Caption="Ubicaci&#243;n Actual" VisibleIndex="1"></dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn FieldName="Estatus" Name="gcEstatus" Caption="Estatus" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn FieldName="DepartamentoRemitente" Name="gcDepartamentoRemitente" Caption="Departamento Remitente" VisibleIndex="3"></dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn Width="200" CellStyle-HorizontalAlign="Center" FieldName="EnviadoPor" Name="gcEnviadoPor" Caption="Enviado por" VisibleIndex="4"></dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" Width="100" CellStyle-HorizontalAlign="Center" FieldName="Fecha" Name="gcFecha" Caption="Fecha" VisibleIndex="5"></dx:GridViewDataTextColumn>
                                                            <dx:GridViewDataTextColumn FieldName="Comentario" Name="gcComentario" Caption="Comentario" VisibleIndex="6"></dx:GridViewDataTextColumn>
                                                        </Columns>
                                                    </dx:ASPxGridView>

                                                </div>
                                            </div>
                                            <!--- ----->
                                        </div>

                                        <div id="menu2" class="tab-pane fade">
                                            <!--- ----->
                                            <div class="panel panel-default">
                                                <div class="panel-heading mi-header">
                                                    <strong>Estatus y Destino del Expediente</strong>
                                                </div>
                                                <div class="panel-body" style="padding-bottom: 10px;">
                                                    <div class="row">
                                                        <div class="col-sm-12">
                                                            <div class="input-group" style="margin-bottom: 10px; width: 100%">
                                                                <span class="input-group-addon" style="width: 100px">Estatus</span>



                                                                <dx:BootstrapComboBox EnableViewState="false" ViewStateMode="Disabled" Width="100%" AutoPostBack="false" ID="selectEstatus" runat="server" TextField="EstatusAplicado" ValueField="IdEstatusAplicado">
                                                                </dx:BootstrapComboBox>

                                                            </div>

                                                        </div>
                                                        <div class="col-sm-12">
                                                            <div class="input-group" style="margin-bottom: 10px">

                                                                <span class="input-group-addon">Destinatario</span>

                                                                <dx:BootstrapComboBox EnableViewState="false" ViewStateMode="Disabled" Width="100%" AutoPostBack="false" ID="selectDestinatario" runat="server" TextField="Destinatario" ValueField="IdDestinatario">
                                                                </dx:BootstrapComboBox>

                                                            </div>

                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12">
                                                        <div class="input-group" style="margin: -15px; padding: 0; margin-top: 0px; margin-bottom: 10px; width: 102%">
                                                            <span class="input-group-addon" style="width: 100px">Comentario</span>
                                                            <textarea style="margin: 0" rows="10" runat="server" maxlength="256" class="form-control" id="TxtComentario" name="TxtComentario" placeholder="Comentario"></textarea>
                                                        </div>
                                                    </div>


                                                    <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-envelope" ID="btnEnviar" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text=" Gestionar">
                                                        <SettingsBootstrap RenderOption="Success" />
                                                        <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('ENVIAR|');}" />
                                                    </dx:BootstrapButton>

                                                </div>
                                            </div>
                                            <!--- ----->
                                        </div>
                                        <div id="menu3" class="tab-pane fade">
                                            <div class="data-grid" style="border: none; left: 0px; right: 0px; top: 10px; bottom: 0px;">
                                                <div style="position: absolute">
                                                    <div class="input-group" style="">
                                                        <%--onchange="__doPostBack('ContentPlaceHolder1_cbPrincipal_btnAgregarArchivo_CD','CARGAR_ARCHIVO');"--%>
                                                        <%--<asp:FileUpload ID="cargador" CssClass="btn" Style="width: 85%; height: 15px;" runat="server" />--%>
                                                        <%--<asp:FileUpload ID="uploader" runat="server" />--%>

                                                        <dx:BootstrapUploadControl ID="UploadControl" runat="server" ShowUploadButton="true" DialogTriggerID="externalDropZone"
                                                            ShowProgressPanel="True" OnFileUploadComplete="UploadControl_FileUploadComplete">
                                                            <UploadButton IconCssClass="glyphicon glyphicon-cloud-upload" Text="Cargar Archivo" />
                                                            <AdvancedModeSettings EnableDragAndDrop="True" EnableFileList="False" EnableMultiSelect="False" ExternalDropZoneID="externalDropZone" DropZoneText="Soltar archivo aquí" />
                                                            <ValidationSettings MaxFileSize="100000000" />
                                                            <BrowseButton Text="Agregar archivo" />
                                                            <%--<DropZoneStyle CssClass="uploadControlDropZone" />--%>
                                                            <%--<ProgressBarStyle CssClass="uploadControlProgressBar" />--%>
                                                            <ClientSideEvents
                                                                DropZoneEnter="function(s, e) { if(e.dropZone.id == 'externalDropZone') setElementVisible('dropZone', true); }"
                                                                DropZoneLeave="function(s, e) { if(e.dropZone.id == 'externalDropZone') setElementVisible('dropZone', false); }" FileUploadComplete="onUploadControlFileUploadComplete"></ClientSideEvents>

                                                        </dx:BootstrapUploadControl>
                                                        <br />
                                                        <%--<dx:ASPxButton Width="200" ID="btnAgregarArchivo" UseSubmitBehavior="false" Visible="true" AutoPostBack="true" Theme="MetropolisBlue" Text="Agregar Archivo" runat="server" OnClick="btnAgregarArchivo_Click">
                                                                <ClientSideEvents Click="function(s, e) { e.processOnServer = true;}"></ClientSideEvents>
                                                                <Image IconID="actions_addfile_16x16office2013"></Image> 
                                                            </dx:ASPxButton>--%>
                                                    </div>
                                                </div>
                                                <div class="data-grid-contenido" style="top: 100px">

                                                    <dx:ASPxCallbackPanel ID="cbDocumentos" runat="server" ClientInstanceName="cbDocumentos" OnCallback="cbDocumentos_Callback">
                                                        <PanelCollection>
                                                            <dx:PanelContent>

                                                                <dx:ASPxGridView ID="grdArchivos" EnableCallBacks="false" ClientInstanceName="grdArchivos" runat="server" Theme="MetropolisBlue" SettingsBehavior-EnableCustomizationWindow="true" OnCustomButtonInitialize="grdArchivos_CustomButtonInitialize" OnCustomButtonCallback="grdArchivos_CustomButtonCallback" HorizontalScrollBarMode="Auto" KeyFieldName="IdArchivo" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">
                                                                    <%--<ClientSideEvents CustomButtonClick="function(s, e) {  cbDocumentos.PerformCallback('ELIMINAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex)); }" />--%>
                                                                    <ClientSideEvents CustomButtonClick="OncustomButtonClick" />
                                                                    <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                                                    <Settings ShowFooter="true" ShowHeaderFilterButton="false" VerticalScrollBarMode="Auto" VerticalScrollableHeight="300" VerticalScrollBarStyle="VirtualSmooth" />
                                                                    <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                                                    <Columns>
                                                                        <dx:GridViewDataTextColumn Width="50" FieldName="IdDocumento" Name="gcIdDocumento" Caption="DOC" VisibleIndex="0" CellStyle-HorizontalAlign="Center"></dx:GridViewDataTextColumn>
                                                                        <dx:GridViewDataTextColumn Width="50" FieldName="IdArchivo" Name="gcIdArchivo" Caption="ID" VisibleIndex="1" CellStyle-HorizontalAlign="Center"></dx:GridViewDataTextColumn>
                                                                        <dx:GridViewDataTextColumn FieldName="Nombre" Name="gcNombre" Caption="Archivo" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                                                        <dx:GridViewDataTextColumn Visible="false" FieldName="RutaVirtual" Name="gcRutaVirtual" Caption="Ruta Virtual" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                                                        <dx:GridViewDataTextColumn Width="150" FieldName="UsuarioRegistraArchivo" Name="gcUsuarioRegistraArchivo" Caption="Agregado por" VisibleIndex="4"></dx:GridViewDataTextColumn>
                                                                        <dx:GridViewDataCheckColumn Width="100" FieldName="EliminadoArchivo" Name="gcEliminadoArchivo" Caption="&#191;Eliminado?" VisibleIndex="3"></dx:GridViewDataCheckColumn>

                                                                        <dx:GridViewCommandColumn Caption="#" Width="120" VisibleIndex="5">
                                                                            <CustomButtons>
                                                                                <dx:GridViewCommandColumnCustomButton Text="Visualizar" ID="btnVisualizar">
                                                                                    <Image IconID="print_preview_16x16"></Image>
                                                                                </dx:GridViewCommandColumnCustomButton>
                                                                            </CustomButtons>
                                                                        </dx:GridViewCommandColumn>
                                                                        <dx:GridViewCommandColumn Caption="#" Width="120" VisibleIndex="5">
                                                                            <CustomButtons>
                                                                                <dx:GridViewCommandColumnCustomButton Text="Descargar" ID="btnDescargar">
                                                                                    <Image IconID="actions_download_16x16"></Image>
                                                                                </dx:GridViewCommandColumnCustomButton>
                                                                            </CustomButtons>
                                                                        </dx:GridViewCommandColumn>
                                                                        <dx:GridViewCommandColumn Caption="#" Width="120" VisibleIndex="5">
                                                                            <CustomButtons>
                                                                                <dx:GridViewCommandColumnCustomButton Text="Eliminar" ID="btnEliminar">
                                                                                    <Image IconID="edit_delete_16x16office2013"></Image>
                                                                                </dx:GridViewCommandColumnCustomButton>
                                                                            </CustomButtons>
                                                                        </dx:GridViewCommandColumn>

                                                                    </Columns>
                                                                </dx:ASPxGridView>

                                                                <dx:BootstrapPopupControl ID="ModalSubirArchivo" runat="server" ShowFooter="true"
                                                                    ClientInstanceName="infoPopup" CloseAction="None"
                                                                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                                                                    <HeaderTemplate>
                                                                        <h4>AGREGAR ARCHIVO
                                                                        </h4>
                                                                    </HeaderTemplate>
                                                                    <FooterTemplate>
                                                                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                                                                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); }" />
                                                                        </dx:BootstrapButton>
                                                                    </FooterTemplate>
                                                                    <ContentCollection>
                                                                        <dx:ContentControl>
                                                                            <p id="ModalSubirArchivoContent" runat="server">El archivo se a agregado satisfactoriamente</p>
                                                                        </dx:ContentControl>
                                                                    </ContentCollection>
                                                                </dx:BootstrapPopupControl>


                                                                <dx:BootstrapPopupControl ID="ModalConfirmacion" runat="server" ShowFooter="true"
                                                                    ClientInstanceName="infoPopup" CloseAction="None"
                                                                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                                                                    <HeaderTemplate>
                                                                        <h4>Eliminar archivo
                                                                        </h4>
                                                                    </HeaderTemplate>
                                                                    <FooterTemplate>
                                                                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                                                                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); }" />
                                                                        </dx:BootstrapButton>
                                                                    </FooterTemplate>
                                                                    <ContentCollection>
                                                                        <dx:ContentControl>
                                                                            <p id="P1" runat="server">El archivo se a agregado satisfactoriamente</p>
                                                                        </dx:ContentControl>
                                                                    </ContentCollection>
                                                                </dx:BootstrapPopupControl>

                                                            </dx:PanelContent>
                                                        </PanelCollection>
                                                    </dx:ASPxCallbackPanel>



                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="vista-modulo ocultar-vista" id="vistaConsultarExpedientes" runat="server" visible="false">
                            <div class="formulario">
                                <div style="position: absolute; left: 10px; top: 10px; bottom: 10px; width: 280px;">
                                    <dx:ASPxGridView ID="grdTiposDocumentos" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" KeyFieldName="IdTipoDocumento" HorizontalScrollBarMode="Auto" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">
                                        <%--<ClientSideEvents RowDblClick="OpenRegistro()" />--%>
                                        <ClientSideEvents CustomButtonClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR-EXPEDIENTE-POR-TIPO|&#39;+ s.GetRowKey(e.visibleIndex));}" RowClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR-EXPEDIENTE-POR-TIPO|&#39;+ s.GetRowKey(e.visibleIndex));}"></ClientSideEvents>

                                        <SettingsPager Mode="ShowAllRecords" PageSize="15"></SettingsPager>
                                        <Settings ShowFooter="true" VerticalScrollBarMode="Hidden" VerticalScrollableHeight="400" VerticalScrollBarStyle="VirtualSmooth" />
                                        <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                        <SettingsSearchPanel Visible="True"></SettingsSearchPanel>
                                        <Styles>
                                            <Cell Wrap="False"></Cell>
                                        </Styles>
                                        <Columns>
                                            <dx:GridViewCommandColumn Width="40" VisibleIndex="0" Name="gcIdExpediente">
                                                <CustomButtons>
                                                    <dx:GridViewCommandColumnCustomButton Image-IconID="actions_pagenext_16x16devav">
                                                    </dx:GridViewCommandColumnCustomButton>
                                                </CustomButtons>
                                            </dx:GridViewCommandColumn>
                                            <dx:GridViewDataTextColumn CellStyle-HorizontalAlign="Left" FieldName="TipoDocumento" Name="gcTipoDocumento" Caption="Tipo de documento" VisibleIndex="1">
                                            </dx:GridViewDataTextColumn>


                                        </Columns>
                                    </dx:ASPxGridView>
                                </div>
                                <div style="position: absolute; left: 305px; top: 10px; bottom: 10px; right: 10px;">
                                    <div style="position: absolute; left: 10px; font-size: 15px; text-transform: uppercase; font-family: Arial; margin-bottom: 30px; line-height: 50px">Documento Seleccionado: <span id="TituloDocumentoSeleccionado" style="font-weight: bold;" runat="server">Facturas</span></div>
                                    <dx:ASPxGridView ID="grdConsultarExpedientes" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" KeyFieldName="IdExpediente" HorizontalScrollBarMode="Auto" SettingsPager-Visible="true" Settings-ShowFooter="True" Width="100%">

                                        <ClientSideEvents CustomButtonClick="function(s, e) {	cbPrincipal.PerformCallback('ABRIR-DESDECONSULTA|'+ s.GetRowKey(e.visibleIndex));}" RowDblClick="function(s, e) {	cbPrincipal.PerformCallback('ABRIR-DESDECONSULTA|'+ s.GetRowKey(e.visibleIndex));}"></ClientSideEvents>
                                        <SettingsBehavior EnableCustomizationWindow="true" />
                                        <SettingsResizing ColumnResizeMode="Control" />
                                        <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                        <Settings ShowFooter="true" ShowFilterRow="false" ShowHeaderFilterButton="true" HorizontalScrollBarMode="Auto" VerticalScrollBarMode="Hidden" VerticalScrollableHeight="400" VerticalScrollBarStyle="VirtualSmooth" />
                                        <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                        <SettingsSearchPanel Visible="True"></SettingsSearchPanel>
                                        <Styles>
                                            <Cell Wrap="False"></Cell>
                                        </Styles>
                                        <Columns>
                                            <dx:GridViewCommandColumn Width="40" VisibleIndex="0" Name="gcIdExpediente">
                                                <CustomButtons>
                                                    <dx:GridViewCommandColumnCustomButton Image-IconID="actions_pagenext_16x16devav">
                                                    </dx:GridViewCommandColumnCustomButton>
                                                </CustomButtons>
                                            </dx:GridViewCommandColumn>
                                            <dx:GridViewDataTextColumn CellStyle-HorizontalAlign="Center" Width="100" FieldName="IdExpediente" Name="gcIdExpediente" Caption="Expediente" VisibleIndex="1">
                                            </dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="120" FieldName="NumeroDocumento" Name="gcNumeroDocumento" Caption="N&#250;mero" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="Asunto" Name="gcAsunto" Caption="Asunto" VisibleIndex="3" Width="200"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="Procedencia" Name="gcProcedencia" Caption="Procedencia" VisibleIndex="4" Width="300"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="120" PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" CellStyle-HorizontalAlign="Center" FieldName="FechaEntrega" Name="gcFechaEntrega" Caption=" Fecha Entrega" VisibleIndex="5"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" Width="120" CellStyle-HorizontalAlign="Center" FieldName="FechaRegistro" Name="gcFechaRegistro" Caption="Fecha Ingreso" VisibleIndex="6"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="LabelUbicacion" Name="gcLabelUbicacion" Caption="Ubicaci&#243;n Actual" VisibleIndex="7" Width="300"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="LabelEstatus" Name="gcLabelEstatus" Caption="Estatus" VisibleIndex="8"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="Beneficiario" Name="gcBeneficiario" Caption="Suplidor" VisibleIndex="9"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="RNC" Name="gcRNC" Caption="RNC" VisibleIndex="10"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="NCF" Name="gcNCF" Caption="NCF" VisibleIndex="10"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="ModificadoPor" Name="gcModificadoPor" Caption="Modificado Por" VisibleIndex="11"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="IdCxP" Name="gcIdCxP" Caption="IdCxP" VisibleIndex="12"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="c2" Width="160" FieldName="Balance" Name="gcBalance" Caption="Balance" VisibleIndex="13"></dx:GridViewDataTextColumn>
                                        </Columns>
                                        <TotalSummary>
                                            <dx:ASPxSummaryItem SummaryType="Count" ShowInColumn="gcLabelUbicacion" Visible="true" DisplayFormat="Total de Documentos: {0}" />
                                            <dx:ASPxSummaryItem SummaryType="Sum" FieldName="Balance" ShowInColumn="gcBalance" Visible="true" DisplayFormat="Total: {0:c2}" />
                                        </TotalSummary>
                                    </dx:ASPxGridView>
                                </div>
                                <div></div>
                            </div>
                        </div>
                        <div class="vista-modulo" id="vista3" runat="server">
                            <div class="data-grid">

                                <div class="data-grid-contenido" style="top: 0">

                                    <dx:ASPxGridView ID="grdListadoExpedientes1" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" KeyFieldName="IdExpediente" HorizontalScrollBarMode="Auto" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">
                                        <%--<ClientSideEvents RowDblClick="OpenRegistro()" />--%>
                                        <ClientSideEvents CustomButtonClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR|&#39;+ s.GetRowKey(e.visibleIndex));}" RowDblClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR|&#39;+ s.GetRowKey(e.visibleIndex));}"></ClientSideEvents>

                                        <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                        <Settings ShowFooter="true" ShowHeaderFilterButton="false" VerticalScrollBarMode="Hidden" VerticalScrollableHeight="400" VerticalScrollBarStyle="VirtualSmooth" />
                                        <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                        <SettingsSearchPanel Visible="True"></SettingsSearchPanel>
                                        <Columns>
                                            <dx:GridViewCommandColumn Width="40" VisibleIndex="0" Name="gcIdExpediente">
                                                <CustomButtons>
                                                    <dx:GridViewCommandColumnCustomButton Image-IconID="actions_pagenext_16x16devav">
                                                    </dx:GridViewCommandColumnCustomButton>
                                                </CustomButtons>
                                            </dx:GridViewCommandColumn>
                                            <dx:GridViewDataTextColumn CellStyle-HorizontalAlign="Center" Width="100" FieldName="IdExpediente" Name="gcIdExpediente" Caption="Expediente" VisibleIndex="1">
                                            </dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="TipoDocumento" Name="gcTipoDocumento" Caption="Tipo" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="120" FieldName="NumeroDocumento" Name="gcNumeroDocumento" Caption="N&#250;mero" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="Asunto" Name="gcAsunto" Caption="Asunto" VisibleIndex="3"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="Procedencia" Name="gcProcedencia" Caption="Procedencia" VisibleIndex="4"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="120" PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" CellStyle-HorizontalAlign="Center" FieldName="FechaEntrega" Name="gcFechaEntrega" Caption=" Fecha Entrega" VisibleIndex="5"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" Width="120" CellStyle-HorizontalAlign="Center" FieldName="FechaRegistro" Name="gcFechaRegistro" Caption="Fecha Ingreso" VisibleIndex="6"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="LabelUbicacion" Name="gcLabelUbicacion" Caption="Ubicaci&#243;n Actual" VisibleIndex="7"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="LabelEstatus" Name="gcLabelEstatus" Caption="Estatus" VisibleIndex="8"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="c2" Width="100" FieldName="Balance" Name="gcBalance" Caption="Balance" VisibleIndex="9"></dx:GridViewDataTextColumn>
                                        </Columns>
                                    </dx:ASPxGridView>

                                </div>
                            </div>
                        </div>




                        <div class="vista-modulo" id="vista4" runat="server">
                            <div class="data-grid">
                                <!--<div class="data-grid-header">
                            <i id="loading-filtrar" style="position: absolute; left: 520px; top: 10px; font-size: 28px; display: none" class="fa fa-circle-o-notch fa-spin"></i>
                            <div class="input-group input-group-unstyled data-grid-buscador">
                                <input type="text" class="form-control" name="FiltarRegistros" id="filtrar-registros" placeholder="Buscar expediente" />
                                <div style="position: absolute; right: 10px"><span class="glyphicon glyphicon-search form-control-feedback"></span></div>
                            </div>
                            
                        </div>-->
                                <div class="data-grid-contenido" style="top: 0">



                                    <dx:ASPxGridView ID="grdListEnviados" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" KeyFieldName="IdRegistro" HorizontalScrollBarMode="Auto" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">
                                        <%--<ClientSideEvents RowDblClick="OpenRegistro()" />--%>
                                        <ClientSideEvents CustomButtonClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR-ENVIADO|&#39;+ s.GetRowKey(e.visibleIndex));}"
                                            RowDblClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR-ENVIADO|&#39;+ s.GetRowKey(e.visibleIndex));}"></ClientSideEvents>


                                        <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                        <Settings ShowFooter="true" ShowHeaderFilterButton="false" VerticalScrollBarMode="Hidden" VerticalScrollableHeight="400" VerticalScrollBarStyle="VirtualSmooth" />
                                        <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                        <SettingsSearchPanel Visible="True"></SettingsSearchPanel>
                                        <Columns>
                                            <dx:GridViewCommandColumn Width="40" VisibleIndex="0" Name="gcIdExpediente">
                                                <CustomButtons>
                                                    <dx:GridViewCommandColumnCustomButton Image-IconID="actions_pagenext_16x16devav">
                                                    </dx:GridViewCommandColumnCustomButton>
                                                </CustomButtons>
                                            </dx:GridViewCommandColumn>
                                            <dx:GridViewDataTextColumn CellStyle-HorizontalAlign="Center" Width="100" FieldName="IdExpediente" Name="gcIdExpediente" Caption="Expediente" VisibleIndex="1"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="TipoDocumento" Name="gcTipoDocumento" Caption="Tipo" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="NumeroDocumento" Width="100" Name="gcNumeroDocumento" Caption="Número" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="Asunto" Name="gcAsunto" Caption="Asunto" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="DepartamentoRemitente" Name="gcDepartamentoRemitente" Caption="Departamento Remitente" VisibleIndex="3"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="UsuarioRemitente" Name="gcUsuarioRemitente" Caption="Usuario Remitente" VisibleIndex="4"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="DepartamentoDestinatario" Name="gcDepartamentoDestinatario" Caption="Departamento Destinatario" VisibleIndex="5"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" CellStyle-HorizontalAlign="Center" FieldName="FechaEnviado" Name="gcFechaEnviado" Caption="Fecha" VisibleIndex="6"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="LabelUbicacion" Name="gcLabelUbicacion" Caption="Ubicaci&#243;n Actual" VisibleIndex="7"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="LabelEstatus" Name="gcLabelEstatus" Caption="Estatus" VisibleIndex="8"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="c2" Width="100" FieldName="Balance" Name="gcBalance" Caption="Balance" VisibleIndex="9"></dx:GridViewDataTextColumn>
                                        </Columns>
                                    </dx:ASPxGridView>

                                </div>
                            </div>
                        </div>




                    </div>
                </div>






                <dx:BootstrapPopupControl ID="ModalActualizarRegistro" runat="server" ShowFooter="true" CloseAction="CloseButton"
                    ClientInstanceName="infoPopup"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                    <HeaderTemplate>
                        <h4>ACTUALIZANDO REGISTRO</h4>
                    </HeaderTemplate>
                    <FooterTemplate>
                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); $('#ContentPlaceHolder1_cbPrincipal_ModalActualizarRegistro').hide();$('.modal-backdrop').hide() }" />
                        </dx:BootstrapButton>
                    </FooterTemplate>
                    <ContentCollection>
                        <dx:ContentControl>
                            <p id="modalMensajeActualizar" runat="server">El registro se ha actualizado correctamente</p>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>


                <dx:BootstrapPopupControl ID="ModalEnviar" runat="server" ShowFooter="true"
                    ClientInstanceName="infoPopup" CloseAction="None"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                    <HeaderTemplate>
                        <h4>ENVIANDO EXPEDIENTE
                        </h4>
                    </HeaderTemplate>
                    <FooterTemplate>
                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); $('#ContentPlaceHolder1_cbPrincipal_ModalEnviar').hide();$('.modal-backdrop').hide() }" />
                        </dx:BootstrapButton>
                    </FooterTemplate>
                    <ContentCollection>
                        <dx:ContentControl>
                            <p id="modalMensajeEnviar" runat="server">El registro se ha envíado correctamente</p>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>

                <dx:BootstrapPopupControl ID="ModalSalvarRegistro" runat="server" ShowFooter="true"
                    ClientInstanceName="infoPopup" CloseAction="None"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                    <HeaderTemplate>
                        <h4>SALVANDO REGISTRO
                        </h4>
                    </HeaderTemplate>
                    <FooterTemplate>
                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); $('#ContentPlaceHolder1_cbPrincipal_ModalSalvarRegistro').hide();$('.modal-backdrop').hide() }" />
                        </dx:BootstrapButton>
                    </FooterTemplate>
                    <ContentCollection>
                        <dx:ContentControl>
                            <p id="msjSalvado" runat="server">El registro se ha salvado correctamente</p>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>


                <div style="position: absolute; color: #fff; bottom: 5px; left: 5px; right: 5px; height: 25px; border-top: 1px solid #ccc; padding-left: 10px">Registro: <span runat="server" id="RegistroSeleccionado"></span>Estatus: <span runat="server" id="EstatusActual"></span>Ultima Fecha: <span runat="server" id="UltimaFecha"></span><span id="Seccion" runat="server"></span></div>
            </dx:PanelContent>
        </PanelCollection>


    </dx:ASPxCallbackPanel>




</asp:Content>

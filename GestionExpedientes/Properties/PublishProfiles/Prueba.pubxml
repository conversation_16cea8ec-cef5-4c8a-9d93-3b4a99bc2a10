<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <IncludeSetACLProviderOnDestination>False</IncludeSetACLProviderOnDestination>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Debug</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>http://SERIIS002:93/</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <MSDeployServiceURL>https://SERIIS002:8172/msdeploy.axd</MSDeployServiceURL>
    <DeployIisAppPath>GestionExpedientes_Test</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <UserName>inaipi\jairo.moreno</UserName>
    <_SavePWD>False</_SavePWD>
    <PublishDatabaseSettings>
      <Objects xmlns="">
        <ObjectGroup Name="Excel03ConString" Order="1" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Provider=Microsoft.Jet.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES'" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\Excel03ConString_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Provider=Microsoft.Jet.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES'" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="Excel07ConString" Order="2" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES'" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\Excel07ConString_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES'" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="SqlConn" Order="3" Enabled="False">
          <Destination Path="Data Source=SERTEST001;Initial Catalog=GESHUM;User ID=sa;Password=InaipiSqlTest-01" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=SERTEST001;Initial Catalog=GESHUM;User ID=sa;Password=InaipiSqlTest-01;Max Pool Size=3000;Connect Timeout=0" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\SqlConn_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Data Source=SERTEST001;Initial Catalog=GESHUM;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=0;Max Pool Size=3000;" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="SqlCon" Order="4" Enabled="False">
          <Destination Path="Data Source=SERTEST001;Initial Catalog=QEC;Persist Security Info=True;User ID=sa;Password=InaipiSqlTest-01" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=SERTEST001;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01;Max Pool Size=3000;Connect Timeout=600" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\SqlCon_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Data Source=SERTEST001;Initial Catalog=QEC;User ID=sa;Password=InaipiSqlTest-01;Connection Timeout=600;Max Pool Size=3000;" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
      </Objects>
    </PublishDatabaseSettings>
  </PropertyGroup>
  <ItemGroup>
    <MSDeployParameterValue Include="$(DeployParameterPrefix)Excel03ConString-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)Excel07ConString-Web.config Connection String" />
    <MSDeployParameterValue Include="SqlConn-Web.config Connection String">
      <ParameterValue>Data Source=SERTEST001;Initial Catalog=GESHUM;User ID=sa;Password=InaipiSqlTest-01</ParameterValue>
    </MSDeployParameterValue>
    <MSDeployParameterValue Include="SqlCon-Web.config Connection String">
      <ParameterValue>Data Source=SERTEST001;Initial Catalog=QEC;Persist Security Info=True;User ID=sa;Password=InaipiSqlTest-01</ParameterValue>
    </MSDeployParameterValue>
  </ItemGroup>
</Project>
﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using SI_INAIPI.Controls.CxP;
using SI_INAIPI.DataAccess.CxP;
using SI_INAIPI.Models.CxP;
using INAIPI.Core;
using static SI_INAIPI.Global;
using System.Text.RegularExpressions;
using DevExpress.Web;
using System.IO;
using SI_INAIPI.Models.DIG;
using SI_INAIPI.Controls.DIG;
using DevExpress.Web.Demos;
using System.Net;

namespace GestionExpedientes
{
    public partial class Default : BasePage
    {
        CxP_PagosExpedientesCtrl ctrl = new CxP_PagosExpedientesCtrl();

        public enum EnumBoton { btnbtnRegistros = 1, btnRecibidos = 2, btnbtnEnviados = 3 }

        private EnumBoton BotonActivo
        {
            get { return (EnumBoton)Session["Default_BotonActivo"]; }
            set { Session["Default_BotonActivo"] = value; }
        }

        private List<CxP_PagosExpedientes> PagoExpedientes
        {
            get { return (List<CxP_PagosExpedientes>)Session["Default_PagoExpedientes"]; }
            set { Session["Default_PagoExpedientes"] = value; }
        }
        private List<CxP_PagosExpedientes> ListExpedientesPorTipoDocumento
        {
            get { return (List<CxP_PagosExpedientes>)Session["Default_PagoExpedientes_PorTipo"]; }
            set { Session["Default_PagoExpedientes_PorTipo"] = value; }
        }
        private List<CxP_PagosExpedientesRecibidos> PagoExpedientesRecibidos
        {
            get { return (List<CxP_PagosExpedientesRecibidos>)Session["Default_PagoExpedientes_Recibidos"]; }
            set { Session["Default_PagoExpedientes_Recibidos"] = value; }
        }
        private List<CxP_PagosExpedientesEnviados> PagoExpedientesEnviados
        {
            get { return (List<CxP_PagosExpedientesEnviados>)Session["Default_PagoExpedientes_Enviados"]; }
            set { Session["Default_PagoExpedientes_Enviados"] = value; }
        }

        private List<CxP_PagosExpedientesProcedencias> ListProcedencias
        {
            get { return (List<CxP_PagosExpedientesProcedencias>)Session["Default_ListProcedencias"]; }
            set { Session["Default_ListProcedencias"] = value; }
        }

        private List<CxP_PagosExpedientesMensajeros> ListMensajeros
        {
            get { return (List<CxP_PagosExpedientesMensajeros>)Session["Default_ListMensajeros"]; }
            set { Session["Default_ListMensajeros"] = value; }
        }
        private List<CxP_PagosExpedientesBeneficiarios> ListBeneficiarios
        {
            get { return (List<CxP_PagosExpedientesBeneficiarios>)Session["Default_ListBeneficiarios"]; }
            set { Session["Default_ListBeneficiarios"] = value; }
        }

        private CxP_PagosExpedientes PagoExpediente
        {
            get { return (CxP_PagosExpedientes)Session["Default_PagoExpediente"]; }
            set { Session["Default_PagoExpediente"] = value; }
        }
        private CxP_PagosExpedientesRecibidos PagoExpedienteRecibido
        {
            get { return (CxP_PagosExpedientesRecibidos)Session["Default_PagoExpedienteRecibido"]; }
            set { Session["Default_PagoExpedienteRecibido"] = value; }
        }
        private CxP_PagosExpedientesEnviados PagoExpedienteEnviado
        {
            get { return (CxP_PagosExpedientesEnviados)Session["Default_PagoExpedienteEnviado"]; }
            set { Session["Default_PagoExpedienteEnviado"] = value; }
        }
        private CxP_PagosExpedientesProcedencias ProcedenciaSeleccionada
        {
            get { return (CxP_PagosExpedientesProcedencias)Session["Default_ProcedenciaSeleccionada"]; }
            set { Session["Default_ProcedenciaSeleccionada"] = value; }
        }
        private List<CxP_PagosExpedientesCronologia> PagoExpedientesCronologia
        {
            get { return (List<CxP_PagosExpedientesCronologia>)Session["Default_PagoExpedientesCronologia"]; }
            set { Session["Default_PagoExpedientesCronologia"] = value; }
        }
        private List<CxP_PagosExpedientesDestinatarios> ListDestinatarios
        {
            get { return (List<CxP_PagosExpedientesDestinatarios>)Session["Default_ListDestinatarios"]; }
            set { Session["Default_ListDestinatarios"] = value; }
        }
        private List<CxP_PagosExpedientesTiposDocumentos> ListTiposDocumentos
        {
            get { return (List<CxP_PagosExpedientesTiposDocumentos>)Session["Default_ListTiposDocumentos"]; }
            set { Session["Default_ListTiposDocumentos"] = value; }
        }
        private List<CxP_PagosExpedientesTiposPagos> ListTiposPagos
        {
            get { return (List<CxP_PagosExpedientesTiposPagos>)Session["Default_ListTiposPagos"]; }
            set { Session["Default_ListTiposPagos"] = value; }
        }
        private List<CxP_PagosExpedientesEstatus> ListExpedientesEstatus
        {
            get { return (List<CxP_PagosExpedientesEstatus>)Session["Default_ListExpedientesEstatus"]; }
            set { Session["Default_ListExpedientesEstatus"] = value; }
        }
        private List<CorreoElectronicoDestinatarios> ListCorreos
        {
            get { return (List<CorreoElectronicoDestinatarios>)Session["Default_PagoExpedientesCorreos"]; }
            set { Session["Default_PagoExpedientesCorreos"] = value; }
        }
        private decimal UsuarioRemitente
        {
            get { return Convert.ToDecimal(Session["UsuarioRemitente"] ?? "0"); }
            set { Session["UsuarioRemitente"] = value; }
        }

        private List<DIG_Documentos.DIG_DocumentosArchivos> ListarDocumentos
        {
            get { return (List<DIG_Documentos.DIG_DocumentosArchivos>)Session["Default_ListarDocumentos"]; }
            set { Session["Default_ListarDocumentos"] = value; }
        }

        private DIG_Documentos.DIG_DocumentosArchivos Archivo
        {
            get { return (DIG_Documentos.DIG_DocumentosArchivos)Session["Default_Archivo"]; }
            set { Session["Default_Archivo"] = value; }
        }
        private List<DIG_Documentos.DIG_DocumentosArchivos> Archivos
        {
            get { return (List<DIG_Documentos.DIG_DocumentosArchivos>)Session["Default_Archivos"]; }
            set { Session["Default_Archivos"] = value; }
        }
        private String Src
        {
            get { return (String)Session["Default_Src"]; }
            set { Session["Default_Src"] = value; }
        }
        protected void Page_Load(object sender, EventArgs e)
        {

            if (!IsPostBack)
            {
                LimpiarVariablesDeSession();

            }

            if (!IsCallback)
            {
                string UsuarioRemitente = ctrl.Decode(Request.QueryString[ctrl.Encode("UsuarioRemitente")] ?? ""); //Request.QueryString["UsuarioRemitente"];
                string DepartamentoRemitente = ctrl.Decode(Request.QueryString[ctrl.Encode("DepartamentoRemitente")] ?? ""); //Request.QueryString["DepartamentoRemitente"];
                string Rol = ctrl.Decode(Request.QueryString[ctrl.Encode("Rol")] ?? ""); //Request.QueryString["Rol"];
                string btnSeccionado = ctrl.Decode(Request.QueryString[ctrl.Encode("btnSeccionado")] ?? ""); //Request.QueryString["Rol"];

                if (string.IsNullOrEmpty(UsuarioRemitente) | string.IsNullOrEmpty(DepartamentoRemitente) | string.IsNullOrEmpty(Rol))
                {
                    Response.Redirect(System.Configuration.ConfigurationManager.AppSettings["MenuPrincipalUrl"]);
                }

                Session["UsuarioRemitente"] = UsuarioRemitente; //"2023";
                Session["DepartamentoRemitente"] = DepartamentoRemitente; // "22"; //22, 30, 23, 25
                Session["Rol"] = Rol; //"Administrador";
                TxtIdUsuario.Value = Session["UsuarioRemitente"].ToString();
                IdDepartamento.Value = Session["DepartamentoRemitente"].ToString();
                SeccionSeleccionada.InnerHtml = " EXPEDIENTES RECIBIDOS";
                btnNuevo.Visible = false;

                //PreviewDocumento.Visible = false;

                Usuario userLogin = new Usuario();
                userLogin = ctrl.Usuario(UsuarioRemitente.ToString().ToInt());

                LoginUser.InnerText = userLogin.NombreCompleto;
                LoginDepartamento.InnerText = userLogin.Departamento;

                if (string.IsNullOrEmpty(btnSeccionado))
                {
                    Session["btnSeccionado"] = "Registros";
                    vista3.Visible = true;
                }
                else
                {
                    Session["btnSeccionado"] = btnSeccionado;
                    if (Session["btnSeccionado"].ToString() == "Enviados")
                    {
                        btnNuevo.Visible = false;
                        btnSalvar.Visible = false;
                        vista1.Visible = false;
                        vista2.Visible = false;
                        vista3.Visible = false;
                        vista4.Visible = true;
                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        SeccionSeleccionada.InnerHtml = " EXPEDIENTES ENVIADOS";
                    }
                    else
                    if (Session["btnSeccionado"].ToString() == "Registros")
                    {
                        btnNuevo.Visible = true;
                        vista1.Visible = false;
                        vista2.Visible = false;
                        vista3.Visible = true;
                        vista4.Visible = false;
                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        SeccionSeleccionada.InnerHtml = "EXPEDIENTES REGISTRADOS";
                    }
                    else if (Session["btnSeccionado"].ToString() == "Recibidos")
                    {
                        btnNuevo.Visible = false;
                        btnSalvar.Visible = false;
                        vista1.Visible = true;
                        vista2.Visible = false;
                        vista3.Visible = false;
                        vista4.Visible = false;

                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        SeccionSeleccionada.InnerHtml = " EXPEDIENTES RECIBIDOS";
                    }
                    else if (Session["btnSeccionado"].ToString() == "Consulta")
                    {
                        btnNuevo.Visible = false;
                        vista1.Visible = false;
                        vista2.Visible = false;
                        vista3.Visible = false;
                        vista4.Visible = false;
                        vistaConsultarExpedientes.Visible = true;

                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        SeccionSeleccionada.InnerHtml = " CONSULTAR EXPEDIENTES";
                        ListTiposDocumentos = ctrl.ListExpedientesTiposDocumentos();
                        grdTiposDocumentos.DataSource = ListTiposDocumentos;
                        grdTiposDocumentos.DataBind();
                    }
                    else if (Session["btnSeccionado"].ToString() == "FacturaCxP")
                    {
                        SeccionSeleccionada.InnerHtml = " REGISTRAR EXPEDIENTE";
                        FechaEmision.Value = DateTime.Today.Date;
                        FechaEntrega.Value = DateTime.Today.Date;
                        HoraEntrega.Value = DateTime.Now.ToString("HH:mm:ss");
                        Session["Default_PagoExpediente"] = null;
                        txtTipoDocumento.Value = "";
                        txtNumeroDocumento.Value = "";
                        Asunto.Value = "";
                        NCF.Value = "";
                        Descripcion.Value = "";
                        Procedencia.Value = "";
                        EntregadoPor.Value = "";
                        Beneficiario.Value = "";
                        BeneficiarioCorreo.Value = "";
                        TipoPago.Value = "";
                        ValorFactura.Value = "";
                        Impuesto.Value = "";
                        Monto.Value = "";
                        Observacion.Value = "";

                        vista1.Visible = false;
                        vista2.Visible = true;
                        vista3.Visible = false;
                        vista4.Visible = false;
                        btnNuevo.Visible = false;
                        vistaConsultarExpedientes.Visible = false;
                        btnDocumentos.Visible = false;
                        //PArchivo.Visible = false;

                        ListadoProcedencias1();
                        ListadoMensajeros1();
                        ListadoTiposDeDocumentos();
                        txtTipoDocumento.Value = ListTiposDocumentos?.FirstOrDefault()?.IdTipoDocumento.ToString();

                        ListadoTiposPagos();
                        ListadoExepedienteEstatus();
                        ListadoBeneficiarios();
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnSalvar.Visible = true;
                    }
                }
            }

            //Session["btnSeccionado"] = "Recibidos";

            /*
            ListadoProcedencias1();
            ListadoMensajeros1();
            ListadoTiposDeDocumentos();
            ListadoTiposPagos();
            ListadoExepedienteEstatus();
            */
            if (IsPostBack)
            {
                ListadoDestinatarios();
                ListadoProcedencias1();
                ListadoMensajeros1();
                ListadoTiposDeDocumentos();
                ListadoTiposPagos();
                ListadoExepedienteEstatus();
                ListadoBeneficiarios();

                grdArchivos.DataSource = ListarDocumentos;
                grdArchivos.DataBind();
            }
            else
            {
                LimpiarVariablesDeSession();
                //BotonActivo = EnumBoton.btnRecibidos;
            }
            //ValidarBotonActivo();

            int.TryParse(Session["DepartamentoRemitente"].ToString(), out int intDepartamentoRemitente);

            int totalrecibidos = ctrl.TotalRecibidos(intDepartamentoRemitente);

            labelTotalRecibidos.InnerHtml = totalrecibidos.ToString();

            if (Session["Rol"].ToString() != "Administrador")
            {
                //btnRegistros.Visible = false;
                //btnNuevo.Visible = false;
                //btnActualizar.Visible = false;
                //ListadoExpedientesRecibidos(true);
            }
            else
            {
                //btnNuevo.Visible = true;
                //vista3.Visible = false;
                // ListadoExpedientes(true);
            }

            //btnSalvar.Visible = false;
            btnActualizar.Visible = false;
            btnGestionar.Visible = false;
            btnCronologia.Visible = false;

            //OpenRegistro(125);
            ListadoExpedientes(!IsPostBack);   //vista
            ListadoExpedientesEnviados(!IsPostBack);    //vista
            ListadoExpedientesRecibidos(!IsPostBack);   //vista1
            //ListadoExpedientesEnviadosRecibidos(!IsPostBack);
            //ListadoProcedencias();
            // ListadoMensajeros();
            //ListadoBeneficiarios();
            /*vista1.Visible = true;
            vista2.Visible = false;
            vista3.Visible = false;
            vista4.Visible = false;*/

            //ListadoProcedencias();
            if (!IsPostBack)
            {
                //ListadoDestinatarios();
            }
        }

        private void LimpiarVariablesDeSession()
        {
            ListTiposDocumentos = null;
            ListBeneficiarios = null;
            ListDestinatarios = null;
            ListExpedientesEstatus = null;
            ListExpedientesPorTipoDocumento = null;
            ListTiposPagos = null;
            ListProcedencias = null;
            ListMensajeros = null;
        }

        protected void Page_Init(object sender, EventArgs e)
        {

            txtTipoDocumento.DataSource = ListTiposDocumentos;
            txtTipoDocumento.DataBind();

            txtProcedencia.DataSource = ListProcedencias;
            txtProcedencia.DataBind();

            txtEntregadoPor.DataSource = ListMensajeros;
            txtEntregadoPor.DataBind();

            txtBeneficiario.DataSource = ListBeneficiarios;
            txtBeneficiario.DataBind();

            txtTipoPago.DataSource = ListTiposPagos;
            txtTipoPago.DataBind();

            selectEstatus.DataSource = ListExpedientesEstatus;
            selectEstatus.DataBind();

            selectDestinatario.DataSource = ListDestinatarios;
            selectDestinatario.DataBind();

            grdTiposDocumentos.DataSource = ListTiposDocumentos;
            grdTiposDocumentos.DataBind();

            /*grdConsultarExpedientes.DataSource = PagoExpedientes;
            grdConsultarExpedientes.DataBind();*/

            grdConsultarExpedientes.DataSource = ListExpedientesPorTipoDocumento;
            grdConsultarExpedientes.DataBind();

            if (IsPostBack)
            {
                grdCronologia.DataSource = PagoExpedientesCronologia;
                grdCronologia.DataBind();
            }
        }

        private void ListadoExpedientesRecibidos(bool getData)
        {
            if (getData)
            {
                PagoExpedientesRecibidos = ctrl.ListExpedientesRecibidos(Session["DepartamentoRemitente"].ToString().ToInt());
            }

            grdListadoExpedientesRecibidos.DataSource = PagoExpedientesRecibidos;
            grdListadoExpedientesRecibidos.DataBind();
        }
        private void ListadoExpedientesEnviados(bool getData)
        {
            if (getData)
            {
                PagoExpedientesEnviados = ctrl.ListExpedientesEnviados(Session["DepartamentoRemitente"].ToString());
            }

            grdListEnviados.DataSource = PagoExpedientesEnviados;
            grdListEnviados.DataBind();
            btnGestionar.Visible = false;
        }

        private void ListadoExpedientesEnviadosRecibidos(bool getData)
        {
            if (Session["btnSeccionado"].ToString() == "Enviados")
            {
                if (getData)
                {
                    PagoExpedientesEnviados = ctrl.ListExpedientesEnviados(Session["DepartamentoRemitente"].ToString());
                }
                grdListadoExpedientesRecibidos.DataSource = PagoExpedientesEnviados;
                grdListadoExpedientesRecibidos.DataBind();
                btnGestionar.Visible = false;
            }
            if (Session["btnSeccionado"].ToString() == "Recibidos")
            {
                if (getData)
                {
                    PagoExpedientesRecibidos = ctrl.ListExpedientesRecibidos(Session["DepartamentoRemitente"].ToString().ToInt());
                }
                grdListadoExpedientesRecibidos.DataSource = PagoExpedientesRecibidos;
                grdListadoExpedientesRecibidos.DataBind();
                btnGestionar.Visible = true;
                btnSalvar.Visible = false;
            }
        }



        private void ListadoBeneficiarios()
        {
            ListBeneficiarios = ctrl.ListBeneficiarios();
            txtBeneficiario.DataSource = ListBeneficiarios;
            txtBeneficiario.DataBind();
        }

        private void ListadoExpedientes(bool getData)
        {
            if (getData)
            {
                PagoExpedientes = ctrl.ListExpedientes(Session["DepartamentoRemitente"].ToString().ToInt());
            }
            grdListadoExpedientes1.DataSource = PagoExpedientes;
            grdListadoExpedientes1.DataBind();
        }

        private void ListadoProcedencias()
        {
            ListProcedencias = ctrl.ListProcedencias();
            //grdListProcedencias.DataSource = ListProcedencias;
            //grdListProcedencias.DataBind();

            //selectProcedencia.Items.AddRange(ListProcedencias.Select(j => new ListItem(j.Procedencia, j.IdProcedencia.ToString())).ToArray());
            //selectDestinatario.DataValueField = "IdProcedencia";
            //selectDestinatario.DataTextField = "Procedencia";
            //selectDestinatario.DataBind();


        }
        private void ListadoProcedencias1()
        {
            ListProcedencias = ctrl.ListProcedencias();
            txtProcedencia.DataSource = ListProcedencias;
            txtProcedencia.DataBind();
        }
        private void ListadoMensajeros1()
        {
            ListMensajeros = ctrl.ListMensajeros();
            txtEntregadoPor.DataSource = ListMensajeros;
            txtEntregadoPor.DataBind();
        }
        private void ListadoDestinatarios()
        {
            ListDestinatarios = ctrl.ListDestinatarios(Session["DepartamentoRemitente"].ToString().ToInt());
            selectDestinatario.DataSource = ListDestinatarios;
            selectDestinatario.DataBind();
            // selectDestinatario.Items.Clear();
            //selectDestinatario.Items.Add("");
            // selectDestinatario.Items.AddRange(ListDestinatarios.Select(j => new ListItem(j.Destinatario, j.IdDestinatario.ToString())).ToArray());

        }
        private void ListadoTiposDeDocumentos()
        {
            ListTiposDocumentos = ctrl.ListExpedientesTiposDocumentos();
            //txtTipoDocumento.Items.Clear();
            //txtTipoDocumento.Items.Add("");
            // txtTipoDocumento.Items.AddRange(ListTiposDocumentos.Select(j => new ListItem(j.TipoDocumento, j.IdTipoDocumento.ToString())).ToArray());



            txtTipoDocumento.DataSource = ListTiposDocumentos;
            txtTipoDocumento.DataBind();
        }
        private void ListadoTiposPagos()
        {
            ListTiposPagos = ctrl.ListExpedientesTiposPagos();
            txtTipoPago.DataSource = ListTiposPagos;
            txtTipoPago.DataBind();

            //txtTipoPago.Items.Clear();
            // txtTipoPago.Items.Add("");
            //txtTipoPago.Items.AddRange(ListTiposPagos.Select(j => new ListItem(j.TipoPago, j.IdTipoPago.ToString())).ToArray());

        }
        private void ListadoExepedienteEstatus()
        {
            ListExpedientesEstatus = ctrl.ListExpedientesEstatus(Session["DepartamentoRemitente"].ToString().ToInt(), IdTipoDocumento.Value.ToString().ToInt());
            selectEstatus.DataSource = ListExpedientesEstatus;
            selectEstatus.DataBind();

        }
        private void ListadoMensajeros()
        {
            ListMensajeros = ctrl.ListMensajeros();

        }

        public void OpenRegistro(int IdExpediente)
        {
            UploadControl.Enabled = false;
            btnNuevo.Visible = false;
            btnSalvar.Visible = false;
            btnActualizar.Visible = true;
            idExpediente.Value = IdExpediente.ToString();

            PagoExpediente = PagoExpedientes.FirstOrDefault(x => x.IdExpediente == IdExpediente);

            IdTipoDocumento.Value = PagoExpediente.IdTipoDocumento.ToString();
            //FechaEmision.Value = PagoExpediente.FechaEmision.Date.ToString("yyyy-MM-dd");
            FechaEmision.Value = PagoExpediente.FechaEmision.ToString();
            txtNumeroDocumento.Value = PagoExpediente.NumeroDocumento;
            Asunto.Value = PagoExpediente.Asunto;
            NCF.Value = PagoExpediente.NCF;
            Descripcion.Value = PagoExpediente.Descripcion;
            IdProcedencia.Value = PagoExpediente.IdProcedencia.ToString();
            Procedencia.Value = PagoExpediente.Procedencia;
            IdEntregadoPor.Value = PagoExpediente.IdEntregadoPor.ToString();
            EntregadoPor.Value = PagoExpediente.EntregadoPor;
            //FechaEntrega.Value = PagoExpediente.FechaEntrega.Date.ToString("yyyy-MM-dd");
            FechaEntrega.Value = PagoExpediente.FechaEntrega.ToString();
            HoraEntrega.Value = PagoExpediente.HoraEntrega.ToString();
            IdBeneficiario.Value = PagoExpediente.IdBeneficiario.ToString();
            Beneficiario.Value = PagoExpediente.Beneficiario;
            BeneficiarioCorreo.Text = PagoExpediente.BeneficiarioCorreo;
            IdTipoPago.Value = PagoExpediente.IdTipoPago.ToString();
            ValorFactura.Value = PagoExpediente.ValorFactura.ToString();
            Impuesto.Value = PagoExpediente.Impuesto.ToString();
            Monto.Value = PagoExpediente.Monto.ToString();
            Observacion.Value = PagoExpediente.Observacion;
            fechaRegistro.InnerText = PagoExpediente.FechaRegistro.ToString();
            registradoPor.InnerText = PagoExpediente.RegistradoPor;
            modificadoPor.InnerText = PagoExpediente.ModificadoPor;
            ultimaModificacion.InnerText = PagoExpediente.FechaModifica.ToString();
            labelIdExpediente.InnerText = PagoExpediente.IdExpediente.ToString();

            ultimaUbicacion.InnerText = PagoExpediente.Ubicacion;
            txtProcedencia.Value = PagoExpediente.IdProcedencia.ToString();
            txtEntregadoPor.Value = PagoExpediente.IdEntregadoPor.ToString();
            txtBeneficiario.Value = PagoExpediente.IdBeneficiario.ToString();
            txtTipoDocumento.Value = PagoExpediente.IdTipoDocumento.ToString();
            txtTipoPago.Value = PagoExpediente.IdTipoPago.ToString();
            ultimaUbicacion.InnerHtml = PagoExpediente.LabelUbicacion;
            ultimoEstatus.InnerHtml = PagoExpediente.LabelEstatus;
            txtNumeroRequisicion.Value = PagoExpediente.NumeroRequisicion;


            if (!string.IsNullOrEmpty(IdExpediente.ToString()))
            {
                btnGestionar.Visible = false;
                btnCronologia.Visible = true;
            }
            //if(PagoExpediente.IdTipoDocumento == 2)
            //{
            //divNumeroRequerimiento.Visible = true;
            // }

            PresentarFormulario(true);
            vista2.Visible = true;
            vista1.Visible = false;
            vista3.Visible = false;
            vista4.Visible = false;
            Cronologia(IdExpediente);

            //grdArchivos
            ListarDocumentos = ctrl.ListarArchivosDocumento((PagoExpediente.IdDocumento ?? 0).ToString().ToInt());
            grdArchivos.DataSource = ListarDocumentos;
            grdArchivos.DataBind();
            btnDocumentos.Visible = true;
        }

        public void OpenRegistroDesdeConsulta(int IdExpediente)
        {
            btnDocumentos.Visible = true;
            UploadControl.Visible = false;
            btnNuevo.Visible = false;
            btnSalvar.Visible = false;
            btnActualizar.Visible = true;
            idExpediente.Value = IdExpediente.ToString();

            PagoExpediente = ListExpedientesPorTipoDocumento.FirstOrDefault(x => x.IdExpediente == IdExpediente);

            IdTipoDocumento.Value = PagoExpediente.IdTipoDocumento.ToString();
            //FechaEmision.Value = PagoExpediente.FechaEmision.Date.ToString("yyyy-MM-dd");
            FechaEmision.Value = PagoExpediente.FechaEmision.ToString();
            txtNumeroDocumento.Value = PagoExpediente.NumeroDocumento;
            Asunto.Value = PagoExpediente.Asunto;
            NCF.Value = PagoExpediente.NCF;
            Descripcion.Value = PagoExpediente.Descripcion;
            IdProcedencia.Value = PagoExpediente.IdProcedencia.ToString();
            Procedencia.Value = PagoExpediente.Procedencia;
            IdEntregadoPor.Value = PagoExpediente.IdEntregadoPor.ToString();
            EntregadoPor.Value = PagoExpediente.EntregadoPor;
            //FechaEntrega.Value = PagoExpediente.FechaEntrega.Date.ToString("yyyy-MM-dd");
            FechaEntrega.Value = PagoExpediente.FechaEntrega.ToString();
            HoraEntrega.Value = PagoExpediente.HoraEntrega.ToString();
            IdBeneficiario.Value = PagoExpediente.IdBeneficiario.ToString();
            Beneficiario.Value = PagoExpediente.Beneficiario;
            BeneficiarioCorreo.Text = PagoExpediente.BeneficiarioCorreo;
            IdTipoPago.Value = PagoExpediente.IdTipoPago.ToString();
            ValorFactura.Value = PagoExpediente.ValorFactura.ToString();
            Impuesto.Value = PagoExpediente.Impuesto.ToString();
            Monto.Value = PagoExpediente.Monto.ToString();
            Observacion.Value = PagoExpediente.Observacion;
            fechaRegistro.InnerText = PagoExpediente.FechaRegistro.ToString();
            registradoPor.InnerText = PagoExpediente.RegistradoPor;
            modificadoPor.InnerText = PagoExpediente.ModificadoPor;
            ultimaModificacion.InnerText = PagoExpediente.FechaModifica.ToString();
            labelIdExpediente.InnerText = PagoExpediente.IdExpediente.ToString();

            ultimaUbicacion.InnerText = PagoExpediente.Ubicacion;
            txtProcedencia.Value = PagoExpediente.IdProcedencia.ToString();
            txtEntregadoPor.Value = PagoExpediente.IdEntregadoPor.ToString();
            txtBeneficiario.Value = PagoExpediente.IdBeneficiario.ToString();
            txtTipoDocumento.Value = PagoExpediente.IdTipoDocumento.ToString();
            txtTipoPago.Value = PagoExpediente.IdTipoPago.ToString();
            ultimaUbicacion.InnerHtml = PagoExpediente.LabelUbicacion;
            ultimoEstatus.InnerHtml = PagoExpediente.LabelEstatus;
            txtNumeroRequisicion.Value = PagoExpediente.NumeroRequisicion;

            if (!string.IsNullOrEmpty(IdExpediente.ToString()))
            {
                btnGestionar.Visible = false;
                btnCronologia.Visible = true;
            }

            PresentarFormulario(true);
            vista2.Visible = true;
            vista1.Visible = false;
            vista3.Visible = false;
            vista4.Visible = false;
            btnActualizar.Visible = false;
            Cronologia(IdExpediente);


        }

        public void OpenRegistroRecibido(int IdRegistro)
        {
            btnDocumentos.Visible = true;
            UploadControl.Enabled = true;
            Seccion.InnerText = "Recibidos";

            btnActualizar.Visible = false;
            //Session["btnSeccionado"] = "Recibidos";

            btnGestionar.Visible = true;
            PagoExpedienteRecibido = PagoExpedientesRecibidos.FirstOrDefault(x => x.IdRegistro == IdRegistro);
            //PagoExpedienteRecibido.IdRegistro = IdRegistro;
            idExpediente.Value = PagoExpedienteRecibido.IdExpediente.ToString();
            txtTipoDocumento.Value = PagoExpedienteRecibido.IdTipoDocumento.ToString();
            FechaEmision.Value = PagoExpedienteRecibido.FechaEmision.ToString();
            txtNumeroDocumento.Value = PagoExpedienteRecibido.NumeroDocumento;
            Asunto.Value = PagoExpedienteRecibido.Asunto;
            NCF.Value = PagoExpedienteRecibido.NCF;
            Descripcion.Value = PagoExpedienteRecibido.Descripcion;
            txtProcedencia.Value = PagoExpedienteRecibido.IdProcedencia.ToString();
            Procedencia.Value = PagoExpedienteRecibido.Procedencia;
            txtEntregadoPor.Value = PagoExpedienteRecibido.IdEntregadoPor.ToString();
            EntregadoPor.Value = PagoExpedienteRecibido.EntregadoPor;
            FechaEntrega.Value = PagoExpedienteRecibido.FechaEntrega.ToString();
            HoraEntrega.Value = PagoExpedienteRecibido.HoraEntrega.ToString();
            txtBeneficiario.Value = PagoExpedienteRecibido.IdBeneficiario.ToString();
            Beneficiario.Value = PagoExpedienteRecibido.Beneficiario;
            BeneficiarioCorreo.Value = PagoExpedienteRecibido.BeneficiarioCorreo;
            txtTipoPago.Value = PagoExpedienteRecibido.IdTipoPago.ToString();
            ValorFactura.Value = PagoExpedienteRecibido.ValorFactura.ToString();
            Impuesto.Value = PagoExpedienteRecibido.Impuesto.ToString();
            Monto.Value = PagoExpedienteRecibido.Monto.ToString();
            Observacion.Value = PagoExpedienteRecibido.Observacion;
            fechaRegistro.InnerText = PagoExpedienteRecibido.FechaRegistro.ToString();
            registradoPor.InnerText = PagoExpedienteRecibido.RegistradoPor;
            modificadoPor.InnerText = PagoExpedienteRecibido.ModificadoPor;
            ultimaModificacion.InnerText = PagoExpedienteRecibido.FechaModifica.ToString();
            labelIdExpediente.InnerText = PagoExpedienteRecibido.NumeroExpediente.ToString();//idExpediente.Value;
            ultimaUbicacion.InnerHtml = PagoExpedienteRecibido.LabelUbicacion;
            ultimoEstatus.InnerHtml = PagoExpedienteRecibido.LabelEstatus;

            RegistroSeleccionado.InnerText = PagoExpedienteRecibido.IdExpediente.ToString();
            EstatusActual.InnerText = PagoExpedienteRecibido.LabelEstatus;
            UltimaFecha.InnerText = PagoExpedienteRecibido.FechaEnviado.ToString();
            txtNumeroRequisicion.Value = PagoExpedienteRecibido.NumeroRequisicion;

            ListadoDestinatarios();
            btnCronologia.Visible = true;
            PresentarFormulario(true);
            vista2.Visible = true;
            vista1.Visible = false;
            vista3.Visible = false;
            vista4.Visible = false;

            ListExpedientesEstatus = ctrl.ListExpedientesEstatus(Session["DepartamentoRemitente"].ToString().ToInt(), txtTipoDocumento.Value.ToString().ToInt());
            selectEstatus.DataSource = ListExpedientesEstatus;
            selectEstatus.DataBind();
            Cronologia(idExpediente.Value.ToString().ToInt());

            ListarDocumentos = ctrl.ListarArchivosDocumento((PagoExpedienteRecibido.IdDocumento ?? 0).ToString().ToInt());
            grdArchivos.DataSource = ListarDocumentos;
            grdArchivos.DataBind();

            //cbDocumentos_Callback(null, null);
            btnNuevo.Visible = true;
        }

        public void OpenRegistroEnviado(int IdRegistro)
        {
            btnDocumentos.Visible = true;
            UploadControl.Enabled = false;
            Seccion.InnerText = "Enviados";

            btnActualizar.Visible = false;
            //Session["btnSeccionado"] = "Recibidos";

            btnGestionar.Visible = false;
            PagoExpedienteEnviado = PagoExpedientesEnviados.FirstOrDefault(x => x.IdRegistro == IdRegistro);
            idExpediente.Value = PagoExpedienteEnviado.IdExpediente.ToString();
            txtTipoDocumento.Value = PagoExpedienteEnviado.IdTipoDocumento.ToString();
            FechaEmision.Value = PagoExpedienteEnviado.FechaEmision.ToString();
            txtNumeroDocumento.Value = PagoExpedienteEnviado.NumeroDocumento;
            Asunto.Value = PagoExpedienteEnviado.Asunto;
            NCF.Value = PagoExpedienteEnviado.NCF;
            Descripcion.Value = PagoExpedienteEnviado.Descripcion;
            txtProcedencia.Value = PagoExpedienteEnviado.IdProcedencia.ToString();
            //Procedencia.Value = PagoExpedienteEnviado.Procedencia;
            txtEntregadoPor.Value = PagoExpedienteEnviado.IdEntregadoPor.ToString();
            // EntregadoPor.Value = PagoExpedienteEnviado.EntregadoPor;
            FechaEntrega.Value = PagoExpedienteEnviado.FechaEntrega.ToString();
            HoraEntrega.Value = PagoExpedienteEnviado.HoraEntrega.ToString();
            txtBeneficiario.Value = PagoExpedienteEnviado.IdBeneficiario.ToString();
            //Beneficiario.Value = PagoExpedienteEnviado.Beneficiario;
            BeneficiarioCorreo.Value = PagoExpedienteEnviado.BeneficiarioCorreo;
            txtTipoPago.Value = PagoExpedienteEnviado.IdTipoPago.ToString();
            ValorFactura.Value = PagoExpedienteEnviado.ValorFactura.ToString();
            Impuesto.Value = PagoExpedienteEnviado.Impuesto.ToString();
            Monto.Value = PagoExpedienteEnviado.Monto.ToString();
            Observacion.Value = PagoExpedienteEnviado.Observacion;
            fechaRegistro.InnerText = PagoExpedienteEnviado.FechaRegistro.ToString();
            registradoPor.InnerText = PagoExpedienteEnviado.RegistradoPor;
            modificadoPor.InnerText = PagoExpedienteEnviado.ModificadoPor;
            ultimaModificacion.InnerText = PagoExpedienteEnviado.FechaModifica.ToString();
            labelIdExpediente.InnerText = idExpediente.Value;
            ultimaUbicacion.InnerHtml = PagoExpedienteEnviado.LabelUbicacion;
            ultimoEstatus.InnerHtml = PagoExpedienteEnviado.LabelEstatus;
            txtNumeroRequisicion.Value = PagoExpedienteEnviado.NumeroRequisicion;

            ListadoDestinatarios();
            btnCronologia.Visible = true;
            //PresentarFormulario(true);
            vista2.Visible = true;
            vista1.Visible = false;
            vista3.Visible = false;
            vista4.Visible = false;
            Cronologia(idExpediente.Value.ToString().ToInt());

            ListarDocumentos = ctrl.ListarArchivosDocumento((PagoExpedienteEnviado.IdDocumento ?? 0).ToString().ToInt());
            grdArchivos.DataSource = ListarDocumentos;
            grdArchivos.DataBind();
            btnNuevo.Visible = true;
        }

        public void SeleccionarProcedencia(int idProcedencia)
        {

            //IdProcedencia.Value = idProcedencia.ToString();

            //ProcedenciaSeleccionada = ListProcedencias.FirstOrDefault(x => x.IdProcedencia == idProcedencia);


            // Procedencia.Value = ProcedenciaSeleccionada.Procedencia;
            //ScriptManager.RegisterStartupScript(this, this.GetType(), "Pop", "OcultarModals()", true);
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Pop", "$('#Modal-procedencias').modal('hide');", true);
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Pop", "$('.modal-backdrop').removeClass('in')", true);
        }



        protected void cbPrincipal_Callback(object sender, DevExpress.Web.CallbackEventArgsBase e)
        {
            string[] parametro = e.Parameter.Split('|');

            int IdExpediente = 0;
            int IdProcedencia = 0;
            int IdRegistro = 0;
            int IdTipoDocumento = 0;

            /*
            ListadoDestinatarios();
            ListadoBeneficiarios();
            ListadoProcedencias1();
            ListadoMensajeros1();
            ListadoTiposDeDocumentos();
            ListadoTiposPagos();
            ListadoExepedienteEstatus();*/

            switch (parametro[0])
            {


                case "EXPEDIENTES-REGISTRADOS":
                    vista1.Visible = false;
                    vista3.Visible = true;
                    vista2.Visible = false;
                    vista4.Visible = false;
                    vistaConsultarExpedientes.Visible = false;
                    //ListadoExpedientes(true);
                    SeccionSeleccionada.InnerHtml = " EXPEDIENTES REGISTRADOS";
                    Session["btnSeccionado"] = "Registros";
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnNuevo.Visible = true;
                    break;

                case "EXPEDIENTES-RECIBIDOS":
                    PagoExpedientesRecibidos = ctrl.ListExpedientesRecibidos(Session["DepartamentoRemitente"].ToString().ToInt());
                    grdListadoExpedientesRecibidos.DataSource = PagoExpedientesRecibidos;
                    grdListadoExpedientesRecibidos.DataBind();

                    btnSalvar.Visible = false;
                    btnNuevo.Visible = false;
                    vista1.Visible = true;
                    vista3.Visible = false;
                    vista2.Visible = false;
                    vista4.Visible = false;
                    vistaConsultarExpedientes.Visible = false;
                    //ListadoExpedientesRecibidos(true); 
                    SeccionSeleccionada.InnerHtml = " EXPEDIENTES RECIBIDOS";
                    Session["btnSeccionado"] = "Recibidos";
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    break;

                case "EXPEDIENTES-ENVIADOS":
                    PagoExpedientesEnviados = ctrl.ListExpedientesEnviados(Session["DepartamentoRemitente"].ToString());
                    grdListEnviados.DataSource = PagoExpedientesEnviados;
                    grdListEnviados.DataBind();

                    btnNuevo.Visible = false;
                    btnSalvar.Visible = false;
                    vista1.Visible = false;
                    vista3.Visible = false;
                    vista2.Visible = false;
                    vista4.Visible = true;
                    //ListadoExpedientesRecibidos(true); 
                    SeccionSeleccionada.InnerHtml = " EXPEDIENTES ENVIADOS";
                    Session["btnSeccionado"] = "Enviados";
                    vistaConsultarExpedientes.Visible = false;
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    break;

                case "ABRIR":
                    vistaConsultarExpedientes.Visible = false;
                    if (parametro.Length > 1)
                    {
                        int.TryParse(parametro[1].ToString(), out IdExpediente);
                        vista1.Visible = false;
                        //vista2.Visible = true;

                    }
                    OpenRegistro(IdExpediente);
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    break;

                case "ABRIR-DESDECONSULTA":
                    if (parametro.Length > 1)
                    {
                        int.TryParse(parametro[1].ToString(), out IdExpediente);
                        vista1.Visible = false;
                        //vista2.Visible = true;

                    }
                    //
                    vista2.Visible = true;

                    labelIdExpediente.InnerText = IdExpediente.ToString();
                    OpenRegistroDesdeConsulta(IdExpediente);
                    vistaConsultarExpedientes.Visible = false;
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    break;

                case "CERRAR":
                    //PresentarFormulario();
                    vista2.Visible = false;

                    if (Session["btnSeccionado"] == null)
                    {
                        vista3.Visible = false;
                        //ListadoExpedientesRecibidos(true);
                        btnNuevo.Visible = false;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        break;
                    }

                    switch (Session["btnSeccionado"].ToString())
                    {
                        case "Recibidos":
                            vista3.Visible = false;
                            vista4.Visible = false;
                            vista1.Visible = true;
                            vista2.Visible = false;
                            vistaConsultarExpedientes.Visible = false;
                            //ListadoExpedientesRecibidos(true);
                            btnNuevo.Visible = false;
                            btnSalvar.Visible = false;
                            btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                            btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            break;

                        case "Registros":
                            vista1.Visible = false;
                            vista3.Visible = true;
                            vista4.Visible = false;
                            btnNuevo.Visible = true;
                            vistaConsultarExpedientes.Visible = false;
                            // ListadoExpedientes(false);
                            btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                            btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            break;

                        case "Enviados":

                            btnNuevo.Visible = false;
                            btnSalvar.Visible = false;
                            vista3.Visible = false;
                            vista4.Visible = true;
                            vista1.Visible = false;
                            vista2.Visible = false;
                            vistaConsultarExpedientes.Visible = false;
                            // ListadoExpedientesEnviados(true);
                            btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                            btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            break;

                        case "Consulta":
                            btnNuevo.Visible = false;
                            vista3.Visible = false;
                            vista4.Visible = false;
                            vista1.Visible = false;
                            vista2.Visible = false;
                            vistaConsultarExpedientes.Visible = true;
                            // ListadoExpedientesEnviados(true);
                            btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                            break;
                    }
                    /*
                    if (Session["btnSeccionado"].ToString() == "Recibidos")
                    {
                        vista3.Visible = false;
                        //ListadoExpedientesRecibidos(true);
                        btnNuevo.Visible = false;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;                        
                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    }*/
                    /*
                    if (Session["btnSeccionado"].ToString() == "Registros")
                    {
                        vista1.Visible = false;
                        vista3.Visible = true;
                        vista4.Visible = false;
                        // ListadoExpedientes(false);
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    }*/
                    /*
                    if (Session["btnSeccionado"].ToString() == "Enviados")
                    {
                        btnNuevo.Visible = false;
                        vista3.Visible = false;
                        // ListadoExpedientesEnviados(true);
                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                       
                    }*/
                    break;



                case "SELECCIONAR_PROCEDENCIA":
                    if (parametro.Length > 1)
                    {
                        int.TryParse(parametro[1].ToString(), out IdProcedencia);
                        // vista1.Visible = false;
                        //vista2.Visible = true;
                    }
                    SeleccionarProcedencia(IdProcedencia);
                    break;
                case "ABRIR-RECIBIDO":
                    vistaConsultarExpedientes.Visible = false;
                    btnNuevo.Visible = false;
                    int.TryParse(parametro[1].ToString(), out IdRegistro);
                    OpenRegistroRecibido(IdRegistro);
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;

                    break;

                case "ABRIR-ENVIADO":
                    vistaConsultarExpedientes.Visible = false;
                    btnNuevo.Visible = false;
                    int.TryParse(parametro[1].ToString(), out IdRegistro);
                    OpenRegistroEnviado(IdRegistro);
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;

                    break;

                case "NUEVO-EXPEDIENTE":
                    vistaConsultarExpedientes.Visible = false;
                    Session["Default_PagoExpediente"] = null;
                    // ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Pop", "alert();", true);

                    vista1.Visible = false;
                    vista2.Visible = true;
                    btnSalvar.Visible = true;
                    btnNuevo.Visible = false;
                    btnDocumentos.Visible = false;

                    DateTime fechaActual = DateTime.Today.Date;

                    FechaEntrega.Value = DateTime.Today.Date.ToString();
                    HoraEntrega.Text = fechaActual.ToString();
                    // ScriptManager.RegisterStartupScript(this, this.GetType(), "Pop", "LimpiarCampos;", true);
                    //ScriptManager.RegisterStartupScript(this, typeof(Page), "popup", script, true);
                    /*ListadoDestinatarios();
                    ListadoProcedencias1();
                    ListadoMensajeros1();
                    ListadoTiposDeDocumentos();
                    ListadoTiposPagos();
                    ListadoExepedienteEstatus();*/

                    break;

                case "PROCEDENCIAS":
                    vista1.Visible = false;
                    vista2.Visible = true;
                    btnSalvar.Visible = true;
                    btnNuevo.Visible = false;
                    ListadoProcedencias();
                    break;

                case "SALVAR":
                    btnNuevo.Visible = false;
                    btnSalvar.Visible = false;
                    vista2.Visible = false;
                    vista1.Visible = false;
                    vista3.Visible = false;
                    vista4.Visible = false;
                    SalvarExpediente1();
                    break;

                case "ACTUALIZAR":
                    vistaConsultarExpedientes.Visible = false;
                    ActualizarExpediente();

                    //ModalActualizarRegistro.ShowHeader = true;
                    //ModalActualizarRegistro.HeaderText = "Actualizando registro";
                    //ModalActualizarRegistro.PopupVerticalAlign = DevExpress.Web.PopupVerticalAlign.WindowCenter;
                    //ModalActualizarRegistro.PopupHorizontalAlign = DevExpress.Web.PopupHorizontalAlign.WindowCenter;


                    //ModalActualizarRegistro.Height = 200;
                    break;

                case "ENVIAR":
                    EstatusActual.InnerHtml = PagoExpedienteRecibido.LabelEstatus;
                    UltimaFecha.InnerText = PagoExpedienteRecibido.FechaEnviado.ToString();

                    Enviar();

                    vista2.Visible = false;

                    if (Session["btnSeccionado"].ToString() == "Recibidos")
                    {
                        vista3.Visible = false;
                        vista1.Visible = true;
                        vistaConsultarExpedientes.Visible = false;
                        ListadoExpedientesRecibidos(true);
                        ListadoExpedientesEnviados(true);
                        btnNuevo.Visible = false;
                        //grdListEnviados.DataSource = PagoExpedientesEnviados;
                        // grdListEnviados.DataBind();
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    }
                    if (Session["btnSeccionado"].ToString() == "Registros")
                    {
                        vista1.Visible = false;
                        vista3.Visible = true;
                        ListadoExpedientes(false);
                        ListadoExpedientesRecibidos(true);
                        ListadoExpedientesEnviados(true);
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                        btnNuevo.Visible = true;
                    }

                    break;

                case "CONSULTAR-EXPEDIENTES":
                    SeccionSeleccionada.InnerHtml = " CONSULTAR EXPEDIENTES";
                    vistaConsultarExpedientes.Visible = true;
                    vista1.Visible = false;
                    vista2.Visible = false;
                    vista3.Visible = false;
                    vista4.Visible = false;
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;

                    ListTiposDocumentos = ctrl.ListExpedientesTiposDocumentos();
                    grdTiposDocumentos.DataSource = ListTiposDocumentos;
                    grdTiposDocumentos.DataBind();

                    /*ListExpedientesPorTipoDocumento = ctrl.ListExpedientesPorTipoDocumento(1);
                    grdConsultarExpedientes.DataSource = ListExpedientesPorTipoDocumento;
                    grdConsultarExpedientes.DataBind();*/
                    break;

                case "ABRIR-EXPEDIENTE-POR-TIPO":
                    if (parametro.Length > 1)
                    {
                        int.TryParse(parametro[1].ToString(), out IdTipoDocumento);
                        //vista1.Visible = false;
                        //vista2.Visible = true;
                        vista1.Visible = false;
                        vista2.Visible = false;
                        vista3.Visible = false;
                        vista4.Visible = false;
                        vistaConsultarExpedientes.Visible = true;
                        string labelDocumentoSeleccionado = "Facturas";


                        if (IdTipoDocumento == 1)
                        {
                            grdConsultarExpedientes.Columns["gcBalance"].Visible = true;

                            grdConsultarExpedientes.Columns["gcBeneficiario"].Visible = true;
                            grdConsultarExpedientes.Columns["gcRNC"].Visible = true;
                            grdConsultarExpedientes.Columns["gcNCF"].Visible = true;
                            grdConsultarExpedientes.Columns["IdCxP"].Visible = true;
                        }

                        if (IdTipoDocumento == 2)
                        {
                            labelDocumentoSeleccionado = "Requisición de Compras";
                            grdConsultarExpedientes.Columns["gcBalance"].Visible = false;
                            grdConsultarExpedientes.Columns["gcBeneficiario"].Visible = false;
                            grdConsultarExpedientes.Columns["gcRNC"].Visible = false;
                            grdConsultarExpedientes.Columns["gcNCF"].Visible = false;
                            grdConsultarExpedientes.Columns["IdCxP"].Visible = false;
                        }


                        TituloDocumentoSeleccionado.InnerHtml = labelDocumentoSeleccionado;

                        ListExpedientesPorTipoDocumento = ctrl.ListExpedientesPorTipoDocumento(IdTipoDocumento);
                        grdConsultarExpedientes.DataSource = ListExpedientesPorTipoDocumento;
                        grdConsultarExpedientes.DataBind();

                        btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                        btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;


                    }
                    break;

                case "CARGAR_ARCHIVO":
                    break;
            }

        }
        private void PresentarFormulario(bool ver = false)
        {
            if (ver)
            {
                vista2.Visible = ver;
                vista1.Visible = false;
            }
        }

        protected void btNuevoExpediente(object sender, EventArgs e)
        {
            FechaEmision.Value = DateTime.Today.Date;
            FechaEntrega.Value = DateTime.Today.Date;
            HoraEntrega.Value = DateTime.Now.ToString("HH:mm:ss");
            Session["Default_PagoExpediente"] = null;

            vista1.Visible = false;
            vista2.Visible = true;
            vista3.Visible = false;
            vista4.Visible = false;
            btnSalvar.Visible = true;
            btnNuevo.Visible = false;
            btnGestionar.Visible = false;
            vistaConsultarExpedientes.Visible = false;
            btnDocumentos.Visible = false;
            // ScriptManager.RegisterStartupScript(this, this.GetType(), "Pop", "LimpiarCampos();", true);

            /*
            ListadoDestinatarios();
            ListadoProcedencias1();
            ListadoMensajeros1();
            ListadoTiposDeDocumentos();
            ListadoTiposPagos();
            ListadoExepedienteEstatus();*/
            btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
            btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
            btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
            //ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Pop", "LimpiarCampos();", true);


        }
        protected void btn_Click_OcultarFormulario(object sender, EventArgs e)
        {

            //vista2.Visible = false;

            btnSalvar.Visible = false;
            btnNuevo.Visible = true;

            ScriptManager.RegisterStartupScript(this, this.GetType(), "Pop", "LimpiarCampos();", true);

            if (Session["btnSeccionado"].ToString() == "Recibidos")
            {
                vista3.Visible = false;
                ListadoExpedientesRecibidos(true);
                btnNuevo.Visible = false;
            }
            if (Session["btnSeccionado"].ToString() == "Registros")
            {
                vista3.Visible = true;
                ListadoExpedientes(false);

            }
            if (Session["btnSeccionado"].ToString() == "Enviados")
            {
                btnNuevo.Visible = false;
                btnSalvar.Visible = false;
                vista3.Visible = false;
                ListadoExpedientesEnviados(true);

            }

        }
        //public void Salvar()
        //{

        //    ctrl.SalvarExpediente(PagoExpediente);

        //}
        //public void SalvarExpediente(object sender, EventArgs e)
        //{


        //    if (ValidAndBinding())
        //    {
        //        ctrl.SalvarExpediente(PagoExpediente);


        //        //ScriptManager.RegisterStartupScript(this, this.GetType(), "Pop", "ModalSave();", true);
        //        ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Pop", "ModalSave();", true);
        //        //vista2.Visible = false;
        //        vista3.Visible = true;

        //        btnSalvar.Visible = false;
        //        btnActualizar.Visible = false;
        //        btnNuevo.Visible = true;
        //        ListadoExpedientes(true);
        //    }
        //    else
        //    {
        //        ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Pop", "openModal();", true);
        //        // ScriptManager.RegisterStartupScript(this, this.GetType(), "Pop", "openModal();", true);


        //        btnSalvar.Visible = true;
        //        vista2.Visible = true;
        //        vista1.Visible = false;
        //    }

        //    //Session["Default_PagoExpediente"] = null;

        //}

        public void SalvarExpediente1()
        {
            if (ValidAndBinding())
            {

                Resultado resultado = ctrl.SalvarExpediente(PagoExpediente);
                if (resultado.TodoBien)
                {
                    vista3.Visible = true;
                    btnSalvar.Visible = false;
                    btnActualizar.Visible = false;
                    btnNuevo.Visible = true;
                    msjSalvado.InnerText = "El registro se ha salvado correctamente. Expediente No.: " + resultado.ID.ToString();
                    ModalSalvarRegistro.ShowOnPageLoad = true;

                    // grdListadoExpedientes1.DataSource = PagoExpedientes;
                    //grdListadoExpedientes1.DataBind();
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    //Enviar(resultado.ID.ToString().ToInt());

                    //Enviar


                    ListadoExpedientes(true);

                    AutoEnviar(resultado.ID.ToString().ToInt());
                    //labelIdExpediente.InnerHtml = resultado.ID.ToString();
                    //vista2.Visible = true;
                    //PagoExpediente.IdExpediente = resultado.ID;
                    //if (labelIdExpediente.InnerHtml != "")
                    //{

                    // }
                    //ctrl.EnviarExpediente(ExpedienteEnviado);
                    //ctrl.ActualizarEstatusRegistroEnviado(ExpedienteEnviado);
                }
                else
                {
                    msjSalvado.InnerText = "Error guardando el expediente. " + resultado.strError;
                    ModalSalvarRegistro.ShowOnPageLoad = true;

                    btnSalvar.Visible = true;
                    vista2.Visible = true;
                    vista1.Visible = false;
                }
            }
            else
            {
                btnSalvar.Visible = true;
                vista2.Visible = true;
                vista1.Visible = false;
            }

        }
        public void ActualizarExpediente()
        {

            if (ValidAndBinding())
            {

                Resultado resultado = ctrl.ActualizarExpediente(PagoExpediente);
                //vista2.Visible = true;
                //btnActualizar.Visible = true;
                //OpenRegistro(PagoExpediente.IdExpediente);

                //vista2.Visible = false;

                if (resultado.TodoBien)
                {
                    btnSalvar.Visible = false;
                    btnActualizar.Visible = false;
                    btnNuevo.Visible = true;
                    ListadoExpedientes(true);
                    //MessageBox.Show("Actualizando");
                    vista1.Visible = false;
                    vista2.Visible = true;
                    vistaConsultarExpedientes.Visible = false;

                    //vista3.Visible = true;
                    OpenRegistro(PagoExpediente.IdExpediente);
                    modalMensajeActualizar.InnerText = "El registro se ha actualizado correctamente. Expediente No.: " + PagoExpediente.IdExpediente;
                    ModalActualizarRegistro.ShowOnPageLoad = true;
                }

                //labelModalIdExpediente.inn = PagoExpediente.IdExpediente;

            }
            else
            {

                btnSalvar.Visible = false;
                vista2.Visible = true;
                btnActualizar.Visible = true;
                btnNuevo.Visible = false;

                ListadoExpedientes(true);
            }
            // Session["Default_PagoExpediente"] = null;
        }
        public void Actualizar(object sender, EventArgs e)
        {



            if (ValidAndBinding())
            {


                ctrl.ActualizarExpediente(PagoExpediente);
                //vista2.Visible = true;
                //btnActualizar.Visible = true;
                //OpenRegistro(PagoExpediente.IdExpediente);

                //vista2.Visible = false;
                vista1.Visible = true;

                btnSalvar.Visible = false;
                btnActualizar.Visible = false;
                btnNuevo.Visible = true;
                ListadoExpedientes(true);
                //MessageBox.Show("Actualizando");
                vista1.Visible = false;
                vista3.Visible = true;
                OpenRegistro(PagoExpediente.IdExpediente);
            }
            else
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "Pop", "openModal();", true);
                btnSalvar.Visible = true;
                vista2.Visible = true;

                ListadoExpedientes(true);
            }
            // Session["Default_PagoExpediente"] = null;
        }
        private bool ValidAndBinding()
        {


            bool todobien = true;
            byte.TryParse((txtTipoDocumento.Value ?? "").ToString(), out byte numIdTipoDocumento);
            if (numIdTipoDocumento <= 0)
            {
                txtTipoDocumento.IsValid = false;
                txtTipoDocumento.ErrorText = "Debe llenar este campo";
                todobien = false;
            }
            else
            {
                if (numIdTipoDocumento == 1)
                {


                    if (txtNumeroRequisicion.Value == null)
                    {
                        txtNumeroRequisicion.IsValid = false;
                        txtNumeroRequisicion.ErrorText = "Debe llenar este campo";
                        todobien = false;
                    }
                }
                else
                {
                    txtNumeroRequisicion.Value = null;
                }
            }


            DateTime.TryParse((FechaEmision.Value ?? "").ToString(), out DateTime fecFechaEmision);
            if (fecFechaEmision == new DateTime(1, 1, 1))
            {
                FechaEmision.IsValid = false;
                FechaEmision.ErrorText = "Debe seleccionar la fecha";
                todobien = false;

            }
            if (txtNumeroDocumento.Value == null)
            {
                txtNumeroDocumento.IsValid = false;
                txtNumeroDocumento.ErrorText = "Debe escribir el número del documento";
                todobien = false;
            }

            if (string.IsNullOrEmpty((Asunto.Value ?? "").ToString()))
            {
                Asunto.IsValid = false;
                Asunto.ErrorText = "Debe agregar el asunto sobre que trata el expediente";
                todobien = false;
            }

            if (numIdTipoDocumento == 1)
            {
                if (string.IsNullOrEmpty((NCF.Value ?? "").ToString()))
                {
                    NCF.IsValid = false;
                    NCF.ErrorText = "Debe llenar el comprobante fiscal";
                    todobien = false;
                }
                else
                {
                    if (!NCF.Value.ToString().IsValidNCF())
                    {
                        NCF.IsValid = false;
                        NCF.ErrorText = "El Número de comprobante fiscal es inválido";
                        todobien = false;
                    }
                }
            }
            else
            {
                NCF.IsValid = true;
                NCF.Text = "";

                // NumeroRequisicion.Value = null;
            }


            int.TryParse((txtProcedencia.Value ?? "").ToString(), out int numIdProcedencia);
            int.TryParse((txtEntregadoPor.Value ?? "").ToString(), out int numIdEntregadoPor);

            DateTime.TryParse((FechaEntrega.Value ?? "").ToString(), out DateTime fecFechaEntrega);
            if (fecFechaEntrega == new DateTime(1, 1, 1))
            {
                FechaEntrega.IsValid = false;
                FechaEntrega.ErrorText = "Debe seleccionar la fecha";
                todobien = false;

            }
            TimeSpan.TryParse((HoraEntrega.Value ?? "").ToString(), out TimeSpan timHoraEntrega);
            if (timHoraEntrega == new TimeSpan(1, 1, 1))
            {
                FechaEntrega.IsValid = false;
                FechaEntrega.ErrorText = "Debe seleccionar la hora en la que se entregó el documento físicamente";
                todobien = false;

            }

            //string vf = ValorFactura.Text.ToString().Replace(",", "");

            //ValorFactura.Value = vf;
            Monto.Number = ValorFactura.Number + Impuesto.Number;

            decimal.TryParse((Monto.Number).ToString(), out decimal numMonto);
            decimal.TryParse((TxtIdUsuario.Value ?? "").ToString(), out decimal numIdUsuario);

            /*
            if (Impuesto.Text.ToString() == "0.00")
            {
                

                if (numIdTipoDocumento == 2)
                {
                    PagoExpediente.Impuesto = null;
                }
                else
                {
                    Impuesto.IsValid = false;
                    Impuesto.ErrorText = "Este campo no puede esta vacío";
                    todobien = false;
                }
                
            }
            else 
            {
                decimal.TryParse((Impuesto.Number).ToString(), out decimal numImpuesto);
                PagoExpediente.Impuesto = numImpuesto;
            }*/
            /*
            if (ValorFactura.Text.ToString() == "0.00")
            {

                if (numIdTipoDocumento == 2)
                {
                    PagoExpediente.ValorFactura = 0;

                }
                else
                {
                    ValorFactura.IsValid = false;
                    ValorFactura.ErrorText = "Este campo no puede esta vacío";
                    todobien = false;
                }
            }
            else
            {
                decimal.TryParse((ValorFactura.Value ?? "").ToString(), out decimal numValorFactura);
                PagoExpediente.ValorFactura = numValorFactura;
            }*/



            decimal.TryParse((Impuesto.Number).ToString(), out decimal numImpuesto);
            decimal.TryParse((ValorFactura.Value ?? "").ToString(), out decimal numValorFactura);



            /*if (txtNumeroRequisicion.Value == null)
            {
                txtNumeroRequisicion.Value = null;
            }



            decimal.TryParse((txtNumeroRequisicion.Value?.ToString() ?? "0").ToString(), out decimal numRequisicion);
            */
            if (PagoExpediente == null)
            {
                PagoExpediente = new CxP_PagosExpedientes();
            }


            //

            if (string.IsNullOrEmpty(txtBeneficiario.Text))
            {
                if (numIdTipoDocumento == 2)
                {
                    PagoExpediente.IdBeneficiario = null;
                }
                else
                {
                    txtBeneficiario.IsValid = false;
                    txtBeneficiario.ErrorText = "Debe seleccionar un Suplidor o Beneficiario";
                    todobien = false;
                }
            }
            else
            {
                int.TryParse((txtBeneficiario.Value ?? "").ToString(), out int numIdBeneficiario);
                PagoExpediente.IdBeneficiario = numIdBeneficiario;
            }

            if (string.IsNullOrEmpty(txtTipoPago.Text))
            {

                if (numIdTipoDocumento == 2)
                {
                    PagoExpediente.IdTipoPago = null;
                }
                else
                {
                    txtTipoPago.IsValid = false;
                    txtTipoPago.ErrorText = "Debe seleccionar el tipo de pago que aplica con esta factura";
                    todobien = false;
                }
            }
            else
            {
                byte.TryParse((txtTipoPago.Value ?? "").ToString(), out byte numIdTipoPago);
                PagoExpediente.IdTipoPago = numIdTipoPago;
            }

            /*if (string.IsNullOrEmpty(NumeroRequisicion.Text))
            {
                PagoExpediente.NumeroRequisicion = null;
            }*/

            PagoExpediente.IdTipoDocumento = numIdTipoDocumento;
            PagoExpediente.FechaEmision = fecFechaEmision;

            PagoExpediente.NumeroDocumento = txtNumeroDocumento.Text;

            PagoExpediente.Asunto = Asunto.Text;
            PagoExpediente.Descripcion = Descripcion.Value;
            PagoExpediente.IdProcedencia = numIdProcedencia;
            PagoExpediente.IdEntregadoPor = numIdEntregadoPor;

            PagoExpediente.FechaEntrega = fecFechaEntrega;
            PagoExpediente.HoraEntrega = timHoraEntrega;

            PagoExpediente.BeneficiarioCorreo = BeneficiarioCorreo.Text;

            PagoExpediente.Monto = numMonto;
            PagoExpediente.Observacion = Observacion.Value;
            PagoExpediente.IdUsuarioRegistra = numIdUsuario;
            PagoExpediente.IdUsuarioModifica = numIdUsuario;

            PagoExpediente.NCF = NCF.Text;

            PagoExpediente.IdDepartamentoSolicitante = Session["DepartamentoRemitente"].ToString().ToInt();
            PagoExpediente.UltimoEstatus = 1;
            PagoExpediente.IdUltimaUbicacion = Session["DepartamentoRemitente"].ToString().ToInt();

            PagoExpediente.ValorFactura = numValorFactura;
            PagoExpediente.Impuesto = numImpuesto;
            PagoExpediente.NumeroRequisicion = txtNumeroRequisicion.Text.ToString().ToIntNuleable();

            return todobien;
        }

        private string ReemplazarComasEnNumeros(string v)
        {
            string input = v;
            string pattern = ",";
            string replacement = "";
            Regex rgx = new Regex(pattern);
            string result = rgx.Replace(input, replacement);
            return result;
        }

        protected void btn_Click_OpenExpedientesEnviados(object sender, EventArgs e)
        {
            vista3.Visible = false;
            vista2.Visible = false;
            vista1.Visible = true;
            btnSalvar.Visible = false;
            btnNuevo.Visible = false;
            btnGestionar.Visible = false;
            ListadoExpedientesEnviados(true);
            Session["btnSeccionado"] = "Enviados";

            Session["Default_SeccionActiva"] = "Enviados";
            SeccionSeleccionada.InnerHtml = "EXPEDIENTES ENVIADOS";
        }
        protected void btn_Click_OpenExpedientesRecibidos(object sender, EventArgs e)
        {

            vista2.Visible = false;
            vista1.Visible = true;
            vista3.Visible = false;
            btnSalvar.Visible = false;
            btnNuevo.Visible = false;
            Session["btnSeccionado"] = "Recibidos";
            ListadoExpedientesRecibidos(true);
            SeccionSeleccionada.InnerHtml = "EXPEDIENTES RECIBIDOS";
        }
        protected void btn_Click_Registros(object sender, EventArgs e)
        {

            vista2.Visible = false;
            vista3.Visible = true;
            btnSalvar.Visible = false;
            btnNuevo.Visible = true;
            ListadoExpedientes(true);
            Session["btnSeccionado"] = "Registros";
            //ScriptManager.RegisterStartupScript(this, this.GetType(), "Pop", "LimpiarCampos();", true);
            SeccionSeleccionada.InnerHtml = "EXPEDIENTES REGISTRADOS";

        }
        protected void Enviar()
        {
            ListExpedientesEstatus = ctrl.ListExpedientesEstatus(Session["DepartamentoRemitente"].ToString().ToInt(), txtTipoDocumento.Value.ToString().ToInt());
            selectEstatus.DataSource = ListExpedientesEstatus;
            selectEstatus.DataBind();

            CxP_PagosExpedientesEnviados ExpedienteEnviado = new CxP_PagosExpedientesEnviados();
            int.TryParse(PagoExpedienteRecibido.IdExpediente.ToString(), out int intIdExpediente);
            ExpedienteEnviado.IdExpediente = intIdExpediente;
            int.TryParse(Session["DepartamentoRemitente"].ToString(), out int intDepartamentoRemitente);
            ExpedienteEnviado.IdDepartamentoRemitente = intDepartamentoRemitente;
            int.TryParse(Session["UsuarioRemitente"].ToString(), out int intUsuarioRemitente);
            ExpedienteEnviado.IdUsuarioRemitente = intUsuarioRemitente;
            int.TryParse(selectDestinatario.Value?.ToString(), out int intDestinatario);
            ExpedienteEnviado.IdDepartamentoDestinatario = selectDestinatario.Value.ToString().ToInt();
            ExpedienteEnviado.Estatus = 1;
            ExpedienteEnviado.FechaEnviado = DateTime.Now;

            ExpedienteEnviado.Comentario = TxtComentario.Value;
            int.TryParse(selectEstatus.Value?.ToString(), out int intEstatusAplicado);

            ExpedienteEnviado.EstatusAplicado = intEstatusAplicado;

            Resultado resultado = ctrl.EnviarExpediente(ExpedienteEnviado);

            if (resultado.TodoBien)
            {
                CxP_PagosExpedientes Expediente = new CxP_PagosExpedientes();
                Expediente = ctrl.ResumenExpediente(resultado.ID.ToString().ToInt());

                ExpedienteEnviado.IdRegistro = PagoExpedienteRecibido.IdRegistro;
                ctrl.ActualizarEstatusRegistroEnviado(ExpedienteEnviado);

                ExpedienteEnviado.IdUltimaUbicacion = ExpedienteEnviado.IdDepartamentoDestinatario.ToString().ToInt();
                ExpedienteEnviado.UltimoEstatus = ExpedienteEnviado.EstatusAplicado.ToString().ToInt();


                ctrl.ActualizarUltimoEstatus(ExpedienteEnviado);

                modalMensajeEnviar.InnerHtml = "El expediente No.: " + PagoExpedienteRecibido.IdExpediente + " Se ha enviado con exito";
                ModalEnviar.ShowOnPageLoad = true;

                ListCorreos = ctrl.ListCorreos(intDestinatario, intDepartamentoRemitente);

                CorreoElectronicoCtrl ManejarCorreo = new CorreoElectronicoCtrl();
                CorreoElectronico correo = new CorreoElectronico();

                correo.IdUsuario = Session["UsuarioRemitente"].ToString().ToInt();
                correo.PageId = HttpContext.Current.Request.Url.AbsoluteUri;
                correo.Asunto = "Gestión del Expediente No.: [" + ExpedienteEnviado.IdExpediente.ToString() + "]";

                DateTime hoy = DateTime.Today;
                DateTime.TryParse(UltimaFecha.InnerText.ToString(), out DateTime dateUltimaFecha);

                string totalDiasBarado = hoy.CalcularTiempoStr(dateUltimaFecha);

                Usuario user = new Usuario();
                user = ctrl.Usuario(intUsuarioRemitente);

                string UsuarioRemitente = user.NombreCompleto;

                string[] romperFechaHora = Expediente.FechaEntrega.ToString().Split(' ');
                string fechaEntrega = romperFechaHora[0].ToString();


                string DepartamentoRemitente = user.Departamento;

                string subTitulo = "Se ha enviado desde " + Expediente.DepartamentoRemitente + " hasta " + Expediente.DepartamentoDestinatario;
                string contenido = "";
                contenido += "<table width='600' border='0' cellspacing='0' cellpadding='0' style='border:1px solid #000;'><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Tipo de documento: </th><td style='border-bottom:1px solid #000; padding-left:5px;'>" + Expediente.TipoDocumento + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Fecha de emision: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + Expediente.FechaEmision + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>No documento: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + Expediente.NumeroDocumento + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Asunto: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + Expediente.Asunto + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Fecha de entrega: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + fechaEntrega + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Gestionado por: </th><td style='border-bottom:1px solid #000; padding-left:5px;text-transform:uppercase'>" + UsuarioRemitente + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>En fecha </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + ExpedienteEnviado.FechaEnviado + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Estuvo sin movimiento: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + totalDiasBarado + " con estatus: " + EstatusActual.InnerHtml.ToString() + " </td></tr><tr><th width='200' scope='row' style='text-align:left; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase'>Último estatus aplicado:</th><td style='padding-left:5px; text-transform:uppercase'>" + Expediente.LabelEstatus + "</td></tr></table>";

                correo.Para = ListCorreos.Where(g => g.EnviarComo == CorreoElectronicoDestinatarios.ModoEnvio.PARA && g.CorreoElectronico.IsValidEmail()).Select(u => u.CorreoElectronico).ToList();
                correo.Copia = ListCorreos.Where(g => g.EnviarComo == CorreoElectronicoDestinatarios.ModoEnvio.COPIA && g.CorreoElectronico.IsValidEmail()).Select(u => u.CorreoElectronico).ToList();
                correo.CopiaOculta = ListCorreos.Where(g => g.EnviarComo == CorreoElectronicoDestinatarios.ModoEnvio.COPIA_OCULTA && g.CorreoElectronico.IsValidEmail()).Select(u => u.CorreoElectronico).ToList();
                correo.Cuerpo = "<!DOCTYPE html PUBLIC \'-//W3C//DTD XHTML 1.0 Transitional //EN\' \'http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\'><html xmlns=\'http://www.w3.org/1999/xhtml\' xmlns:v=\'urn:schemas-microsoft-com:vml\' xmlns:o=\'urn:schemas-microsoft-com:office:office\'><head><meta http-equiv=\'Content-Type\' content=\'text/html; charset=utf-8\' /> <style type=\'text/css\'>  body, .mainTable { height:100% !important; width:100% !important; margin:0; padding:0; }  img, a img { border:0; outline:none; text-decoration:none; }  .imageFix { display:block; }  table, td { border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;}  p {margin-top:0; margin-right:0; margin-left:0; padding:0;}  .ReadMsgBody{width:100%;} .ExternalClass{width:100%;}.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, 	.ExternalClass td, 	.ExternalClass div { line-height: 100% ; } 	img { -ms-interpolation-mode: bicubic; 	} body, table, td, 	p, 	a, li, 	blockquote { -ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; } </style><style>table{ border-collapse: collapse; }@media only screen and (max-width: 600px) {    body[yahoo] .rimg {   max-width: 100%; height: auto; } body[yahoo].rtable { 	width: 100%!important;table-layout: fixed; } body[yahoo].rtable tr { height: auto!important; } } </style><!--[if gte mso 9]><xml>  <o:OfficeDocumentSettings> <o: AllowPNG / ><o: PixelsPerInch > 96</o:PixelsPerInch>  </o: OfficeDocumentSettings ></xml><![endif]--></head ><body yahoo = fix scroll = \'auto\' style=\'padding:0; margin:0; FONT-SIZE: 12px; FONT-FAMILY: Arial, Helvetica, sans-serif; cursor:auto; background:#F3F3F3\'><TABLE class=\'rtable mainTable\' cellSpacing=0 cellPadding=0 width=\'100%\' bgColor=#f3f3f3> <TR ><TD style = \'FONT-SIZE: 0px; HEIGHT: 20px; LINE-HEIGHT: 0\'>&#160;</TD></TR><TR><TD vAlign = top ><TABLE class = rtable style = \'WIDTH: 600px; MARGIN: 0px auto\' cellSpacing=0 cellPadding=0 width=600 align=center border=0> <TR ><TD style = \'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: medium none; PADDING-RIGHT: 0px; BACKGROUND-COLOR: transparent\'> <TABLE class = rtable style = \'WIDTH: 100%\' cellSpacing=0 cellPadding=0 align=left> <TR style = \'HEIGHT: 10px\'><TD style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 35px; TEXT-ALIGN: center; PADDING-TOP: 35px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent\'><TABLE cellSpacing=0 cellPadding=0 align=center border=0><TR> <TD style = \'PADDING-BOTTOM: 2px; PADDING-TOP: 2px; PADDING-LEFT: 2px; PADDING-RIGHT: 2px\' align=center><TABLE cellSpacing=0 cellPadding=0 border=0><TR><TD style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; BACKGROUND-COLOR: transparent\'><IMG class=rimg style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; DISPLAY: block; BACKGROUND-COLOR: transparent\' border=0 src=\'http://app.inaipi.gob.do:97/solicitudes/images/Image_1.png\' width=263 height=64 hspace=\'0\' vspace=\'0\'></TD></TR></TABLE></TD></TR></TABLE></TD></TR></TABLE></TD></TR><TR> <TD style = \'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; PADDING-TOP: 1px; PADDING-LEFT: 0px; BORDER-LEFT: medium none; PADDING-RIGHT: 0px; BACKGROUND-COLOR: transparent\'><TABLE class=rtable style=\'WIDTH: 100%\' cellSpacing=0 cellPadding=0 align=left><TR style=\'HEIGHT: 10px\'><TD style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; TEXT-ALIGN: center; PADDING-TOP: 1px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent\'><P style=\'FONT-SIZE: 18px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #0070cd; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=center><STRONG>Instituto Nacional de Atenci&#243;n Integral a la Primera Infancia</STRONG><BR><FONT style=\'FONT-SIZE: 14px; COLOR: #ffa300\'><STRONG>&#161;Ser ni&#241;o y&#160;ni&#241;a nunca fue mejor&#160;!</STRONG></FONT><BR></P></TD></TR></TABLE></TD></TR> <TR ><TD style = \'BORDER-TOP: #dbdbdb 1px solid; BORDER-RIGHT: #dbdbdb 1px solid; BORDER-BOTTOM: #dbdbdb 1px solid; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: #dbdbdb 1px solid; PADDING-RIGHT: 0px; BACKGROUND-COLOR: #feffff\'><TABLE class=rtable style=\'WIDTH: 100%\' cellSpacing=0 cellPadding=0 align=left><TR style=\'HEIGHT: 10px\'><TD style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 35px; TEXT-ALIGN: center; PADDING-TOP: 35px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: #feffff\'><P style=\'FONT-SIZE: 18px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #a8a7a7; LINE-HEIGHT: 155%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=center><STRONG><h2>" + correo.Asunto + "</h2></STRONG></P> <P style = \'FONT-SIZE: 12px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #a7a7a7; LINE-HEIGHT: 155%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=center></h1> <p style=\'font-size:14px\' >" + subTitulo + "</p><p>" + contenido + "</p> <p ></TD></TR></TABLE></TD></TR><TR> <TD style = \'BORDER-TOP: #dbdbdb 1px solid; BORDER-RIGHT: #dbdbdb 1px solid; BORDER-BOTTOM: #dbdbdb 1px solid; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: #dbdbdb 1px solid; PADDING-RIGHT: 0px; BACKGROUND-COLOR: #feffff\'><TABLE class = rtable style = \'WIDTH: 100%\' cellSpacing=0 cellPadding=0 align=left><TR style=\'HEIGHT: 10px\'> <TD style = \'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; TEXT-ALIGN: center; PADDING-TOP: 1px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent\'> <P style = \'FONT-SIZE: 10px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=left>&#160;</P><P style=\'FONT-SIZE: 10px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=left>&#160;</P><P style=\'FONT-SIZE: 16px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=right>Sistema de Informaci&#243;n &#169;2018 <BR>Departamento de Desarrollo e Implementaci&#243;n de Sistemas, TIC </P></TD></TR></TABLE></TD></TR></TABLE></TD></TR><TR> <TD style = \'FONT-SIZE: 0px; HEIGHT: 8px; LINE-HEIGHT: 0\'>&#160;</TD></TR></TABLE></body></html></TD></TR><TR> <TD style = \'FONT-SIZE: 0px; HEIGHT: 8px; LINE-HEIGHT: 0\'>&#160;</TD></TR></TABLE></body></html>";
                ManejarCorreo.EnviarCorreo(correo, true);
            }
            else
            {
                modalMensajeEnviar.InnerHtml = "Error al enviar " + resultado.strError;
                ModalEnviar.ShowOnPageLoad = true;
            }

        }

        public void EnviarCorreo()
        {

        }

        protected void AutoEnviar(int _IdExpediente)
        {
            CxP_PagosExpedientesEnviados ExpedienteEnviado = new CxP_PagosExpedientesEnviados();
            int.TryParse(PagoExpediente.IdExpediente.ToString(), out int intIdExpediente);
            ExpedienteEnviado.IdExpediente = _IdExpediente;
            int.TryParse(Session["DepartamentoRemitente"].ToString(), out int intDepartamentoRemitente);
            ExpedienteEnviado.IdDepartamentoRemitente = intDepartamentoRemitente;
            int.TryParse(Session["UsuarioRemitente"].ToString(), out int intUsuarioRemitente);
            ExpedienteEnviado.IdUsuarioRemitente = intUsuarioRemitente;
            //int.TryParse(Session["DepartamentoRemitente"].ToString(), out int intDestinatario);
            ExpedienteEnviado.IdDepartamentoDestinatario = Session["DepartamentoRemitente"].ToString().ToInt();
            ExpedienteEnviado.Estatus = 1;

            ExpedienteEnviado.Comentario = "Registro iniciado";
            int.TryParse(selectEstatus.Value?.ToString(), out int intEstatusAplicado);

            ExpedienteEnviado.EstatusAplicado = 1;

            Resultado resultado = ctrl.EnviarExpediente(ExpedienteEnviado);

            if (resultado.TodoBien)
            {

                ListadoExpedientesRecibidos(true);
            }
            else
            {
                modalMensajeEnviar.InnerHtml = "Error al enviar " + resultado.strError;
                ModalEnviar.ShowOnPageLoad = true;
            }




        }
        private void Cronologia(int Idexpediente)
        {

            if (Idexpediente > 0)
            {
                PagoExpedientesCronologia = ctrl.Cronologia(Idexpediente);
            }
            grdCronologia.DataSource = PagoExpedientesCronologia;
            grdCronologia.DataBind();

        }

        private void ValidarBotonActivo()
        {
            switch (BotonActivo)
            {
                case EnumBoton.btnbtnRegistros:
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    break;
                case EnumBoton.btnRecibidos:
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    break;
                case EnumBoton.btnbtnEnviados:
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    break;
                default:

                    break;
            }

        }

        protected void cbTotalCxP_Callback(object sender, DevExpress.Web.CallbackEventArgsBase e)
        {
            Monto.Number = ValorFactura.Number + Impuesto.Number;
        }

        protected void Unnamed_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect(System.Configuration.ConfigurationManager.AppSettings["MenuPrincipalUrl"]);
        }


        private void SubirArchivo(string FileName, byte[] FileBytes, CxP_PagosExpedientes PagoExpediente)
        {
            Resultado r = new Resultado();

            DIG_Documentos documento = new DIG_Documentos();
            documento.IdDocumento = (PagoExpedienteRecibido.IdDocumento ?? 0);
            documento.IdSecPKOrigen = PagoExpedienteRecibido.IdProcedencia.ToString().ToInt();
            documento.CodigoDoc = PagoExpedienteRecibido.NumeroDocumento;
            documento.FechaDoc = PagoExpedienteRecibido.FechaEmision.Date;
            documento.NombreDoc = PagoExpedienteRecibido.Asunto.Replace("/", "|");
            documento.CedulaRNCDoc = PagoExpedienteRecibido.RNC;
            documento.Asunto = PagoExpedienteRecibido.Asunto.Replace("/", "|").Replace(System.Environment.NewLine, "");
            documento.IdCategoria = ListTiposDocumentos.FirstOrDefault(t => t.IdTipoDocumento == PagoExpedienteRecibido.IdTipoDocumento).IdDocumentoCategoria;
            documento.Categoria = ListTiposDocumentos.FirstOrDefault(t => t.IdTipoDocumento == PagoExpedienteRecibido.IdTipoDocumento).DocumentoCategoria; //TODO: BINDEAR ESTE CAMPO DEL IdDocumentoCategoria del tipo de documento
            documento.Observaciones = PagoExpedienteRecibido.Observacion;
            documento.IdSecPKUbicacion = PagoExpedienteRecibido.IdUltimaUbicacion.ToString().ToInt();
            //documento.IdSecPKDestino = PagoExpedienteRecibido.IdUltimaUbicacion.ToString().ToInt();
            documento.IdUsuarioRegistra = UsuarioRemitente;//PagoExpediente.IdUsuarioRegistra.ToString().ToInt();

            DIG_Documentos.DIG_DocumentosArchivos archivo = new DIG_Documentos.DIG_DocumentosArchivos { IdDocumento = documento.IdDocumento, IdArchivo = (ListarDocumentos.Count + 1), Nombre = FileName, Archivo = FileBytes, IdUsuarioRegistraArchivo = UsuarioRemitente };
            if (documento.IdDocumento > 0)
            {
                r = ctrl.AgregarArchivo(documento, archivo);
            }
            else
            {
                documento.Archivos.Add(archivo);
                r = ctrl.SalvarArchivos(PagoExpedienteRecibido, documento);
            }

            if (r.TodoBien)
            {

            }
            else
            {
                //TODO: MENSAJE DE ERROR
                ModalSubirArchivo.ShowOnPageLoad = true;
                ModalSubirArchivoContent.InnerText = "Error. El archivo no pudo subir al servidor";
            }

            //documento.NombreRelacionadoDoc = PagoExpediente.nu

            //documento.Archivos.Add(new DIG_Documentos.DIG_DocumentosArchivos { Archivo = cargador.UploadedFiles[0].UploadedFiles[0].FileBytes, Nombre = cargador.UploadedFiles[0].FileName } );
            //documento.Archivos.Add(new DIG_Documentos.DIG_DocumentosArchivos { Archivo = cargador.fi, Nombre = cargador.UploadedFiles[0].FileName });
            //ctrldig.Registrar(documento);


        }



        protected void btnAgregarArchivo_Click(object sender, EventArgs e)
        {
            //SubirArchivo();

        }

        protected void UploadControl_FileUploadComplete(object sender, FileUploadCompleteEventArgs e)
        {
            /*
            if(Seccion.InnerText == "Recibidos")
            {
                PagoExpediente = PagoExpedienteRecibido.Clone();
            }
            if (Seccion.InnerText == "Enviados")
            {
                PagoExpediente = PagoExpedienteEnviado.Clone();
            }*/

            SubirArchivo(e.UploadedFile.FileName, e.UploadedFile.FileBytes, PagoExpediente);

        }

        protected void cbDocumentos_Callback(object sender, CallbackEventArgsBase e)
        {

            string[] Parametros = e.Parameter.Split('|');


            switch (Parametros[0])
            {
                case "SUBIR_ARCHIVO":
                    ModalSubirArchivoContent.InnerText = "El archivo se a agregado satisfactoriamente";
                    ModalSubirArchivo.ShowOnPageLoad = true;


                    ListarDocumentos = ctrl.ListarArchivosDocumento(PagoExpedienteRecibido.IdDocumento.Value);
                    grdArchivos.DataSource = ListarDocumentos;
                    grdArchivos.DataBind();
                    //TODO: MENSAJE DE TODO BIEN
                    break;

                case "ELIMINAR_ARCHIVO":
                    //int IdArchivo = 0;
                    int.TryParse(Parametros[1], out int IdArchivo);
                    DIG_Documentos.DIG_DocumentosArchivos archivo = ListarDocumentos.FirstOrDefault(a => a.IdArchivo == IdArchivo);
                    //Archivo = Archivos.FirstOrDefault(a => a.IdArchivo == IdArchivo);
                    //DIG_Documentos.DIG_DocumentosArchivos archivo = new DIG_Documentos.DIG_DocumentosArchivos();


                    //archivo.IdDocumento = 21;
                    // archivo.IdArchivo = IdArchivo;
                    //archivo.RutaFisica = "";
                    //archivo.RutaVirtual = "";

                    ctrl.EliminarArchivo(archivo, UsuarioRemitente);

                    ListarDocumentos = ctrl.ListarArchivosDocumento(PagoExpedienteRecibido.IdDocumento.Value);
                    grdArchivos.DataSource = ListarDocumentos;
                    grdArchivos.DataBind();
                    break;
                case "DESCARGAR_ARCHIVO":
                    /*
                    WebClient myWebClient = new WebClient();
                    string destino = System.Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments) + "\\"+archivo.Nombre;
                    //myWebClient.DownloadFile("http://localhost:51196/FileServer/Digitalizacion/FACTURAS%20(22)/dkaskdk%20(21)/13a847c5d563998c69996972c39078f4%20(6).jpg", destino);
                    string rutaArchivo = archivo.RutaVirtual.ToString().Replace("~", "http://localhost:51196");
                    myWebClient.DownloadFile(rutaArchivo, destino);
                    //destino = destino.Replace("\\","\");
                    File.Open(@destino,FileMode.Open);
                    
                    */
                    int.TryParse(Parametros[1], out int idArchivo);
                    DIG_Documentos.DIG_DocumentosArchivos Archivo = ListarDocumentos.FirstOrDefault(a => a.IdArchivo == idArchivo);
                    DescargarArchivo(Archivo);
                    break;

                case "VISUALIZAR_ARCHIVO":
                    int.TryParse(Parametros[1], out int vidArchivo);
                    DIG_Documentos.DIG_DocumentosArchivos vArchivo = ListarDocumentos.FirstOrDefault(a => a.IdArchivo == vidArchivo);
                    Src = vArchivo.RutaVirtual.ToString().Replace("~", "http://localhost:51196");
                    //Iframe.Src = "http://localhost:51196/FileServer/Digitalizacion/REQUISICIONES%20(45)/asunto%20(23)/asdfsdfdsfi%20(4).pdf";
                    Iframe.Attributes.Add("src", Src);
                    //PreviewDocumento.Visible = true;
                    //PArchivo.Value = 1;
                    break;

            }



        }

        private void DescargarArchivo(DIG_Documentos.DIG_DocumentosArchivos Archivo)
        {

            if (Archivo.RutaFisica.Length > 0)
            {
                FileInfo file = new FileInfo(Archivo.RutaFisica);
                if (file.Exists)
                {
                    Response.Clear();
                    Response.ClearHeaders();
                    Response.ClearContent();
                    Response.AddHeader("Content-Disposition", "attachment; filename=" + Archivo.Nombre);
                    Response.AddHeader("Content-Length", file.Length.ToString());
                    Response.ContentType = "text/plain";
                    Response.Flush();
                    //Response.WriteFile(file.FullName);
                    //Response.BinaryWrite(file.)
                    Response.TransmitFile(file.FullName);
                    Response.End();
                }
                else
                {
                    //MsgBox("NO SE ENCONTRÓ ESTE ARCHIVO EN LA RUTA FÍSICA ESPECIFICADA.", Models.Comun.Msjtype.Error, DEFAULT_TITLE_FOR_ERROR);
                    //cbPrincipal.JSProperties["cp_title"] = DEFAULT_TITLE_FOR_ERROR;
                    //cbPrincipal.JSProperties["cp_text"] = "NO SE ENCONTRÓ ESTE ARCHIVO EN LA RUTA FÍSICA ESPECIFICADA.";
                    //cbPrincipal.JSProperties["cp_type"] = "error";
                }

                file = null;
            }
        }

        protected void grdArchivos_CustomButtonInitialize(object sender, ASPxGridViewCustomButtonEventArgs e)
        {
            DIG_Documentos.DIG_DocumentosArchivos archivo = (DIG_Documentos.DIG_DocumentosArchivos)grdArchivos.GetRow(e.VisibleIndex);

            if (archivo != null)
            {
                e.Enabled = !archivo.EliminadoArchivo;
            }

            //if (archivo.EliminadoArchivo)
            //{
            //    if (e.ButtonID.ToUpper() == "BTNELIMINAR")
            //    {
            //        //e.Text = "Reactivar";
            //        //e.Image.IconID = "actions_convert_16x16office2013";
            //        e.Enabled = false;
            //    }

            //    if (e.ButtonID.ToUpper() == "BTNVISUALIZAR")
            //    {
            //        e.Enabled = false;
            //    }

            //    if (e.ButtonID.ToUpper() == "BTNDESCARGAR")
            //    {
            //        e.Enabled = false;
            //    }
            //}


        }

        protected void grdArchivos_CustomButtonCallback(object sender, ASPxGridViewCustomButtonCallbackEventArgs e)
        {
            DIG_Documentos.DIG_DocumentosArchivos archivo = (DIG_Documentos.DIG_DocumentosArchivos)grdArchivos.GetRow(e.VisibleIndex);

            switch (e.ButtonID)
            {
                case "btnDescargar":
                    DescargarArchivo(archivo);
                    break;

                case "btnVisualizar":
                    //PreviewDocumento.Visible = true;

                    Src = archivo.RutaVirtual.ToString().Replace("~", "http://localhost:51196");
                    //Iframe.Src = "http://localhost:51196/FileServer/Digitalizacion/REQUISICIONES%20(45)/asunto%20(23)/asdfsdfdsfi%20(4).pdf";
                    Iframe.Attributes.Add("src", Src);
                    break;
                default:
                    break;
            }

        }

    }


}


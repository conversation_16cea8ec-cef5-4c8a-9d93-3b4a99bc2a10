

$(document).ready(function(e) {
	$(window).keydown(function(event){
    if(event.keyCode == 13) {
      event.preventDefault();
      return false;
    }
  });
	//OpenRegistro('1')
	//DataGrid();
	 
	
	//$("#ContentPlaceHolder1_grdListadoExpedientes_DXSE_I").addClass("form-control");
	
	function Validar(){
		var IdTipoDocumento = $("#IdTipoDocumento").val();
		var FechaEmision = $("#FechaEmision").val(); //alert(FechaEmision);
		var NumeroDocumento = $("#NumeroDocumento").val();
		var Asunto = $("#Asunto").val();
		var Descripcion = $("#Descripcion").val();
		var IdProcedencia = $("#IdProcedencia").val();
		var IdEntregadoPor = $("#IdEntregadoPor").val();
		var FechaEntrega = $("#FechaEntrega").val();
		var HoraEntrega = $("#HoraEntrega").val();
		var IdBeneficiario = $("#IdBeneficiario").val();
		var BeneficiarioCorreo = $("#BeneficiarioCorreo").val();
		var IdTipoPago = $("#IdTipoPago").val();
		var Monto = $("#Monto").val();
		var Observacion = $("#Observacion").val();
		var ValorFactura = $("#ValorFactura").val();
		var Impuesto = $("#Impuesto").val();
		var NCF = $("#NCF").val();
		
		$(".content-formulario .form-control").addClass("requerido");
		$(".content-formulario #BeneficiarioCorreo").removeClass("requerido");
		$(".content-formulario #IdProcedencia").removeClass("requerido");
		$(".content-formulario #Procedencia").removeClass("requerido");
		$(".content-formulario #IdEntregadoPor").removeClass("requerido");
		$(".content-formulario #EntregadoPor").removeClass("requerido");
		$("#btnSalvar").removeAttr('disabled'); 
		
		/*
		$(".form-control").each(function(){
       		//alert($(this).text())
			if($(this).val() == ''){
				return false;	
			}else{
				$(this).removeClass('requerido');
			}
        });
		*/
		
		if(IdTipoDocumento != ''){ 
			$("#IdTipoDocumento").removeClass("requerido");
		}
		if(FechaEmision != ''){ 
			$("#FechaEmision").removeClass("requerido");			
		}
		if(NumeroDocumento != ''){
			$("#NumeroDocumento").removeClass("requerido");  
		}
		if(Asunto != ''){ 
			$("#Asunto").removeClass("requerido"); 
		}
		if(Descripcion != ''){ 
			$("#Descripcion").removeClass("requerido"); 
		}
		if(IdProcedencia != ''){ 
			$("#IdProcedencia").removeClass("requerido"); 
			$("#Procedencia").removeClass("requerido"); 
		}
		
		if(IdEntregadoPor != ''){ 
			$("#IdEntregadoPor").removeClass("requerido");
			$("#EntregadoPor").removeClass("requerido");
		}
		
		if(FechaEntrega != ''){ 
			$("#FechaEntrega").removeClass("requerido"); 
			
		}
		
		if(HoraEntrega != ''){ 
			$("#HoraEntrega").removeClass("requerido"); 
				
		}
		
		if(IdBeneficiario != ''){ 
			$("#IdBeneficiario").removeClass("requerido"); 
			$("#Beneficiario").removeClass("requerido"); 
			
		}
		
		if(IdTipoPago != ''){ 
			$("#IdTipoPago").removeClass("requerido"); 
			
		}
		
		if(Monto != ''){ 
			$("#Monto").removeClass("requerido"); 
			
		}
		if(Observacion != ''){ 
			$("#Observacion").removeClass("requerido"); 
			
		}
		
		if(ValorFactura != ''){ 
			$("#ValorFactura").removeClass("requerido"); 
			
		}
		
		if(Impuesto != ''){ 
			$("#Impuesto").removeClass("requerido"); 
			
		}
		
		if(NCF != ''){ 
			$("#NCF").removeClass("requerido"); 
			
		}
		
		if(IdTipoDocumento == '' || FechaEmision == '' || NumeroDocumento == '' || Asunto == '' || Descripcion == '' || FechaEntrega == '' || HoraEntrega == '' || IdBeneficiario == '' || IdTipoPago == '' || Monto == '' || Observacion == '' || ValorFactura == '' || Impuesto == '' || NCF == '' ){ 
			$(".modal-header").css("background-color","#f00");
			return 0;
		}
		
		
		
	
		
		
		
		
		
		
		
		
		
		
	}
	
	function DataGrid(){
		var frm = $('#form-expedientes');
		$("#Option").val('data-grid');	
		 $("#loading-filtrar").show();	
		 
		$.ajax({
            type: 'POST',
            url: 'proceso.php',
            data: frm.serialize(),
            success: function (data) {
				//console.log(data);
				obj = JSON.parse(data); 
				$("#data-grid-load-registros").html('');
				var filas = '';
				for(i=0; i<obj.Expedientes.length; i++){
					//$("#data-grid-load-registros").append('<option value="'+obj.Expedientes[i].ProvinciaId+'">'+obj.Provincias[i].Nombre+'</option>');
					filas += '<tr onDblClick="OpenRegistro(\''+obj.Expedientes[i].IdExpediente+'\')"><th nowrap="nowrap"><a href="#">'+obj.Expedientes[i].IdExpediente+'</a></th>';
                    filas += '<td align="center" nowrap="nowrap">'+obj.Expedientes[i].NumeroDocumento+'</td>';
					filas += '<td nowrap="nowrap">'+obj.Expedientes[i].Asunto+'</td>';       
					filas += '<td nowrap="nowrap">'+obj.Expedientes[i].Procedencia+'</td>';       
					filas += '<td align="center" nowrap="nowrap">'+obj.Expedientes[i].FechaEntrega+'</td>';       
					filas += '<td align="center" nowrap="nowrap">'+obj.Expedientes[i].FechaRegistro+'</td>';           
					filas += '</tr>';
				}
				$("#data-grid-load-registros").html(filas);
				$("#loading-filtrar").hide();	
            },
            error: function (data) {
			
               $("#Modal-base").modal('show');
				$("#Modal-base .modal-title").text('Cargar datos');
				$("#Modal-base .modal-body p").text('Un error ha ocurrido verifique su conexión o consulte a soporte técnico');
				//$("#Modal-base .modal-footer button").attr('id','btn-cerrar-modal');
            },
        });	
	}
	
	
	
	
    $("#btn-nuevo").click(function(){
		$("#IdExpediente").val('');
		$(".formulario input").val('');
		$(".formulario select").val('');
		$(".formulario textarea").val('');
		$(".formulario #label-IdExpediente").html('');
		
		$("#fecha-registro").html('');
		$("#registrado-por").html('');
		$("#ultima-modificacion").html('');
		$("#modificado-por").html('');
		$("#ultima-ubicacion").html('');
		$("#ultimo-estatus").html('');
		
		$("#Option").val('save');
		$("#vista2").fadeIn("fast");
		$("#btn-nuevo").hide();
		$("#btnSalvar").show();
		
		$('.nav-tabs a[href="#home"]').tab('show');
		$("#data-grid-load-cronologia").html('');
	});
	$("#btn-registros").click(function(){
		$("#vista2").fadeOut("fast");
		$("#btn-nuevo").show();
		$("#btnSalvar").hide();
		//DataGrid();		
	});
	$("#btn-close-vista-2").click(function(){
		
		
		$("#vista-2").fadeOut("fast");
		$("#btn-nuevo").show();
		$("#btnSalvar").hide();
		//DataGrid();	
	});
	
	
	$("#btnSalvar").click(function(){
		$(this).attr('disabled','disabled');
		//alert($("#Option").val());
		//$("#Option").val('save');
		validar = Validar();
		
		if(validar == 0){
			 $("#Modal-base").modal('show');
				$("#Modal-base .modal-title").text('Salvar expediente');
				$("#Modal-base .modal-body p").text('Es obligatorio llenar todos los campos requeridos');
		}else{
		
		 var frm = $('#form-expedientes');
		 
		$.ajax({
            type: 'POST',
            url: 'proceso.php',
            data: frm.serialize(),
            success: function (data) {
				console.log(data);
				data = data.split(':::');
				
				var IdExpediente = data[0]; 
				var expediente = data[1];
				var cronologia = data[2];
				
				if(isNaN(IdExpediente)){
					alert('error en el servidor');
				}else{
					//alert(data);
					OpenRegistroDetalle(expediente)
					DataGridCronologiaData(cronologia);
					//DataGrid();
					$("#IdExpediente").val(IdExpediente);
					$("#label-IdExpediente").html(IdExpediente);
					$("#Option").val('update');
					$('#Modal-save').modal('show');
					$("#btnSalvar").removeAttr('disabled');
					
				}
            },
            error: function (data) {
			
               $("#Modal-base").modal('show');
				$("#Modal-base .modal-title").text('Salvar expediente');
				$("#Modal-base .modal-body p").text('Un error ha ocurrido verifique su conexión o consulte a soporte técnico');
				//$("#Modal-base .modal-footer button").attr('id','btn-cerrar-modal');
				$("#Option").val('save');
				
            },
        });
		}
		return false;
	});
	
	$("#IdProcedencia").dblclick(function(){
		Procedencias();
	});
	$("#Procedencia").dblclick(function(){
		Procedencias();
	});
	$("#IdEntregadoPor").dblclick(function(){
		Mensajeros();
	});
	$("#EntregadoPor").dblclick(function(){
		Mensajeros();
	});
	$("#IdBeneficiario").dblclick(function(){
		Beneficiarios();
	});
	$("#Beneficiario").dblclick(function(){
		Beneficiarios();
	});
	
	$("#btn-cerrar-modal").click(function(){
		alert('cerrar modal');	
	});
	$("#filtrar-registros").keydown(function(e){ 
		if(e.which == 13){
			//alert($("#filtrar-registros").val());
			//DataGrid();
			//return false;	
		}
	});
	/*var contador = 1;
	$("#Asunto").keyup(function(){		
		console.log(contador++);
		if(contador >= 25){
				
		}
	})*/
	
				
	/*$('#Monto').on('input', function () { 
		this.value = this.value.replace(/[^0-9,.]/g,'');
	});*/ 
		
	$("#Monto").change(function(){
		var number = numeral($("#Monto").val());
		number.format();
		numeral.defaultFormat('$0,0.00');		
		$("#Monto").val(number.format());
	});
	
	$("#modal-save-btn-aceptar").click(function(){
		setTimeout(function(){ $("#vista2").fadeOut(); }, 1);
		$("#btn-nuevo").show();
		$("#btnSalvar").hide();	
	});
	
	$("#ValorFactura").on('input', function () { 
    	this.value = this.value.replace(/[^0-9.,]/g,'');
	});
	$("#Impuesto").on('input', function () { 
    	this.value = this.value.replace(/[^0-9.]/g,'');
	});
	$("#ValorFactura").change(function(){
		CalcularMonto();
		
	});
	$("#Impuesto").change(function(){
		CalcularMonto();	
	});
	
});


function CalcularMonto(){
	var valorFactura = Number($("#ValorFactura").val());
	var impuesto = Number($("#Impuesto").val());
	//var valorImpuesto = valorFactura * impuesto; 
	
	$("#Monto").val(valorFactura + impuesto);	
	
	var number = numeral($("#Monto").val());
	number.format();
	numeral.defaultFormat('$0,0.00');		
	$("#Monto").val(number.format());
}
function DataGridCronologia(){
		var frm = $('#form-expedientes');
		$("#Option").val('data-grid-cronologia');
		
		 
		$.ajax({
            type: 'POST',
            url: 'proceso.php',
            data: frm.serialize(),
            success: function (data) { 
				console.log(data);
				obj = JSON.parse(data); 
				$("#data-grid-load-cronologia").html('');
				var filas = '';
				indice = 0;
				for(i=0; i<obj.Cronologia.length; i++){
					//$("#data-grid-load-registros").append('<option value="'+obj.Cronologia[i].ProvinciaId+'">'+obj.Provincias[i].Nombre+'</option>');
					indice++
					filas += '<tr><th nowrap="nowrap"><a href="#">'+indice+'</a></th>';
                    filas += '<td align="center" nowrap="nowrap">'+obj.Cronologia[i].Ubicacion+'</td>';
					filas += '<td nowrap="nowrap">'+obj.Cronologia[i].EnviadoPor+'</td>';       
					filas += '<td align="center" nowrap="nowrap">'+obj.Cronologia[i].Fecha+'</td>';       
					filas += '<td nowrap="nowrap">'+obj.Cronologia[i].Estatus+'</td>';           
					filas += '</tr>';
				}
				$("#data-grid-load-cronologia").html(filas);
				$("#Option").val('update');		
				
            },
            error: function (data) {
			
               $("#Modal-base").modal('show');
				$("#Modal-base .modal-title").text('Cargar datos');
				$("#Modal-base .modal-body p").text('Un error ha ocurrido verifique su conexión o consulte a soporte técnico');
				//$("#Modal-base .modal-footer button").attr('id','btn-cerrar-modal');
				
					$("#Option").val('update');		
				
            },
        });	
	}
	
	function DataGridCronologiaData(data){
				//alert(data);
				obj = JSON.parse(data); 
				$("#data-grid-load-cronologia").html('');
				var filas = '';
				indice = 0;
				for(i=0; i<obj.Cronologia.length; i++){
					//$("#data-grid-load-registros").append('<option value="'+obj.Cronologia[i].ProvinciaId+'">'+obj.Provincias[i].Nombre+'</option>');
					indice++
					filas += '<tr><th nowrap="nowrap"><a href="#">'+indice+'</a></th>';
                    filas += '<td align="center" nowrap="nowrap">'+obj.Cronologia[i].Ubicacion+'</td>';
					filas += '<td nowrap="nowrap">'+obj.Cronologia[i].EnviadoPor+'</td>';       
					filas += '<td align="center" nowrap="nowrap">'+obj.Cronologia[i].Fecha+'</td>';       
					filas += '<td nowrap="nowrap">'+obj.Cronologia[i].Estatus+'</td>';           
					filas += '</tr>';
				}
				$("#data-grid-load-cronologia").html(filas);
				$("#Option").val('update');		
				
           
	}
function OpenRegistro(registro){
	$("#Option").val('ver-detalle-expediente');
	$("#IdExpediente").val(registro);
	 var frm = $('#form-expedientes');
	$.ajax({
            type: 'POST',
            url: 'proceso.php',
            data: frm.serialize(),
            success: function (data) {
				$("#Option").val('update'); //alert($("#Option").val());
				//console.log(data);
				obj = JSON.parse(data); 
				$("#data-grid-load-registros").html('');
				var expediente = obj.Expediente[0];
				//for(i=0; i<obj.Expediente.length; i++){
				$("#label-IdExpediente").html(expediente.IdExpediente);
				$('#IdTipoDocumento option[value='+expediente.IdTipoDocumento+']').attr('selected','selected');
				$('#IdTipoPago option[value='+expediente.IdTipoPago+']').attr('selected','selected');
				FechaEmision = expediente.FechaEmision.split(' ');
				$("#FechaEmision").val(FechaEmision[0]);
				$("#NumeroDocumento").val(expediente.NumeroDocumento);
				$("#Asunto").val(expediente.Asunto);
				$("#Descripcion").val(expediente.Descripcion);
				$("#IdProcedencia").val(expediente.IdProcedencia);
				$("#Procedencia").val(expediente.Procedencia);
				$("#IdEntregadoPor").val(expediente.IdEntregadoPor);
				$("#EntregadoPor").val(expediente.EntregadoPor);
				FechaEntrega = expediente.FechaEntrega.split(' ');
				$("#FechaEntrega").val(FechaEntrega[0]);
				$("#IdBeneficiario").val(expediente.IdBeneficiario);
				$("#BeneficiarioCorreo").val(expediente.BeneficiarioCorreo);
				
				$("#NCF").val(expediente.NCF);
				$("#ValorFactura").val(expediente.ValorFactura);
				$("#Impuesto").val(expediente.Impuesto);
				//$("#IdTipoPago").val(expediente.IdTipoPago);
				$("#Monto").val(expediente.Monto);
				var number = numeral($("#Monto").val());
				number.format();
				numeral.defaultFormat('$0,0.00');		
				$("#Monto").val(number.format());
				//
				$("#Observacion").val(expediente.Observacion);
				$("#Beneficiario").val(expediente.Beneficiario);
				//$("#TipoPago").val(expediente.TipoPago);
				$("#HoraEntrega").val(expediente.HoraEntrega);
				$("#fecha-registro").html(expediente.FechaRegistro);
				$("#registrado-por").html(expediente.RegistradoPor);
				$("#ultima-modificacion").html(expediente.UltimaModificacion);
				$("#modificado-por").html(expediente.ModificadoPor);
				$("#ultima-ubicacion").html(expediente.UltimaUbicacion);
				$("#ultimo-estatus").html(expediente.Estatus);
				
				//alert(data);
				$("#vista2").fadeIn("fast");	
				$("#btn-nuevo").hide();
				$("#btnSalvar").show();
				//}
				DataGridCronologia();
            },
            error: function (data) {
			
               $("#Modal-base").modal('show');
				$("#Modal-base .modal-title").text('Seleccionar registro');
				$("#Modal-base .modal-body p").text('Un error ha ocurrido verifique su conexión o consulte a soporte técnico');
				//$("#Modal-base .modal-footer button").attr('id','btn-cerrar-modal');
				$("#Option").val('ver-detalle-expediente');
            },
        });
	
	
}

function OpenRegistroDetalle(data){	
				obj = JSON.parse(data); 
				$("#data-grid-load-registros").html('');
				var expediente = obj.Expediente[0];
				//var cronologia = obj.Expediente[2];
				//for(i=0; i<obj.Expediente.length; i++){
				$("#label-IdExpediente").html(expediente.IdExpediente);
				$('#IdTipoDocumento option[value='+expediente.IdTipoDocumento+']').attr('selected','selected');
				$('#IdTipoPago option[value='+expediente.IdTipoPago+']').attr('selected','selected');
				FechaEmision = expediente.FechaEmision.split(' ');
				$("#FechaEmision").val(FechaEmision[0]);
				$("#NumeroDocumento").val(expediente.NumeroDocumento);
				$("#Asunto").val(expediente.Asunto);
				$("#Descripcion").val(expediente.Descripcion);
				$("#IdProcedencia").val(expediente.IdProcedencia);
				$("#Procedencia").val(expediente.Procedencia);
				$("#IdEntregadoPor").val(expediente.IdEntregadoPor);
				$("#EntregadoPor").val(expediente.EntregadoPor);
				FechaEntrega = expediente.FechaEntrega.split(' ');
				$("#FechaEntrega").val(FechaEntrega[0]);
				$("#IdBeneficiario").val(expediente.IdBeneficiario);
				$("#BeneficiarioCorreo").val(expediente.BeneficiarioCorreo);
				$("#NCF").val(expediente.NCF);
				$("#ValorFactura").val(expediente.ValorFactura);
				$("#Impuesto").val(expediente.Impuesto);
				//$("#IdTipoPago").val(expediente.IdTipoPago);
				$("#Monto").val(expediente.Monto);
				var number = numeral($("#Monto").val());
				number.format();
				numeral.defaultFormat('$0,0.00');		
				$("#Monto").val(number.format());
				//
				$("#Observacion").val(expediente.Observacion);
				$("#Beneficiario").val(expediente.Beneficiario);
				//$("#TipoPago").val(expediente.TipoPago);
				$("#HoraEntrega").val(expediente.HoraEntrega);
				$("#fecha-registro").html(expediente.FechaRegistro);
				$("#registrado-por").html(expediente.RegistradoPor);
				$("#ultima-modificacion").html(expediente.UltimaModificacion);
				$("#modificado-por").html(expediente.ModificadoPor);
				$("#ultima-ubicacion").html(expediente.UltimaUbicacion);
				$("#ultimo-estatus").html(expediente.Estatus);				
				//alert(data);
				$("#vista2").fadeIn("fast");	
				$("#btn-nuevo").hide();
				$("#btnSalvar").show();
				//}
				//DataGridCronologiaData(cronologia);
				$(".modal-header").css("background-color","#0070cd");
            
	
	
}

function FiltrarProcedencias() {
  var input, filter, table, tr, td, i;
  input = document.getElementById("filtrar-procedencias");
  filter = input.value.toUpperCase();
  table = document.getElementById("tb-registros-procedencias");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[0];
    if (td) {
      if (td.innerHTML.toUpperCase().indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}

function FiltrarMensajeros() {
  var input, filter, table, tr, td, i;
  input = document.getElementById("filtrar-mensajeros");
  filter = input.value.toUpperCase();
  table = document.getElementById("tb-registros-mensajeros");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[0];
    if (td) {
      if (td.innerHTML.toUpperCase().indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}

function FiltrarSuplidores() {
  var input, filter, table, tr, td, i;
  input = document.getElementById("filtrar-suplidores");
  filter = input.value.toUpperCase();
  table = document.getElementById("tb-registros-suplidores");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[0];
    if (td) {
      if (td.innerHTML.toUpperCase().indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}
function SeleccioanrProcedencia(IdProcedencia,Procedencia){
	$("#IdProcedencia").val(IdProcedencia);
	$("#Procedencia").val(Procedencia);
	$('#Modal-procedencias').modal('hide');	
	$("#Procedencia").focus();		
}
function SeleccioanrMensajero(IdMensajero,Mensajero){
	$("#IdEntregadoPor").val(IdMensajero);
	$("#EntregadoPor").val(Mensajero);
	$('#Modal-mensajeros').modal('hide');
	$("#EntregadoPor").focus();		
}
function SeleccioanrSuplidor(IdBeneficiario,Beneficiario){
	$("#IdBeneficiario").val(IdBeneficiario);
	$("#Beneficiario").val(Beneficiario);
	$('#Modal-beneficiarios').modal('hide');
	$("#Beneficiario").focus();	
}
function Procedencias(){
		var frm = $('#form-expedientes');
		$("#Option").val('load-procedencias');	
		$(".modal-header").css("background-color","#0070cd");
		 
		$.ajax({
            type: 'POST',
            url: 'proceso.php',
            data: frm.serialize(),
            success: function (data) {
				//console.log(data);
				obj = JSON.parse(data); 
				var Procedencia = null;
				$("#data-grid-load-registros-procedencias").html('');
				var filas = '';
				for(i=0; i<obj.Procedencias.length; i++){
					Procedencia = obj.Procedencias[i];
					//$("#data-grid-load-registros").append('<option value="'+obj.Expedientes[i].ProvinciaId+'">'+obj.Provincias[i].Nombre+'</option>');
					filas += '<tr onDblClick="SeleccioanrProcedencia(\''+Procedencia.IdProcedencia+'\',\''+Procedencia.Procedencia+'\')">';
                    filas += '<td  nowrap="nowrap">'+Procedencia.Procedencia+'</td>';     
					filas += '</tr>';
				}
				$("#data-grid-load-registros-procedencias").html(filas);
				$('#Modal-procedencias').modal('show');
				
				var IdExpediente = $("#IdExpediente").val();
		
				if(IdExpediente == ''){
					$("#Option").val('save');		
				}else{
					$("#Option").val('update');		
				}	
						
            },
            error: function (data) {
				var IdExpediente = $("#IdExpediente").val();
		
				if(IdExpediente == ''){
					$("#Option").val('save');		
				}else{
					$("#Option").val('update');		
				}
			
               $("#Modal-base").modal('show');
				$("#Modal-base .modal-title").text('Cargar datos');
				$("#Modal-base .modal-body p").text('Un error ha ocurrido verifique su conexión o consulte a soporte técnico');
				//$("#Modal-base .modal-footer button").attr('id','btn-cerrar-modal');
            },
        });	
		
	}
	
	function Mensajeros(){
		var frm = $('#form-expedientes');
		$("#Option").val('load-mensajeros');	
		$(".modal-header").css("background-color","#0070cd");
		 
		$.ajax({
            type: 'POST',
            url: 'proceso.php',
            data: frm.serialize(),
            success: function (data) {
				//console.log(data);
				obj = JSON.parse(data); 
				var Mensajero = null;
				$("#data-grid-load-registros-mensajeros").html('');
				var filas = '';
				for(i=0; i<obj.Mensajeros.length; i++){
					Mensajero = obj.Mensajeros[i];
					//$("#data-grid-load-registros").append('<option value="'+obj.Expedientes[i].ProvinciaId+'">'+obj.Provincias[i].Nombre+'</option>');
					filas += '<tr onDblClick="SeleccioanrMensajero(\''+Mensajero.IdEntregadoPor+'\',\''+Mensajero.EntregadoPor+'\')">';
                    filas += '<td  nowrap="nowrap">'+Mensajero.EntregadoPor+'</td>';     
					filas += '</tr>';
				}
				$("#data-grid-load-registros-mensajeros").html(filas);
				$('#Modal-mensajeros').modal('show');
				
				var IdExpediente = $("#IdExpediente").val();
		
				if(IdExpediente == ''){
					$("#Option").val('save');		
				}else{
					$("#Option").val('update');		
				}	
            },
            error: function (data) {
			
               $("#Modal-base").modal('show');
				$("#Modal-base .modal-title").text('Cargar datos');
				$("#Modal-base .modal-body p").text('Un error ha ocurrido verifique su conexión o consulte a soporte técnico');
				//$("#Modal-base .modal-footer button").attr('id','btn-cerrar-modal');
				
				var IdExpediente = $("#IdExpediente").val();
		
				if(IdExpediente == ''){
					$("#Option").val('save');		
				}else{
					$("#Option").val('update');		
				}
            },
        });	
	}
	
	function Beneficiarios(){
		var frm = $('#form-expedientes');
		$("#Option").val('load-beneficiarios');	
		 $(".modal-header").css("background-color","#0070cd");
		 
		$.ajax({
            type: 'POST',
            url: 'proceso.php',
            data: frm.serialize(),
            success: function (data) { 
				//console.log(data);
				
				obj = JSON.parse(data); 
				var Beneficiario = null;
				$("#data-grid-load-registros-beneficiarios").html('');
				var filas = '';
				for(i=0; i<obj.Beneficiarios.length; i++){
					Beneficiario = obj.Beneficiarios[i];
					//$("#data-grid-load-registros").append('<option value="'+obj.Expedientes[i].ProvinciaId+'">'+obj.Provincias[i].Nombre+'</option>');
					filas += '<tr onDblClick="SeleccioanrSuplidor(\''+Beneficiario.IdBeneficiario+'\',\''+Beneficiario.Beneficiario+'\')">';
                    filas += '<td  nowrap="nowrap">'+Beneficiario.Beneficiario+'</td>';     
					filas += '</tr>';
				}
				$("#data-grid-load-registros-beneficiarios").html(filas);
				$('#Modal-beneficiarios').modal('show');	
				
				var IdExpediente = $("#IdExpediente").val();
		
				if(IdExpediente == ''){
					$("#Option").val('save');		
				}else{
					$("#Option").val('update');		
				}
            },
            error: function (data) {
			
               $("#Modal-base").modal('show');
				$("#Modal-base .modal-title").text('Cargar datos');
				$("#Modal-base .modal-body p").text('Un error ha ocurrido verifique su conexión o consulte a soporte técnico');
				//$("#Modal-base .modal-footer button").attr('id','btn-cerrar-modal');
				
				var IdExpediente = $("#IdExpediente").val();
		
				if(IdExpediente == ''){
					$("#Option").val('save');		
				}else{
					$("#Option").val('update');		
				}
            },
        });	
	}
﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using SI_INAIPI.Controls.CxP;
using SI_INAIPI.DataAccess.CxP;
using SI_INAIPI.Models.CxP;
using INAIPI.Core;
using static SI_INAIPI.Global;
using System.Text.RegularExpressions;
using DevExpress.Web;
using System.IO;
using SI_INAIPI.Models.DIG;
using SI_INAIPI.Controls.DIG;
using DevExpress.Web.Demos;
using System.Net;

namespace GestionExpedientes
{
    public partial class Recibidos : BasePage
    {
        CxP_PagosExpedientesCtrl ctrl = new CxP_PagosExpedientesCtrl();
        private List<CxP_PagosExpedientesRecibidos> PagoExpedientesRecibidos
        {
            get { return (List<CxP_PagosExpedientesRecibidos>)Session["Default_PagoExpedientes_Recibidos"]; }
            set { Session["Default_PagoExpedientes_Recibidos"] = value; }
        }
        private CxP_PagosExpedientes PagoExpediente
        {
            get { return (CxP_PagosExpedientes)Session["Default_PagoExpediente"]; }
            set { Session["Default_PagoExpediente"] = value; }
        }
        private List<CxP_PagosExpedientes> PagoExpedientes
        {
            get { return (List<CxP_PagosExpedientes>)Session["Default_PagoExpedientes"]; }
            set { Session["Default_PagoExpedientes"] = value; }
        }
        private string SeccionActiva
        {
            get { return (string)Session["Default_SeccionActiva"]; }
            set { Session["Default_SeccionActiva"] = value; }
        }
        private bool Editar
        {
            get { return (bool)Session["Default_Editar"]; }
            set { Session["Default_Editar"] = value; }
        }
        public void Totales(int remitente)
        {
            btnRegistros.Badge.Text = ctrl.TotalRegistrados(remitente).ToString();
            btnRecibidos.Badge.Text = ctrl.TotalRecibidos(remitente).ToString();
            btnEnviados.Badge.Text = ctrl.TotalEnviados(remitente).ToString();
        }
        public void Redireccionar(string page, string parametro, string current_page)
        {
            //if (!string.IsNullOrEmpty(parametro))
            //{
               
            //    PagoExpediente = PagoExpedientes.FirstOrDefault(x => x.IdExpediente == parametro.ToInt());

            //}
            if (current_page == "Registrados" || current_page == "Enviados")
            {
                PagoExpediente = PagoExpedientes.FirstOrDefault(x => x.IdExpediente == parametro.ToInt());
            }
            if (current_page == "Recibidos")
            {
                PagoExpediente = PagoExpedientesRecibidos.FirstOrDefault(x => x.IdRegistro == parametro.ToInt());
            }
           
            string strPathAndQuery = HttpContext.Current.Request.Url.PathAndQuery;
            string strUrl = HttpContext.Current.Request.Url.AbsoluteUri.Replace(strPathAndQuery, "/");

            string[] split = strPathAndQuery.Split('?');

            DevExpress.Web.ASPxWebControl.RedirectOnCallback(strUrl + page + ".aspx?" + split[1]);

        }
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsCallback)
            {
                string UsuarioRemitente = ctrl.Decode(Request.QueryString[ctrl.Encode("UsuarioRemitente")] ?? ""); //Request.QueryString["UsuarioRemitente"];
                string DepartamentoRemitente = ctrl.Decode(Request.QueryString[ctrl.Encode("DepartamentoRemitente")] ?? ""); //Request.QueryString["DepartamentoRemitente"];
                string Rol = ctrl.Decode(Request.QueryString[ctrl.Encode("Rol")] ?? ""); //Request.QueryString["Rol"];
                string btnSeccionado = ctrl.Decode(Request.QueryString[ctrl.Encode("btnSeccionado")] ?? ""); //Request.QueryString["Rol"];

                if (string.IsNullOrEmpty(UsuarioRemitente) | string.IsNullOrEmpty(DepartamentoRemitente) | string.IsNullOrEmpty(Rol))
                {
                    Response.Redirect(System.Configuration.ConfigurationManager.AppSettings["MenuPrincipalUrl"]);
                }

                Session["UsuarioRemitente"] = UsuarioRemitente; //"2023";
                Session["DepartamentoRemitente"] = DepartamentoRemitente; // "22"; //22, 30, 23, 25
                Session["Rol"] = Rol; //"Administrador";               


                //PreviewDocumento.Visible = false;

                Usuario userLogin = new Usuario();
                userLogin = ctrl.Usuario(UsuarioRemitente.ToString().ToInt());

                LoginUser.InnerText = userLogin.NombreCompleto;
                LoginDepartamento.InnerText = userLogin.Departamento;

                ListadoExpedientesRecibidos(true);
                Totales(DepartamentoRemitente.ToString().ToInt());
            }
        }
        protected void Page_Init(object sender, EventArgs e)
        {
            grdListadoExpedientesRecibidos.DataSource = PagoExpedientesRecibidos;
            grdListadoExpedientesRecibidos.DataBind();
        }
        private void ListadoExpedientesRecibidos(bool getData)
        {
            if (getData)
            {
                PagoExpedientesRecibidos = ctrl.ListExpedientesRecibidos(Session["DepartamentoRemitente"].ToString().ToInt());
            }

            grdListadoExpedientesRecibidos.DataSource = PagoExpedientesRecibidos;
            grdListadoExpedientesRecibidos.DataBind();
        }

        protected void cbPrincipal_Callback(object sender, DevExpress.Web.CallbackEventArgsBase e)
        {
            string[] parametro = e.Parameter.Split('|');

            switch (parametro[0])
            {
                case "ABRIR-EXPEDIENTE":
                    //PagoExpediente = null;
                    Editar = false;
                    SeccionActiva = "RECIBIDOS";
                    Redireccionar("Formulario", parametro[1],"Recibidos");
                    break;

                case "CONSULTAR-EXPEDIENTES":
                    SeccionActiva = "CONSULTAR";
                    Redireccionar("Consultar", parametro[1],"");
                    break;

                case "NUEVO":
                    Editar = false;
                    SeccionActiva = "REGISTRADOS";
                    PagoExpediente = null;
                    Redireccionar("Formulario", parametro[1],"");
                    break;

                case "EXPEDIENTES-REGISTRADOS":
                    Redireccionar("Registrados", parametro[1],"");
                    break;

                case "EXPEDIENTES-RECIBIDOS":
                    Redireccionar("Recibidos", parametro[1],"");
                    break;

                case "EXPEDIENTES-ENVIADOS":
                    Redireccionar("Enviados", parametro[1],"");
                    break;

            }
        }
    }
}
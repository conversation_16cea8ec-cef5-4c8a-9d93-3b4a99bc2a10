{"version": 3, "file": "popper-utils.js", "sources": ["../src/utils/getStyleComputedProperty.js", "../src/utils/getParentNode.js", "../src/utils/getScrollParent.js", "../src/utils/isIE.js", "../src/utils/getOffsetParent.js", "../src/utils/isOffsetContainer.js", "../src/utils/getRoot.js", "../src/utils/findCommonOffsetParent.js", "../src/utils/getScroll.js", "../src/utils/includeScroll.js", "../src/utils/getBordersSize.js", "../src/utils/getWindowSizes.js", "../src/utils/getClientRect.js", "../src/utils/getBoundingClientRect.js", "../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../src/utils/isFixed.js", "../src/utils/getFixedPositionOffsetParent.js", "../src/utils/getBoundaries.js", "../src/utils/computeAutoPlacement.js", "../src/utils/debounce.js", "../src/utils/find.js", "../src/utils/findIndex.js", "../src/utils/getOffsetRect.js", "../src/utils/getOuterSizes.js", "../src/utils/getOppositePlacement.js", "../src/utils/getPopperOffsets.js", "../src/utils/getReferenceOffsets.js", "../src/utils/getSupportedPropertyName.js", "../src/utils/isFunction.js", "../src/utils/isModifierEnabled.js", "../src/utils/isModifierRequired.js", "../src/utils/isNumeric.js", "../src/utils/getWindow.js", "../src/utils/removeEventListeners.js", "../src/utils/runModifiers.js", "../src/utils/setAttributes.js", "../src/utils/setStyles.js", "../src/utils/setupEventListeners.js", "../src/utils/index.js"], "sourcesContent": ["/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const css = getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Tells if you are running Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @argument {number} version to check\n * @returns {Boolean} isIE\n */\nconst cache = {};\n\nexport default function (version = 'all') {\n  version = version.toString();\n  if(cache.hasOwnProperty(version)){\n    return cache[version];\n  }\n  switch (version) {\n    case '11':\n      cache[version] = navigator.userAgent.indexOf('Trident') !== -1;\n      break;\n    case '10':\n      cache[version] = navigator.appVersion.indexOf('MSIE 10') !== -1;\n      break;\n    case 'all':\n      cache[version] = navigator.userAgent.indexOf('Trident') !== -1 || navigator.userAgent.indexOf('MSIE') !== -1;\n      break;\n  }\n\n  //Set IE\n  cache.all = cache.all || Object.keys(cache).some(key => cache[key]);\n  return cache[version];\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`], 10) +\n    parseFloat(styles[`border${sideB}Width`], 10)\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? html[`offset${axis}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]\n      : 0\n  );\n}\n\nexport default function getWindowSizes() {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  const width =\n    sizes.width || element.clientWidth || result.right - result.left;\n  const height =\n    sizes.height || element.clientHeight || result.bottom - result.top;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth, 10);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth, 10);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && parent.nodeName === 'HTML') {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop, 10);\n    const marginLeft = parseFloat(styles.marginLeft, 10);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes();\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "const isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\nconst longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nlet timeoutDuration = 0;\nfor (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import getWindowSizes from './getWindowSizes';\nimport getClientRect from './getClientRect';\n\n/**\n * Get the position of the given element, relative to its offset parent\n * @method\n * @memberof Popper.Utils\n * @param {Element} element\n * @return {Object} position - Coordinates of the element and its `scrollTop`\n */\nexport default function getOffsetRect(element) {\n  let elementRect;\n  if (element.nodeName === 'HTML') {\n    const { width, height } = getWindowSizes();\n    elementRect = {\n      width,\n      height,\n      left: 0,\n      top: 0,\n    };\n  } else {\n    elementRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight,\n      left: element.offsetLeft,\n      top: element.offsetTop,\n    };\n  }\n\n  // position\n  return getClientRect(elementRect);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const styles = getComputedStyle(element);\n  const x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  const y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import computeAutoPlacement from './computeAutoPlacement';\nimport debounce from './debounce';\nimport findIndex from './findIndex';\nimport getBordersSize from './getBordersSize';\nimport getBoundaries from './getBoundaries';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getClientRect from './getClientRect';\nimport getOffsetParent from './getOffsetParent';\nimport getOffsetRect from './getOffsetRect';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getOuterSizes from './getOuterSizes';\nimport getParentNode from './getParentNode';\nimport getPopperOffsets from './getPopperOffsets';\nimport getReferenceOffsets from './getReferenceOffsets';\nimport getScroll from './getScroll';\nimport getScrollParent from './getScrollParent';\nimport getStyleComputedProperty from './getStyleComputedProperty';\nimport getSupportedPropertyName from './getSupportedPropertyName';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport isFunction from './isFunction';\nimport isModifierEnabled from './isModifierEnabled';\nimport isModifierRequired from './isModifierRequired';\nimport isNumeric from './isNumeric';\nimport removeEventListeners from './removeEventListeners';\nimport runModifiers from './runModifiers';\nimport setAttributes from './setAttributes';\nimport setStyles from './setStyles';\nimport setupEventListeners from './setupEventListeners';\n\n/** @namespace Popper.Utils */\nexport {\n  computeAutoPlacement,\n  debounce,\n  findIndex,\n  getBordersSize,\n  getBoundaries,\n  getBoundingClientRect,\n  getClientRect,\n  getOffsetParent,\n  getOffsetRect,\n  getOffsetRectRelativeToArbitraryNode,\n  getOuterSizes,\n  getParentNode,\n  getPopperOffsets,\n  getReferenceOffsets,\n  getScroll,\n  getScrollParent,\n  getStyleComputedProperty,\n  getSupportedPropertyName,\n  getWindowSizes,\n  isFixed,\n  isFunction,\n  isModifierEnabled,\n  isModifierRequired,\n  isNumeric,\n  removeEventListeners,\n  runModifiers,\n  setAttributes,\n  setStyles,\n  setupEventListeners,\n};\n\n// This is here just for backward compatibility with versions lower than v1.10.3\n// you should import the utilities using named exports, if you want them all use:\n// ```\n// import * as PopperUtils from 'popper-utils';\n// ```\n// The default export will be removed in the next major version.\nexport default {\n  computeAutoPlacement,\n  debounce,\n  findIndex,\n  getBordersSize,\n  getBoundaries,\n  getBoundingClientRect,\n  getClientRect,\n  getOffsetParent,\n  getOffsetRect,\n  getOffsetRectRelativeToArbitraryNode,\n  getOuterSizes,\n  getParentNode,\n  getPopperOffsets,\n  getReferenceOffsets,\n  getScroll,\n  getScrollParent,\n  getStyleComputedProperty,\n  getSupportedPropertyName,\n  getWindowSizes,\n  isFixed,\n  isFunction,\n  isModifierEnabled,\n  isModifierRequired,\n  isNumeric,\n  removeEventListeners,\n  runModifiers,\n  setAttributes,\n  setStyles,\n  setupEventListeners,\n};\n"], "names": ["getStyleComputedProperty", "element", "property", "nodeType", "css", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "document", "body", "ownerDocument", "overflow", "overflowX", "overflowY", "test", "cache", "version", "toString", "hasOwnProperty", "navigator", "userAgent", "indexOf", "appVersion", "all", "Object", "keys", "some", "key", "getOffsetParent", "documentElement", "noOffsetParent", "isIE", "offsetParent", "nextElement<PERSON><PERSON>ling", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "parseFloat", "getSize", "computedStyle", "Math", "max", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "e", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "fixedPosition", "isIE10", "runIsIE", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "window", "innerWidth", "innerHeight", "offset", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "reference", "padding", "boundariesElement", "boundaries", "boundariesNode", "getArea", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "map", "sort", "a", "b", "area", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "length", "variation", "split", "<PERSON><PERSON><PERSON><PERSON>", "longerTimeoutBrowsers", "timeoutDuration", "i", "microtaskDebounce", "fn", "called", "Promise", "resolve", "then", "taskDebounce", "scheduled", "supportsMicroTasks", "find", "arr", "check", "Array", "prototype", "findIndex", "prop", "value", "cur", "match", "obj", "getOffsetRect", "elementRect", "offsetLeft", "offsetTop", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "getReferenceOffsets", "state", "commonOffsetParent", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "slice", "prefix", "to<PERSON><PERSON><PERSON>", "style", "isFunction", "functionToCheck", "getType", "call", "isModifierEnabled", "modifiers", "modifierName", "name", "enabled", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "requested", "warn", "isNumeric", "n", "isNaN", "isFinite", "getWindow", "defaultView", "removeEventListeners", "removeEventListener", "updateBound", "scrollParents", "for<PERSON>ach", "target", "scrollElement", "eventsEnabled", "runModifiers", "data", "ends", "modifiersToRun", "undefined", "setAttributes", "attributes", "setAttribute", "removeAttribute", "setStyles", "unit", "attachToScrollParents", "event", "callback", "isBody", "addEventListener", "passive", "push", "setupEventListeners", "options"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;AAOA,AAAe,SAASA,wBAAT,CAAkCC,OAAlC,EAA2CC,QAA3C,EAAqD;MAC9DD,QAAQE,QAAR,KAAqB,CAAzB,EAA4B;WACnB,EAAP;;;QAGIC,MAAMC,iBAAiBJ,OAAjB,EAA0B,IAA1B,CAAZ;SACOC,WAAWE,IAAIF,QAAJ,CAAX,GAA2BE,GAAlC;;;ACbF;;;;;;;AAOA,AAAe,SAASE,aAAT,CAAuBL,OAAvB,EAAgC;MACzCA,QAAQM,QAAR,KAAqB,MAAzB,EAAiC;WACxBN,OAAP;;SAEKA,QAAQO,UAAR,IAAsBP,QAAQQ,IAArC;;;ACRF;;;;;;;AAOA,AAAe,SAASC,eAAT,CAAyBT,OAAzB,EAAkC;;MAE3C,CAACA,OAAL,EAAc;WACLU,SAASC,IAAhB;;;UAGMX,QAAQM,QAAhB;SACO,MAAL;SACK,MAAL;aACSN,QAAQY,aAAR,CAAsBD,IAA7B;SACG,WAAL;aACSX,QAAQW,IAAf;;;;QAIE,EAAEE,QAAF,EAAYC,SAAZ,EAAuBC,SAAvB,KAAqChB,yBAAyBC,OAAzB,CAA3C;MACI,wBAAwBgB,IAAxB,CAA6BH,WAAWE,SAAX,GAAuBD,SAApD,CAAJ,EAAoE;WAC3Dd,OAAP;;;SAGKS,gBAAgBJ,cAAcL,OAAd,CAAhB,CAAP;;;AC9BF;;;;;;;AAOA,MAAMiB,QAAQ,EAAd;;AAEA,WAAe,UAAUC,UAAU,KAApB,EAA2B;YAC9BA,QAAQC,QAAR,EAAV;MACGF,MAAMG,cAAN,CAAqBF,OAArB,CAAH,EAAiC;WACxBD,MAAMC,OAAN,CAAP;;UAEMA,OAAR;SACO,IAAL;YACQA,OAAN,IAAiBG,UAAUC,SAAV,CAAoBC,OAApB,CAA4B,SAA5B,MAA2C,CAAC,CAA7D;;SAEG,IAAL;YACQL,OAAN,IAAiBG,UAAUG,UAAV,CAAqBD,OAArB,CAA6B,SAA7B,MAA4C,CAAC,CAA9D;;SAEG,KAAL;YACQL,OAAN,IAAiBG,UAAUC,SAAV,CAAoBC,OAApB,CAA4B,SAA5B,MAA2C,CAAC,CAA5C,IAAiDF,UAAUC,SAAV,CAAoBC,OAApB,CAA4B,MAA5B,MAAwC,CAAC,CAA3G;;;;;QAKEE,GAAN,GAAYR,MAAMQ,GAAN,IAAaC,OAAOC,IAAP,CAAYV,KAAZ,EAAmBW,IAAnB,CAAwBC,OAAOZ,MAAMY,GAAN,CAA/B,CAAzB;SACOZ,MAAMC,OAAN,CAAP;;;AC1BF;;;;;;;AAOA,AAAe,SAASY,eAAT,CAAyB9B,OAAzB,EAAkC;MAC3C,CAACA,OAAL,EAAc;WACLU,SAASqB,eAAhB;;;QAGIC,iBAAiBC,KAAK,EAAL,IAAWvB,SAASC,IAApB,GAA2B,IAAlD;;;MAGIuB,eAAelC,QAAQkC,YAA3B;;SAEOA,iBAAiBF,cAAjB,IAAmChC,QAAQmC,kBAAlD,EAAsE;mBACrD,CAACnC,UAAUA,QAAQmC,kBAAnB,EAAuCD,YAAtD;;;QAGI5B,WAAW4B,gBAAgBA,aAAa5B,QAA9C;;MAEI,CAACA,QAAD,IAAaA,aAAa,MAA1B,IAAoCA,aAAa,MAArD,EAA6D;WACpDN,UAAUA,QAAQY,aAAR,CAAsBmB,eAAhC,GAAkDrB,SAASqB,eAAlE;;;;;MAMA,CAAC,IAAD,EAAO,OAAP,EAAgBR,OAAhB,CAAwBW,aAAa5B,QAArC,MAAmD,CAAC,CAApD,IACAP,yBAAyBmC,YAAzB,EAAuC,UAAvC,MAAuD,QAFzD,EAGE;WACOJ,gBAAgBI,YAAhB,CAAP;;;SAGKA,YAAP;;;ACpCa,SAASE,iBAAT,CAA2BpC,OAA3B,EAAoC;QAC3C,EAAEM,QAAF,KAAeN,OAArB;MACIM,aAAa,MAAjB,EAAyB;WAChB,KAAP;;SAGAA,aAAa,MAAb,IAAuBwB,gBAAgB9B,QAAQqC,iBAAxB,MAA+CrC,OADxE;;;ACPF;;;;;;;AAOA,AAAe,SAASsC,OAAT,CAAiBC,IAAjB,EAAuB;MAChCA,KAAKhC,UAAL,KAAoB,IAAxB,EAA8B;WACrB+B,QAAQC,KAAKhC,UAAb,CAAP;;;SAGKgC,IAAP;;;ACRF;;;;;;;;AAQA,AAAe,SAASC,sBAAT,CAAgCC,QAAhC,EAA0CC,QAA1C,EAAoD;;MAE7D,CAACD,QAAD,IAAa,CAACA,SAASvC,QAAvB,IAAmC,CAACwC,QAApC,IAAgD,CAACA,SAASxC,QAA9D,EAAwE;WAC/DQ,SAASqB,eAAhB;;;;QAIIY,QACJF,SAASG,uBAAT,CAAiCF,QAAjC,IACAG,KAAKC,2BAFP;QAGMC,QAAQJ,QAAQF,QAAR,GAAmBC,QAAjC;QACMM,MAAML,QAAQD,QAAR,GAAmBD,QAA/B;;;QAGMQ,QAAQvC,SAASwC,WAAT,EAAd;QACMC,QAAN,CAAeJ,KAAf,EAAsB,CAAtB;QACMK,MAAN,CAAaJ,GAAb,EAAkB,CAAlB;QACM,EAAEK,uBAAF,KAA8BJ,KAApC;;;MAIGR,aAAaY,uBAAb,IACCX,aAAaW,uBADf,IAEAN,MAAMO,QAAN,CAAeN,GAAf,CAHF,EAIE;QACIZ,kBAAkBiB,uBAAlB,CAAJ,EAAgD;aACvCA,uBAAP;;;WAGKvB,gBAAgBuB,uBAAhB,CAAP;;;;QAIIE,eAAejB,QAAQG,QAAR,CAArB;MACIc,aAAa/C,IAAjB,EAAuB;WACdgC,uBAAuBe,aAAa/C,IAApC,EAA0CkC,QAA1C,CAAP;GADF,MAEO;WACEF,uBAAuBC,QAAvB,EAAiCH,QAAQI,QAAR,EAAkBlC,IAAnD,CAAP;;;;ACjDJ;;;;;;;;AAQA,AAAe,SAASgD,SAAT,CAAmBxD,OAAnB,EAA4ByD,OAAO,KAAnC,EAA0C;QACjDC,YAAYD,SAAS,KAAT,GAAiB,WAAjB,GAA+B,YAAjD;QACMnD,WAAWN,QAAQM,QAAzB;;MAEIA,aAAa,MAAb,IAAuBA,aAAa,MAAxC,EAAgD;UACxCqD,OAAO3D,QAAQY,aAAR,CAAsBmB,eAAnC;UACM6B,mBAAmB5D,QAAQY,aAAR,CAAsBgD,gBAAtB,IAA0CD,IAAnE;WACOC,iBAAiBF,SAAjB,CAAP;;;SAGK1D,QAAQ0D,SAAR,CAAP;;;AChBF;;;;;;;;;AASA,AAAe,SAASG,aAAT,CAAuBC,IAAvB,EAA6B9D,OAA7B,EAAsC+D,WAAW,KAAjD,EAAwD;QAC/DC,YAAYR,UAAUxD,OAAV,EAAmB,KAAnB,CAAlB;QACMiE,aAAaT,UAAUxD,OAAV,EAAmB,MAAnB,CAAnB;QACMkE,WAAWH,WAAW,CAAC,CAAZ,GAAgB,CAAjC;OACKI,GAAL,IAAYH,YAAYE,QAAxB;OACKE,MAAL,IAAeJ,YAAYE,QAA3B;OACKG,IAAL,IAAaJ,aAAaC,QAA1B;OACKI,KAAL,IAAcL,aAAaC,QAA3B;SACOJ,IAAP;;;ACnBF;;;;;;;;;;AAUA,AAAe,SAASS,cAAT,CAAwBC,MAAxB,EAAgCC,IAAhC,EAAsC;QAC7CC,QAAQD,SAAS,GAAT,GAAe,MAAf,GAAwB,KAAtC;QACME,QAAQD,UAAU,MAAV,GAAmB,OAAnB,GAA6B,QAA3C;;SAGEE,WAAWJ,OAAQ,SAAQE,KAAM,OAAtB,CAAX,EAA0C,EAA1C,IACAE,WAAWJ,OAAQ,SAAQG,KAAM,OAAtB,CAAX,EAA0C,EAA1C,CAFF;;;ACZF,SAASE,OAAT,CAAiBJ,IAAjB,EAAuB9D,IAAvB,EAA6BgD,IAA7B,EAAmCmB,aAAnC,EAAkD;SACzCC,KAAKC,GAAL,CACLrE,KAAM,SAAQ8D,IAAK,EAAnB,CADK,EAEL9D,KAAM,SAAQ8D,IAAK,EAAnB,CAFK,EAGLd,KAAM,SAAQc,IAAK,EAAnB,CAHK,EAILd,KAAM,SAAQc,IAAK,EAAnB,CAJK,EAKLd,KAAM,SAAQc,IAAK,EAAnB,CALK,EAMLxC,KAAK,EAAL,IACI0B,KAAM,SAAQc,IAAK,EAAnB,IACAK,cAAe,SAAQL,SAAS,QAAT,GAAoB,KAApB,GAA4B,MAAO,EAA1D,CADA,GAEAK,cAAe,SAAQL,SAAS,QAAT,GAAoB,QAApB,GAA+B,OAAQ,EAA9D,CAHJ,GAII,CAVC,CAAP;;;AAcF,AAAe,SAASQ,cAAT,GAA0B;QACjCtE,OAAOD,SAASC,IAAtB;QACMgD,OAAOjD,SAASqB,eAAtB;QACM+C,gBAAgB7C,KAAK,EAAL,KAAY7B,iBAAiBuD,IAAjB,CAAlC;;SAEO;YACGkB,QAAQ,QAAR,EAAkBlE,IAAlB,EAAwBgD,IAAxB,EAA8BmB,aAA9B,CADH;WAEED,QAAQ,OAAR,EAAiBlE,IAAjB,EAAuBgD,IAAvB,EAA6BmB,aAA7B;GAFT;;;;;;;;;;;;;;;;;ACtBF;;;;;;;AAOA,AAAe,SAASI,aAAT,CAAuBC,OAAvB,EAAgC;sBAExCA,OADL;WAESA,QAAQd,IAAR,GAAec,QAAQC,KAFhC;YAGUD,QAAQhB,GAAR,GAAcgB,QAAQE;;;;ACJlC;;;;;;;AAOA,AAAe,SAASC,qBAAT,CAA+BtF,OAA/B,EAAwC;MACjD8D,OAAO,EAAX;;;;;MAKI;QACE7B,KAAK,EAAL,CAAJ,EAAc;aACLjC,QAAQsF,qBAAR,EAAP;YACMtB,YAAYR,UAAUxD,OAAV,EAAmB,KAAnB,CAAlB;YACMiE,aAAaT,UAAUxD,OAAV,EAAmB,MAAnB,CAAnB;WACKmE,GAAL,IAAYH,SAAZ;WACKK,IAAL,IAAaJ,UAAb;WACKG,MAAL,IAAeJ,SAAf;WACKM,KAAL,IAAcL,UAAd;KAPF,MASK;aACIjE,QAAQsF,qBAAR,EAAP;;GAXJ,CAcA,OAAMC,CAAN,EAAQ;;QAEFC,SAAS;UACP1B,KAAKO,IADE;SAERP,KAAKK,GAFG;WAGNL,KAAKQ,KAAL,GAAaR,KAAKO,IAHZ;YAILP,KAAKM,MAAL,GAAcN,KAAKK;GAJ7B;;;QAQMsB,QAAQzF,QAAQM,QAAR,KAAqB,MAArB,GAA8B2E,gBAA9B,GAAiD,EAA/D;QACMG,QACJK,MAAML,KAAN,IAAepF,QAAQ0F,WAAvB,IAAsCF,OAAOlB,KAAP,GAAekB,OAAOnB,IAD9D;QAEMgB,SACJI,MAAMJ,MAAN,IAAgBrF,QAAQ2F,YAAxB,IAAwCH,OAAOpB,MAAP,GAAgBoB,OAAOrB,GADjE;;MAGIyB,iBAAiB5F,QAAQ6F,WAAR,GAAsBT,KAA3C;MACIU,gBAAgB9F,QAAQ+F,YAAR,GAAuBV,MAA3C;;;;MAIIO,kBAAkBE,aAAtB,EAAqC;UAC7BtB,SAASzE,yBAAyBC,OAAzB,CAAf;sBACkBuE,eAAeC,MAAf,EAAuB,GAAvB,CAAlB;qBACiBD,eAAeC,MAAf,EAAuB,GAAvB,CAAjB;;WAEOY,KAAP,IAAgBQ,cAAhB;WACOP,MAAP,IAAiBS,aAAjB;;;SAGKZ,cAAcM,MAAd,CAAP;;;ACzDa,SAASQ,oCAAT,CAA8CC,QAA9C,EAAwDC,MAAxD,EAAgEC,gBAAgB,KAAhF,EAAuF;QAC9FC,SAASC,KAAQ,EAAR,CAAf;QACMC,SAASJ,OAAO5F,QAAP,KAAoB,MAAnC;QACMiG,eAAejB,sBAAsBW,QAAtB,CAArB;QACMO,aAAalB,sBAAsBY,MAAtB,CAAnB;QACMO,eAAehG,gBAAgBwF,QAAhB,CAArB;;QAEMzB,SAASzE,yBAAyBmG,MAAzB,CAAf;QACMQ,iBAAiB9B,WAAWJ,OAAOkC,cAAlB,EAAkC,EAAlC,CAAvB;QACMC,kBAAkB/B,WAAWJ,OAAOmC,eAAlB,EAAmC,EAAnC,CAAxB;;;MAGGR,iBAAiBD,OAAO5F,QAAP,KAAoB,MAAxC,EAAgD;eACnC6D,GAAX,GAAiBY,KAAKC,GAAL,CAASwB,WAAWrC,GAApB,EAAyB,CAAzB,CAAjB;eACWE,IAAX,GAAkBU,KAAKC,GAAL,CAASwB,WAAWnC,IAApB,EAA0B,CAA1B,CAAlB;;MAEEc,UAAUD,cAAc;SACrBqB,aAAapC,GAAb,GAAmBqC,WAAWrC,GAA9B,GAAoCuC,cADf;UAEpBH,aAAalC,IAAb,GAAoBmC,WAAWnC,IAA/B,GAAsCsC,eAFlB;WAGnBJ,aAAanB,KAHM;YAIlBmB,aAAalB;GAJT,CAAd;UAMQuB,SAAR,GAAoB,CAApB;UACQC,UAAR,GAAqB,CAArB;;;;;;MAMI,CAACT,MAAD,IAAWE,MAAf,EAAuB;UACfM,YAAYhC,WAAWJ,OAAOoC,SAAlB,EAA6B,EAA7B,CAAlB;UACMC,aAAajC,WAAWJ,OAAOqC,UAAlB,EAA8B,EAA9B,CAAnB;;YAEQ1C,GAAR,IAAeuC,iBAAiBE,SAAhC;YACQxC,MAAR,IAAkBsC,iBAAiBE,SAAnC;YACQvC,IAAR,IAAgBsC,kBAAkBE,UAAlC;YACQvC,KAAR,IAAiBqC,kBAAkBE,UAAnC;;;YAGQD,SAAR,GAAoBA,SAApB;YACQC,UAAR,GAAqBA,UAArB;;;MAIAT,UAAU,CAACD,aAAX,GACID,OAAO5C,QAAP,CAAgBmD,YAAhB,CADJ,GAEIP,WAAWO,YAAX,IAA2BA,aAAanG,QAAb,KAA0B,MAH3D,EAIE;cACUuD,cAAcsB,OAAd,EAAuBe,MAAvB,CAAV;;;SAGKf,OAAP;;;ACtDa,SAAS2B,6CAAT,CAAuD9G,OAAvD,EAAgE+G,gBAAgB,KAAhF,EAAuF;QAC9FpD,OAAO3D,QAAQY,aAAR,CAAsBmB,eAAnC;QACMiF,iBAAiBhB,qCAAqChG,OAArC,EAA8C2D,IAA9C,CAAvB;QACMyB,QAAQL,KAAKC,GAAL,CAASrB,KAAK+B,WAAd,EAA2BuB,OAAOC,UAAP,IAAqB,CAAhD,CAAd;QACM7B,SAASN,KAAKC,GAAL,CAASrB,KAAKgC,YAAd,EAA4BsB,OAAOE,WAAP,IAAsB,CAAlD,CAAf;;QAEMnD,YAAY,CAAC+C,aAAD,GAAiBvD,UAAUG,IAAV,CAAjB,GAAmC,CAArD;QACMM,aAAa,CAAC8C,aAAD,GAAiBvD,UAAUG,IAAV,EAAgB,MAAhB,CAAjB,GAA2C,CAA9D;;QAEMyD,SAAS;SACRpD,YAAYgD,eAAe7C,GAA3B,GAAiC6C,eAAeJ,SADxC;UAEP3C,aAAa+C,eAAe3C,IAA5B,GAAmC2C,eAAeH,UAF3C;SAAA;;GAAf;;SAOO3B,cAAckC,MAAd,CAAP;;;ACjBF;;;;;;;;AAQA,AAAe,SAASC,OAAT,CAAiBrH,OAAjB,EAA0B;QACjCM,WAAWN,QAAQM,QAAzB;MACIA,aAAa,MAAb,IAAuBA,aAAa,MAAxC,EAAgD;WACvC,KAAP;;MAEEP,yBAAyBC,OAAzB,EAAkC,UAAlC,MAAkD,OAAtD,EAA+D;WACtD,IAAP;;SAEKqH,QAAQhH,cAAcL,OAAd,CAAR,CAAP;;;ACjBF;;;;;;;;AAQA,AAAe,SAASsH,4BAAT,CAAsCtH,OAAtC,EAA+C;;MAEvD,CAACA,OAAD,IAAY,CAACA,QAAQuH,aAArB,IAAsCtF,MAA1C,EAAkD;WAC1CvB,SAASqB,eAAhB;;MAEEyF,KAAKxH,QAAQuH,aAAjB;SACOC,MAAMzH,yBAAyByH,EAAzB,EAA6B,WAA7B,MAA8C,MAA3D,EAAmE;SAC5DA,GAAGD,aAAR;;SAEKC,MAAM9G,SAASqB,eAAtB;;;ACVF;;;;;;;;;;;AAWA,AAAe,SAAS0F,aAAT,CACbC,MADa,EAEbC,SAFa,EAGbC,OAHa,EAIbC,iBAJa,EAKb1B,gBAAgB,KALH,EAMb;;;MAGI2B,aAAa,EAAE3D,KAAK,CAAP,EAAUE,MAAM,CAAhB,EAAjB;QACMnC,eAAeiE,gBAAgBmB,6BAA6BI,MAA7B,CAAhB,GAAuDlF,uBAAuBkF,MAAvB,EAA+BC,SAA/B,CAA5E;;;MAGIE,sBAAsB,UAA1B,EAAuC;iBACxBf,8CAA8C5E,YAA9C,EAA4DiE,aAA5D,CAAb;GADF,MAIK;;QAEC4B,cAAJ;QACIF,sBAAsB,cAA1B,EAA0C;uBACvBpH,gBAAgBJ,cAAcsH,SAAd,CAAhB,CAAjB;UACII,eAAezH,QAAf,KAA4B,MAAhC,EAAwC;yBACrBoH,OAAO9G,aAAP,CAAqBmB,eAAtC;;KAHJ,MAKO,IAAI8F,sBAAsB,QAA1B,EAAoC;uBACxBH,OAAO9G,aAAP,CAAqBmB,eAAtC;KADK,MAEA;uBACY8F,iBAAjB;;;UAGI1C,UAAUa,qCACd+B,cADc,EAEd7F,YAFc,EAGdiE,aAHc,CAAhB;;;QAOI4B,eAAezH,QAAf,KAA4B,MAA5B,IAAsC,CAAC+G,QAAQnF,YAAR,CAA3C,EAAkE;YAC1D,EAAEmD,MAAF,EAAUD,KAAV,KAAoBH,gBAA1B;iBACWd,GAAX,IAAkBgB,QAAQhB,GAAR,GAAcgB,QAAQyB,SAAxC;iBACWxC,MAAX,GAAoBiB,SAASF,QAAQhB,GAArC;iBACWE,IAAX,IAAmBc,QAAQd,IAAR,GAAec,QAAQ0B,UAA1C;iBACWvC,KAAX,GAAmBc,QAAQD,QAAQd,IAAnC;KALF,MAMO;;mBAEQc,OAAb;;;;;aAKOd,IAAX,IAAmBuD,OAAnB;aACWzD,GAAX,IAAkByD,OAAlB;aACWtD,KAAX,IAAoBsD,OAApB;aACWxD,MAAX,IAAqBwD,OAArB;;SAEOE,UAAP;;;AC1EF,SAASE,OAAT,CAAiB,EAAE5C,KAAF,EAASC,MAAT,EAAjB,EAAoC;SAC3BD,QAAQC,MAAf;;;;;;;;;;;;AAYF,AAAe,SAAS4C,oBAAT,CACbC,SADa,EAEbC,OAFa,EAGbT,MAHa,EAIbC,SAJa,EAKbE,iBALa,EAMbD,UAAU,CANG,EAOb;MACIM,UAAU3G,OAAV,CAAkB,MAAlB,MAA8B,CAAC,CAAnC,EAAsC;WAC7B2G,SAAP;;;QAGIJ,aAAaL,cACjBC,MADiB,EAEjBC,SAFiB,EAGjBC,OAHiB,EAIjBC,iBAJiB,CAAnB;;QAOMO,QAAQ;SACP;aACIN,WAAW1C,KADf;cAEK+C,QAAQhE,GAAR,GAAc2D,WAAW3D;KAHvB;WAKL;aACE2D,WAAWxD,KAAX,GAAmB6D,QAAQ7D,KAD7B;cAEGwD,WAAWzC;KAPT;YASJ;aACCyC,WAAW1C,KADZ;cAEE0C,WAAW1D,MAAX,GAAoB+D,QAAQ/D;KAX1B;UAaN;aACG+D,QAAQ9D,IAAR,GAAeyD,WAAWzD,IAD7B;cAEIyD,WAAWzC;;GAfvB;;QAmBMgD,cAAc3G,OAAOC,IAAP,CAAYyG,KAAZ,EACjBE,GADiB,CACbzG;;KAEAuG,MAAMvG,GAAN,CAFA;UAGGmG,QAAQI,MAAMvG,GAAN,CAAR;IAJU,EAMjB0G,IANiB,CAMZ,CAACC,CAAD,EAAIC,CAAJ,KAAUA,EAAEC,IAAF,GAASF,EAAEE,IANT,CAApB;;QAQMC,gBAAgBN,YAAYO,MAAZ,CACpB,CAAC,EAAExD,KAAF,EAASC,MAAT,EAAD,KACED,SAASsC,OAAOhC,WAAhB,IAA+BL,UAAUqC,OAAO/B,YAF9B,CAAtB;;QAKMkD,oBAAoBF,cAAcG,MAAd,GAAuB,CAAvB,GACtBH,cAAc,CAAd,EAAiB9G,GADK,GAEtBwG,YAAY,CAAZ,EAAexG,GAFnB;;QAIMkH,YAAYb,UAAUc,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAlB;;SAEOH,qBAAqBE,YAAa,IAAGA,SAAU,EAA1B,GAA8B,EAAnD,CAAP;;;ACxEF,MAAME,YAAY,OAAOhC,MAAP,KAAkB,WAAlB,IAAiC,OAAOvG,QAAP,KAAoB,WAAvE;AACA,MAAMwI,wBAAwB,CAAC,MAAD,EAAS,SAAT,EAAoB,SAApB,CAA9B;AACA,IAAIC,kBAAkB,CAAtB;AACA,KAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIF,sBAAsBJ,MAA1C,EAAkDM,KAAK,CAAvD,EAA0D;MACpDH,aAAa5H,UAAUC,SAAV,CAAoBC,OAApB,CAA4B2H,sBAAsBE,CAAtB,CAA5B,KAAyD,CAA1E,EAA6E;sBACzD,CAAlB;;;;;AAKJ,AAAO,SAASC,iBAAT,CAA2BC,EAA3B,EAA+B;MAChCC,SAAS,KAAb;SACO,MAAM;QACPA,MAAJ,EAAY;;;aAGH,IAAT;WACOC,OAAP,CAAeC,OAAf,GAAyBC,IAAzB,CAA8B,MAAM;eACzB,KAAT;;KADF;GALF;;;AAYF,AAAO,SAASC,YAAT,CAAsBL,EAAtB,EAA0B;MAC3BM,YAAY,KAAhB;SACO,MAAM;QACP,CAACA,SAAL,EAAgB;kBACF,IAAZ;iBACW,MAAM;oBACH,KAAZ;;OADF,EAGGT,eAHH;;GAHJ;;;AAWF,MAAMU,qBAAqBZ,aAAahC,OAAOuC,OAA/C;;;;;;;;;;;AAYA,eAAgBK,qBACZR,iBADY,GAEZM,YAFJ;;ACjDA;;;;;;;;;AASA,AAAe,SAASG,IAAT,CAAcC,GAAd,EAAmBC,KAAnB,EAA0B;;MAEnCC,MAAMC,SAAN,CAAgBJ,IAApB,EAA0B;WACjBC,IAAID,IAAJ,CAASE,KAAT,CAAP;;;;SAIKD,IAAInB,MAAJ,CAAWoB,KAAX,EAAkB,CAAlB,CAAP;;;ACdF;;;;;;;;;AASA,AAAe,SAASG,SAAT,CAAmBJ,GAAnB,EAAwBK,IAAxB,EAA8BC,KAA9B,EAAqC;;MAE9CJ,MAAMC,SAAN,CAAgBC,SAApB,EAA+B;WACtBJ,IAAII,SAAJ,CAAcG,OAAOA,IAAIF,IAAJ,MAAcC,KAAnC,CAAP;;;;QAIIE,QAAQT,KAAKC,GAAL,EAAUS,OAAOA,IAAIJ,IAAJ,MAAcC,KAA/B,CAAd;SACON,IAAIxI,OAAJ,CAAYgJ,KAAZ,CAAP;;;AChBF;;;;;;;AAOA,AAAe,SAASE,aAAT,CAAuBzK,OAAvB,EAAgC;MACzC0K,WAAJ;MACI1K,QAAQM,QAAR,KAAqB,MAAzB,EAAiC;UACzB,EAAE8E,KAAF,EAASC,MAAT,KAAoBJ,gBAA1B;kBACc;WAAA;YAAA;YAGN,CAHM;WAIP;KAJP;GAFF,MAQO;kBACS;aACLjF,QAAQ6F,WADH;cAEJ7F,QAAQ+F,YAFJ;YAGN/F,QAAQ2K,UAHF;WAIP3K,QAAQ4K;KAJf;;;;SASK1F,cAAcwF,WAAd,CAAP;;;AC9BF;;;;;;;AAOA,AAAe,SAASG,aAAT,CAAuB7K,OAAvB,EAAgC;QACvCwE,SAASpE,iBAAiBJ,OAAjB,CAAf;QACM8K,IAAIlG,WAAWJ,OAAOoC,SAAlB,IAA+BhC,WAAWJ,OAAOuG,YAAlB,CAAzC;QACMC,IAAIpG,WAAWJ,OAAOqC,UAAlB,IAAgCjC,WAAWJ,OAAOyG,WAAlB,CAA1C;QACMzF,SAAS;WACNxF,QAAQ6F,WAAR,GAAsBmF,CADhB;YAELhL,QAAQ+F,YAAR,GAAuB+E;GAFjC;SAIOtF,MAAP;;;ACfF;;;;;;;AAOA,AAAe,SAAS0F,oBAAT,CAA8BhD,SAA9B,EAAyC;QAChDiD,OAAO,EAAE9G,MAAM,OAAR,EAAiBC,OAAO,MAAxB,EAAgCF,QAAQ,KAAxC,EAA+CD,KAAK,QAApD,EAAb;SACO+D,UAAUkD,OAAV,CAAkB,wBAAlB,EAA4CC,WAAWF,KAAKE,OAAL,CAAvD,CAAP;;;ACNF;;;;;;;;;;AAUA,AAAe,SAASC,gBAAT,CAA0B5D,MAA1B,EAAkC6D,gBAAlC,EAAoDrD,SAApD,EAA+D;cAChEA,UAAUc,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAZ;;;QAGMwC,aAAaX,cAAcnD,MAAd,CAAnB;;;QAGM+D,gBAAgB;WACbD,WAAWpG,KADE;YAEZoG,WAAWnG;GAFrB;;;QAMMqG,UAAU,CAAC,OAAD,EAAU,MAAV,EAAkBnK,OAAlB,CAA0B2G,SAA1B,MAAyC,CAAC,CAA1D;QACMyD,WAAWD,UAAU,KAAV,GAAkB,MAAnC;QACME,gBAAgBF,UAAU,MAAV,GAAmB,KAAzC;QACMG,cAAcH,UAAU,QAAV,GAAqB,OAAzC;QACMI,uBAAuB,CAACJ,OAAD,GAAW,QAAX,GAAsB,OAAnD;;gBAEcC,QAAd,IACEJ,iBAAiBI,QAAjB,IACAJ,iBAAiBM,WAAjB,IAAgC,CADhC,GAEAL,WAAWK,WAAX,IAA0B,CAH5B;MAII3D,cAAc0D,aAAlB,EAAiC;kBACjBA,aAAd,IACEL,iBAAiBK,aAAjB,IAAkCJ,WAAWM,oBAAX,CADpC;GADF,MAGO;kBACSF,aAAd,IACEL,iBAAiBL,qBAAqBU,aAArB,CAAjB,CADF;;;SAIKH,aAAP;;;ACxCF;;;;;;;;;;AAUA,AAAe,SAASM,mBAAT,CAA6BC,KAA7B,EAAoCtE,MAApC,EAA4CC,SAA5C,EAAuDxB,gBAAgB,IAAvE,EAA6E;QACpF8F,qBAAqB9F,gBAAgBmB,6BAA6BI,MAA7B,CAAhB,GAAuDlF,uBAAuBkF,MAAvB,EAA+BC,SAA/B,CAAlF;SACO3B,qCAAqC2B,SAArC,EAAgDsE,kBAAhD,EAAoE9F,aAApE,CAAP;;;AChBF;;;;;;;AAOA,AAAe,SAAS+F,wBAAT,CAAkCjM,QAAlC,EAA4C;QACnDkM,WAAW,CAAC,KAAD,EAAQ,IAAR,EAAc,QAAd,EAAwB,KAAxB,EAA+B,GAA/B,CAAjB;QACMC,YAAYnM,SAASoM,MAAT,CAAgB,CAAhB,EAAmBC,WAAnB,KAAmCrM,SAASsM,KAAT,CAAe,CAAf,CAArD;;OAEK,IAAInD,IAAI,CAAb,EAAgBA,IAAI+C,SAASrD,MAA7B,EAAqCM,GAArC,EAA0C;UAClCoD,SAASL,SAAS/C,CAAT,CAAf;UACMqD,UAAUD,SAAU,GAAEA,MAAO,GAAEJ,SAAU,EAA/B,GAAmCnM,QAAnD;QACI,OAAOS,SAASC,IAAT,CAAc+L,KAAd,CAAoBD,OAApB,CAAP,KAAwC,WAA5C,EAAyD;aAChDA,OAAP;;;SAGG,IAAP;;;AClBF;;;;;;;AAOA,AAAe,SAASE,UAAT,CAAoBC,eAApB,EAAqC;QAC5CC,UAAU,EAAhB;SAEED,mBACAC,QAAQ1L,QAAR,CAAiB2L,IAAjB,CAAsBF,eAAtB,MAA2C,mBAF7C;;;ACTF;;;;;;AAMA,AAAe,SAASG,iBAAT,CAA2BC,SAA3B,EAAsCC,YAAtC,EAAoD;SAC1DD,UAAUpL,IAAV,CACL,CAAC,EAAEsL,IAAF,EAAQC,OAAR,EAAD,KAAuBA,WAAWD,SAASD,YADtC,CAAP;;;ACLF;;;;;;;;;;AAUA,AAAe,SAASG,kBAAT,CACbJ,SADa,EAEbK,cAFa,EAGbC,aAHa,EAIb;QACMC,aAAazD,KAAKkD,SAAL,EAAgB,CAAC,EAAEE,IAAF,EAAD,KAAcA,SAASG,cAAvC,CAAnB;;QAEMG,aACJ,CAAC,CAACD,UAAF,IACAP,UAAUpL,IAAV,CAAesC,YAAY;WAEvBA,SAASgJ,IAAT,KAAkBI,aAAlB,IACApJ,SAASiJ,OADT,IAEAjJ,SAASvB,KAAT,GAAiB4K,WAAW5K,KAH9B;GADF,CAFF;;MAUI,CAAC6K,UAAL,EAAiB;UACTD,aAAc,KAAIF,cAAe,IAAvC;UACMI,YAAa,KAAIH,aAAc,IAArC;YACQI,IAAR,CACG,GAAED,SAAU,4BAA2BF,UAAW,4DAA2DA,UAAW,GAD3H;;SAIKC,UAAP;;;ACpCF;;;;;;;AAOA,AAAe,SAASG,SAAT,CAAmBC,CAAnB,EAAsB;SAC5BA,MAAM,EAAN,IAAY,CAACC,MAAMjJ,WAAWgJ,CAAX,CAAN,CAAb,IAAqCE,SAASF,CAAT,CAA5C;;;ACRF;;;;;AAKA,AAAe,SAASG,SAAT,CAAmB/N,OAAnB,EAA4B;QACnCY,gBAAgBZ,QAAQY,aAA9B;SACOA,gBAAgBA,cAAcoN,WAA9B,GAA4C/G,MAAnD;;;ACLF;;;;;;AAMA,AAAe,SAASgH,oBAAT,CAA8BtG,SAA9B,EAAyCqE,KAAzC,EAAgD;;YAEnDrE,SAAV,EAAqBuG,mBAArB,CAAyC,QAAzC,EAAmDlC,MAAMmC,WAAzD;;;QAGMC,aAAN,CAAoBC,OAApB,CAA4BC,UAAU;WAC7BJ,mBAAP,CAA2B,QAA3B,EAAqClC,MAAMmC,WAA3C;GADF;;;QAKMA,WAAN,GAAoB,IAApB;QACMC,aAAN,GAAsB,EAAtB;QACMG,aAAN,GAAsB,IAAtB;QACMC,aAAN,GAAsB,KAAtB;SACOxC,KAAP;;;AClBF;;;;;;;;;;AAUA,AAAe,SAASyC,YAAT,CAAsBzB,SAAtB,EAAiC0B,IAAjC,EAAuCC,IAAvC,EAA6C;QACpDC,iBAAiBD,SAASE,SAAT,GACnB7B,SADmB,GAEnBA,UAAUT,KAAV,CAAgB,CAAhB,EAAmBpC,UAAU6C,SAAV,EAAqB,MAArB,EAA6B2B,IAA7B,CAAnB,CAFJ;;iBAIeN,OAAf,CAAuBnK,YAAY;QAC7BA,SAAS,UAAT,CAAJ,EAA0B;;cAChBwJ,IAAR,CAAa,uDAAb;;UAEIpE,KAAKpF,SAAS,UAAT,KAAwBA,SAASoF,EAA5C,CAJiC;QAK7BpF,SAASiJ,OAAT,IAAoBR,WAAWrD,EAAX,CAAxB,EAAwC;;;;WAIjCnE,OAAL,CAAauC,MAAb,GAAsBxC,cAAcwJ,KAAKvJ,OAAL,CAAauC,MAA3B,CAAtB;WACKvC,OAAL,CAAawC,SAAb,GAAyBzC,cAAcwJ,KAAKvJ,OAAL,CAAawC,SAA3B,CAAzB;;aAEO2B,GAAGoF,IAAH,EAASxK,QAAT,CAAP;;GAZJ;;SAgBOwK,IAAP;;;ACnCF;;;;;;;;AAQA,AAAe,SAASI,aAAT,CAAuB9O,OAAvB,EAAgC+O,UAAhC,EAA4C;SAClDpN,IAAP,CAAYoN,UAAZ,EAAwBV,OAAxB,CAAgC,UAASjE,IAAT,EAAe;UACvCC,QAAQ0E,WAAW3E,IAAX,CAAd;QACIC,UAAU,KAAd,EAAqB;cACX2E,YAAR,CAAqB5E,IAArB,EAA2B2E,WAAW3E,IAAX,CAA3B;KADF,MAEO;cACG6E,eAAR,CAAwB7E,IAAxB;;GALJ;;;ACPF;;;;;;;;AAQA,AAAe,SAAS8E,SAAT,CAAmBlP,OAAnB,EAA4BwE,MAA5B,EAAoC;SAC1C7C,IAAP,CAAY6C,MAAZ,EAAoB6J,OAApB,CAA4BjE,QAAQ;QAC9B+E,OAAO,EAAX;;QAGE,CAAC,OAAD,EAAU,QAAV,EAAoB,KAApB,EAA2B,OAA3B,EAAoC,QAApC,EAA8C,MAA9C,EAAsD5N,OAAtD,CAA8D6I,IAA9D,MACE,CAAC,CADH,IAEAuD,UAAUnJ,OAAO4F,IAAP,CAAV,CAHF,EAIE;aACO,IAAP;;YAEMsC,KAAR,CAActC,IAAd,IAAsB5F,OAAO4F,IAAP,IAAe+E,IAArC;GAVF;;;ACRF,SAASC,qBAAT,CAA+B3I,YAA/B,EAA6C4I,KAA7C,EAAoDC,QAApD,EAA8DlB,aAA9D,EAA6E;QACrEmB,SAAS9I,aAAanG,QAAb,KAA0B,MAAzC;QACMgO,SAASiB,SAAS9I,aAAa7F,aAAb,CAA2BoN,WAApC,GAAkDvH,YAAjE;SACO+I,gBAAP,CAAwBH,KAAxB,EAA+BC,QAA/B,EAAyC,EAAEG,SAAS,IAAX,EAAzC;;MAEI,CAACF,MAAL,EAAa;0BAET9O,gBAAgB6N,OAAO/N,UAAvB,CADF,EAEE8O,KAFF,EAGEC,QAHF,EAIElB,aAJF;;gBAOYsB,IAAd,CAAmBpB,MAAnB;;;;;;;;;AASF,AAAe,SAASqB,mBAAT,CACbhI,SADa,EAEbiI,OAFa,EAGb5D,KAHa,EAIbmC,WAJa,EAKb;;QAEMA,WAAN,GAAoBA,WAApB;YACUxG,SAAV,EAAqB6H,gBAArB,CAAsC,QAAtC,EAAgDxD,MAAMmC,WAAtD,EAAmE,EAAEsB,SAAS,IAAX,EAAnE;;;QAGMlB,gBAAgB9N,gBAAgBkH,SAAhB,CAAtB;wBAEE4G,aADF,EAEE,QAFF,EAGEvC,MAAMmC,WAHR,EAIEnC,MAAMoC,aAJR;QAMMG,aAAN,GAAsBA,aAAtB;QACMC,aAAN,GAAsB,IAAtB;;SAEOxC,KAAP;;;ACiBF;;;;;;AAMA,YAAe;sBAAA;UAAA;WAAA;gBAAA;eAAA;uBAAA;eAAA;iBAAA;eAAA;sCAAA;eAAA;eAAA;kBAAA;qBAAA;WAAA;iBAAA;0BAAA;0BAAA;gBAAA;SAAA;YAAA;mBAAA;oBAAA;WAAA;sBAAA;cAAA;eAAA;WAAA;;CAAf;;;;;"}
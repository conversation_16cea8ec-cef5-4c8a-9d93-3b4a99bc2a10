/*
 Copyright (C) <PERSON> 2018
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */for(var e='undefined'!=typeof window&&'undefined'!=typeof document,t=['Edge','Trident','Firefox'],o=0,n=0;n<t.length;n+=1)if(e&&0<=navigator.userAgent.indexOf(t[n])){o=1;break}function i(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}function r(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},o))}}var p=e&&window.Promise,s=p?i:r;function d(e){return e&&'[object Function]'==={}.toString.call(e)}function a(e,t){if(1!==e.nodeType)return[];var o=getComputedStyle(e,null);return t?o[t]:o}function l(e){return'HTML'===e.nodeName?e:e.parentNode||e.host}function f(e){if(!e)return document.body;switch(e.nodeName){case'HTML':case'BODY':return e.ownerDocument.body;case'#document':return e.body;}var t=a(e),o=t.overflow,n=t.overflowX,i=t.overflowY;return /(auto|scroll|overlay)/.test(o+i+n)?e:f(l(e))}var m={},h=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:'all';return(e=e.toString(),m.hasOwnProperty(e))?m[e]:('11'===e?m[e]=-1!==navigator.userAgent.indexOf('Trident'):'10'===e?m[e]=-1!==navigator.appVersion.indexOf('MSIE 10'):'all'===e?m[e]=-1!==navigator.userAgent.indexOf('Trident')||-1!==navigator.userAgent.indexOf('MSIE'):void 0,m.all=m.all||Object.keys(m).some(function(e){return m[e]}),m[e])};function c(e){if(!e)return document.documentElement;for(var t=h(10)?document.body:null,o=e.offsetParent;o===t&&e.nextElementSibling;)o=(e=e.nextElementSibling).offsetParent;var n=o&&o.nodeName;return n&&'BODY'!==n&&'HTML'!==n?-1!==['TD','TABLE'].indexOf(o.nodeName)&&'static'===a(o,'position')?c(o):o:e?e.ownerDocument.documentElement:document.documentElement}function g(e){var t=e.nodeName;return'BODY'!==t&&('HTML'===t||c(e.firstElementChild)===e)}function u(e){return null===e.parentNode?e:u(e.parentNode)}function b(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return document.documentElement;var o=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,n=o?e:t,i=o?t:e,r=document.createRange();r.setStart(n,0),r.setEnd(i,0);var p=r.commonAncestorContainer;if(e!==p&&t!==p||n.contains(i))return g(p)?p:c(p);var s=u(e);return s.host?b(s.host,t):b(e,u(t).host)}function y(e){var t=1<arguments.length&&arguments[1]!==void 0?arguments[1]:'top',o='top'===t?'scrollTop':'scrollLeft',n=e.nodeName;if('BODY'===n||'HTML'===n){var i=e.ownerDocument.documentElement,r=e.ownerDocument.scrollingElement||i;return r[o]}return e[o]}function w(e,t){var o=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=y(t,'top'),i=y(t,'left'),r=o?-1:1;return e.top+=n*r,e.bottom+=n*r,e.left+=i*r,e.right+=i*r,e}function E(e,t){var o='x'===t?'Left':'Top',n='Left'==o?'Right':'Bottom';return parseFloat(e['border'+o+'Width'],10)+parseFloat(e['border'+n+'Width'],10)}function v(e,t,o,n){return Math.max(t['offset'+e],t['scroll'+e],o['client'+e],o['offset'+e],o['scroll'+e],h(10)?o['offset'+e]+n['margin'+('Height'===e?'Top':'Left')]+n['margin'+('Height'===e?'Bottom':'Right')]:0)}function x(){var e=document.body,t=document.documentElement,o=h(10)&&getComputedStyle(t);return{height:v('Height',e,t,o),width:v('Width',e,t,o)}}var O=function(e,t){if(!(e instanceof t))throw new TypeError('Cannot call a class as a function')},L=function(){function e(e,t){for(var o,n=0;n<t.length;n++)o=t[n],o.enumerable=o.enumerable||!1,o.configurable=!0,'value'in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}return function(t,o,n){return o&&e(t.prototype,o),n&&e(t,n),t}}(),S=function(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e},T=Object.assign||function(e){for(var t,o=1;o<arguments.length;o++)for(var n in t=arguments[o],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e};function D(e){return T({},e,{right:e.left+e.width,bottom:e.top+e.height})}function C(e){var t={};try{if(h(10)){t=e.getBoundingClientRect();var o=y(e,'top'),n=y(e,'left');t.top+=o,t.left+=n,t.bottom+=o,t.right+=n}else t=e.getBoundingClientRect()}catch(t){}var i={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},r='HTML'===e.nodeName?x():{},p=r.width||e.clientWidth||i.right-i.left,s=r.height||e.clientHeight||i.bottom-i.top,d=e.offsetWidth-p,l=e.offsetHeight-s;if(d||l){var f=a(e);d-=E(f,'x'),l-=E(f,'y'),i.width-=d,i.height-=l}return D(i)}function N(e,t){var o=Math.max,n=2<arguments.length&&void 0!==arguments[2]&&arguments[2],i=h(10),r='HTML'===t.nodeName,p=C(e),s=C(t),d=f(e),l=a(t),m=parseFloat(l.borderTopWidth,10),c=parseFloat(l.borderLeftWidth,10);n&&'HTML'===t.nodeName&&(s.top=o(s.top,0),s.left=o(s.left,0));var g=D({top:p.top-s.top-m,left:p.left-s.left-c,width:p.width,height:p.height});if(g.marginTop=0,g.marginLeft=0,!i&&r){var u=parseFloat(l.marginTop,10),b=parseFloat(l.marginLeft,10);g.top-=m-u,g.bottom-=m-u,g.left-=c-b,g.right-=c-b,g.marginTop=u,g.marginLeft=b}return(i&&!n?t.contains(d):t===d&&'BODY'!==d.nodeName)&&(g=w(g,t)),g}function k(e){var t=Math.max,o=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=e.ownerDocument.documentElement,i=N(e,n),r=t(n.clientWidth,window.innerWidth||0),p=t(n.clientHeight,window.innerHeight||0),s=o?0:y(n),d=o?0:y(n,'left'),a={top:s-i.top+i.marginTop,left:d-i.left+i.marginLeft,width:r,height:p};return D(a)}function P(e){var t=e.nodeName;return'BODY'===t||'HTML'===t?!1:!('fixed'!==a(e,'position'))||P(l(e))}function W(e){if(!e||!e.parentElement||h())return document.documentElement;for(var t=e.parentElement;t&&'none'===a(t,'transform');)t=t.parentElement;return t||document.documentElement}function B(e,t,o,n){var i=4<arguments.length&&void 0!==arguments[4]&&arguments[4],r={top:0,left:0},p=i?W(e):b(e,t);if('viewport'===n)r=k(p,i);else{var s;'scrollParent'===n?(s=f(l(t)),'BODY'===s.nodeName&&(s=e.ownerDocument.documentElement)):'window'===n?s=e.ownerDocument.documentElement:s=n;var d=N(s,p,i);if('HTML'===s.nodeName&&!P(p)){var a=x(),m=a.height,h=a.width;r.top+=d.top-d.marginTop,r.bottom=m+d.top,r.left+=d.left-d.marginLeft,r.right=h+d.left}else r=d}return r.left+=o,r.top+=o,r.right-=o,r.bottom-=o,r}function H(e){var t=e.width,o=e.height;return t*o}function A(e,t,o,n,i){var r=5<arguments.length&&arguments[5]!==void 0?arguments[5]:0;if(-1===e.indexOf('auto'))return e;var p=B(o,n,r,i),s={top:{width:p.width,height:t.top-p.top},right:{width:p.right-t.right,height:p.height},bottom:{width:p.width,height:p.bottom-t.bottom},left:{width:t.left-p.left,height:p.height}},d=Object.keys(s).map(function(e){return T({key:e},s[e],{area:H(s[e])})}).sort(function(e,t){return t.area-e.area}),a=d.filter(function(e){var t=e.width,n=e.height;return t>=o.clientWidth&&n>=o.clientHeight}),l=0<a.length?a[0].key:d[0].key,f=e.split('-')[1];return l+(f?'-'+f:'')}function I(e,t,o){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null,i=n?W(t):b(t,o);return N(o,i,n)}function M(e){var t=getComputedStyle(e),o=parseFloat(t.marginTop)+parseFloat(t.marginBottom),n=parseFloat(t.marginLeft)+parseFloat(t.marginRight),i={width:e.offsetWidth+n,height:e.offsetHeight+o};return i}function F(e){var t={left:'right',right:'left',bottom:'top',top:'bottom'};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function R(e,t,o){o=o.split('-')[0];var n=M(e),i={width:n.width,height:n.height},r=-1!==['right','left'].indexOf(o),p=r?'top':'left',s=r?'left':'top',d=r?'height':'width',a=r?'width':'height';return i[p]=t[p]+t[d]/2-n[d]/2,i[s]=o===s?t[s]-n[a]:t[F(s)],i}function U(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function Y(e,t,o){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===o});var n=U(e,function(e){return e[t]===o});return e.indexOf(n)}function j(e,t,o){var n=void 0===o?e:e.slice(0,Y(e,'name',o));return n.forEach(function(e){e['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');var o=e['function']||e.fn;e.enabled&&d(o)&&(t.offsets.popper=D(t.offsets.popper),t.offsets.reference=D(t.offsets.reference),t=o(t,e))}),t}function q(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=I(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=A(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=R(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?'fixed':'absolute',e=j(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function K(e,t){return e.some(function(e){var o=e.name,n=e.enabled;return n&&o===t})}function V(e){for(var t=[!1,'ms','Webkit','Moz','O'],o=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<t.length;n++){var i=t[n],r=i?''+i+o:e;if('undefined'!=typeof document.body.style[r])return r}return null}function z(){return this.state.isDestroyed=!0,K(this.modifiers,'applyStyle')&&(this.popper.removeAttribute('x-placement'),this.popper.style.position='',this.popper.style.top='',this.popper.style.left='',this.popper.style.right='',this.popper.style.bottom='',this.popper.style.willChange='',this.popper.style[V('transform')]=''),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function G(e){var t=e.ownerDocument;return t?t.defaultView:window}function _(e,t,o,n){var i='BODY'===e.nodeName,r=i?e.ownerDocument.defaultView:e;r.addEventListener(t,o,{passive:!0}),i||_(f(r.parentNode),t,o,n),n.push(r)}function X(e,t,o,n){o.updateBound=n,G(e).addEventListener('resize',o.updateBound,{passive:!0});var i=f(e);return _(i,'scroll',o.updateBound,o.scrollParents),o.scrollElement=i,o.eventsEnabled=!0,o}function J(){this.state.eventsEnabled||(this.state=X(this.reference,this.options,this.state,this.scheduleUpdate))}function Q(e,t){return G(e).removeEventListener('resize',t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener('scroll',t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function Z(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=Q(this.reference,this.state))}function $(e){return''!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function ee(e,t){Object.keys(t).forEach(function(o){var n='';-1!==['width','height','top','right','bottom','left'].indexOf(o)&&$(t[o])&&(n='px'),e.style[o]=t[o]+n})}function te(e,t){Object.keys(t).forEach(function(o){var n=t[o];!1===n?e.removeAttribute(o):e.setAttribute(o,t[o])})}function oe(e){return ee(e.instance.popper,e.styles),te(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&ee(e.arrowElement,e.arrowStyles),e}function ne(e,t,o,n,i){var r=I(i,t,e,o.positionFixed),p=A(o.placement,r,t,e,o.modifiers.flip.boundariesElement,o.modifiers.flip.padding);return t.setAttribute('x-placement',p),ee(t,{position:o.positionFixed?'fixed':'absolute'}),o}function ie(e,t){var o=Math.floor,n=t.x,i=t.y,r=e.offsets.popper,p=U(e.instance.modifiers,function(e){return'applyStyle'===e.name}).gpuAcceleration;void 0!==p&&console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');var s,d,a=void 0===p?t.gpuAcceleration:p,l=c(e.instance.popper),f=C(l),m={position:r.position},h={left:o(r.left),top:o(r.top),bottom:o(r.bottom),right:o(r.right)},g='bottom'===n?'top':'bottom',u='right'===i?'left':'right',b=V('transform');if(d='bottom'==g?-f.height+h.bottom:h.top,s='right'==u?-f.width+h.right:h.left,a&&b)m[b]='translate3d('+s+'px, '+d+'px, 0)',m[g]=0,m[u]=0,m.willChange='transform';else{var y='bottom'==g?-1:1,w='right'==u?-1:1;m[g]=d*y,m[u]=s*w,m.willChange=g+', '+u}var E={"x-placement":e.placement};return e.attributes=T({},E,e.attributes),e.styles=T({},m,e.styles),e.arrowStyles=T({},e.offsets.arrow,e.arrowStyles),e}function re(e,t,o){var n=U(e,function(e){var o=e.name;return o===t}),i=!!n&&e.some(function(e){return e.name===o&&e.enabled&&e.order<n.order});if(!i){var r='`'+t+'`';console.warn('`'+o+'`'+' modifier is required by '+r+' modifier in order to work, be sure to include it before '+r+'!')}return i}function pe(e,t){var o;if(!re(e.instance.modifiers,'arrow','keepTogether'))return e;var n=t.element;if('string'==typeof n){if(n=e.instance.popper.querySelector(n),!n)return e;}else if(!e.instance.popper.contains(n))return console.warn('WARNING: `arrow.element` must be child of its popper element!'),e;var i=e.placement.split('-')[0],r=e.offsets,p=r.popper,s=r.reference,d=-1!==['left','right'].indexOf(i),l=d?'height':'width',f=d?'Top':'Left',m=f.toLowerCase(),h=d?'left':'top',c=d?'bottom':'right',g=M(n)[l];s[c]-g<p[m]&&(e.offsets.popper[m]-=p[m]-(s[c]-g)),s[m]+g>p[c]&&(e.offsets.popper[m]+=s[m]+g-p[c]),e.offsets.popper=D(e.offsets.popper);var u=s[m]+s[l]/2-g/2,b=a(e.instance.popper),y=parseFloat(b['margin'+f],10),w=parseFloat(b['border'+f+'Width'],10),E=u-e.offsets.popper[m]-y-w;return E=Math.max(Math.min(p[l]-g,E),0),e.arrowElement=n,e.offsets.arrow=(o={},S(o,m,Math.round(E)),S(o,h,''),o),e}function se(e){if('end'===e)return'start';return'start'===e?'end':e}var de=['auto-start','auto','auto-end','top-start','top','top-end','right-start','right','right-end','bottom-end','bottom','bottom-start','left-end','left','left-start'],ae=de.slice(3);function le(e){var t=1<arguments.length&&arguments[1]!==void 0&&arguments[1],o=ae.indexOf(e),n=ae.slice(o+1).concat(ae.slice(0,o));return t?n.reverse():n}var fe={FLIP:'flip',CLOCKWISE:'clockwise',COUNTERCLOCKWISE:'counterclockwise'};function me(e,t){if(K(e.instance.modifiers,'inner'))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var o=B(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),n=e.placement.split('-')[0],i=F(n),r=e.placement.split('-')[1]||'',p=[];switch(t.behavior){case fe.FLIP:p=[n,i];break;case fe.CLOCKWISE:p=le(n);break;case fe.COUNTERCLOCKWISE:p=le(n,!0);break;default:p=t.behavior;}return p.forEach(function(s,d){if(n!==s||p.length===d+1)return e;n=e.placement.split('-')[0],i=F(n);var a=e.offsets.popper,l=e.offsets.reference,f=Math.floor,m='left'===n&&f(a.right)>f(l.left)||'right'===n&&f(a.left)<f(l.right)||'top'===n&&f(a.bottom)>f(l.top)||'bottom'===n&&f(a.top)<f(l.bottom),h=f(a.left)<f(o.left),c=f(a.right)>f(o.right),g=f(a.top)<f(o.top),u=f(a.bottom)>f(o.bottom),b='left'===n&&h||'right'===n&&c||'top'===n&&g||'bottom'===n&&u,y=-1!==['top','bottom'].indexOf(n),w=!!t.flipVariations&&(y&&'start'===r&&h||y&&'end'===r&&c||!y&&'start'===r&&g||!y&&'end'===r&&u);(m||b||w)&&(e.flipped=!0,(m||b)&&(n=p[d+1]),w&&(r=se(r)),e.placement=n+(r?'-'+r:''),e.offsets.popper=T({},e.offsets.popper,R(e.instance.popper,e.offsets.reference,e.placement)),e=j(e.instance.modifiers,e,'flip'))}),e}function he(e){var t=e.offsets,o=t.popper,n=t.reference,i=e.placement.split('-')[0],r=Math.floor,p=-1!==['top','bottom'].indexOf(i),s=p?'right':'bottom',d=p?'left':'top',a=p?'width':'height';return o[s]<r(n[d])&&(e.offsets.popper[d]=r(n[d])-o[a]),o[d]>r(n[s])&&(e.offsets.popper[d]=r(n[s])),e}function ce(e,t,o,n){var i=Math.max,r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),p=+r[1],s=r[2];if(!p)return e;if(0===s.indexOf('%')){var d;switch(s){case'%p':d=o;break;case'%':case'%r':default:d=n;}var a=D(d);return a[t]/100*p}if('vh'===s||'vw'===s){var l;return l='vh'===s?i(document.documentElement.clientHeight,window.innerHeight||0):i(document.documentElement.clientWidth,window.innerWidth||0),l/100*p}return p}function ge(e,t,o,n){var i=[0,0],r=-1!==['right','left'].indexOf(n),p=e.split(/(\+|\-)/).map(function(e){return e.trim()}),s=p.indexOf(U(p,function(e){return-1!==e.search(/,|\s/)}));p[s]&&-1===p[s].indexOf(',')&&console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');var d=/\s*,\s*|\s+/,a=-1===s?[p]:[p.slice(0,s).concat([p[s].split(d)[0]]),[p[s].split(d)[1]].concat(p.slice(s+1))];return a=a.map(function(e,n){var i=(1===n?!r:r)?'height':'width',p=!1;return e.reduce(function(e,t){return''===e[e.length-1]&&-1!==['+','-'].indexOf(t)?(e[e.length-1]=t,p=!0,e):p?(e[e.length-1]+=t,p=!1,e):e.concat(t)},[]).map(function(e){return ce(e,i,t,o)})}),a.forEach(function(e,t){e.forEach(function(o,n){$(o)&&(i[t]+=o*('-'===e[n-1]?-1:1))})}),i}function ue(e,t){var o,n=t.offset,i=e.placement,r=e.offsets,p=r.popper,s=r.reference,d=i.split('-')[0];return o=$(+n)?[+n,0]:ge(n,p,s,d),'left'===d?(p.top+=o[0],p.left-=o[1]):'right'===d?(p.top+=o[0],p.left+=o[1]):'top'===d?(p.left+=o[0],p.top-=o[1]):'bottom'===d&&(p.left+=o[0],p.top+=o[1]),e.popper=p,e}function be(e,t){var o=t.boundariesElement||c(e.instance.popper);e.instance.reference===o&&(o=c(o));var n=B(e.instance.popper,e.instance.reference,t.padding,o,e.positionFixed);t.boundaries=n;var i=t.priority,r=e.offsets.popper,p={primary:function(e){var o=r[e];return r[e]<n[e]&&!t.escapeWithReference&&(o=Math.max(r[e],n[e])),S({},e,o)},secondary:function(e){var o='right'===e?'left':'top',i=r[o];return r[e]>n[e]&&!t.escapeWithReference&&(i=Math.min(r[o],n[e]-('right'===e?r.width:r.height))),S({},o,i)}};return i.forEach(function(e){var t=-1===['left','top'].indexOf(e)?'secondary':'primary';r=T({},r,p[t](e))}),e.offsets.popper=r,e}function ye(e){var t=e.placement,o=t.split('-')[0],n=t.split('-')[1];if(n){var i=e.offsets,r=i.reference,p=i.popper,s=-1!==['bottom','top'].indexOf(o),d=s?'left':'top',a=s?'width':'height',l={start:S({},d,r[d]),end:S({},d,r[d]+r[a]-p[a])};e.offsets.popper=T({},p,l[n])}return e}function we(e){if(!re(e.instance.modifiers,'hide','preventOverflow'))return e;var t=e.offsets.reference,o=U(e.instance.modifiers,function(e){return'preventOverflow'===e.name}).boundaries;if(t.bottom<o.top||t.left>o.right||t.top>o.bottom||t.right<o.left){if(!0===e.hide)return e;e.hide=!0,e.attributes['x-out-of-boundaries']=''}else{if(!1===e.hide)return e;e.hide=!1,e.attributes['x-out-of-boundaries']=!1}return e}function Ee(e){var t=e.placement,o=t.split('-')[0],n=e.offsets,i=n.popper,r=n.reference,p=-1!==['left','right'].indexOf(o),s=-1===['top','left'].indexOf(o);return i[p?'left':'top']=r[o]-(s?i[p?'width':'height']:0),e.placement=F(t),e.offsets.popper=D(i),e}var ve={shift:{order:100,enabled:!0,fn:ye},offset:{order:200,enabled:!0,fn:ue,offset:0},preventOverflow:{order:300,enabled:!0,fn:be,priority:['left','right','top','bottom'],padding:5,boundariesElement:'scrollParent'},keepTogether:{order:400,enabled:!0,fn:he},arrow:{order:500,enabled:!0,fn:pe,element:'[x-arrow]'},flip:{order:600,enabled:!0,fn:me,behavior:'flip',padding:5,boundariesElement:'viewport'},inner:{order:700,enabled:!1,fn:Ee},hide:{order:800,enabled:!0,fn:we},computeStyle:{order:850,enabled:!0,fn:ie,gpuAcceleration:!0,x:'bottom',y:'right'},applyStyle:{order:900,enabled:!0,fn:oe,onLoad:ne,gpuAcceleration:void 0}},xe={placement:'bottom',positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:ve},Oe=function(){function e(t,o){var n=this,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};O(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=s(this.update.bind(this)),this.options=T({},e.Defaults,i),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=o&&o.jquery?o[0]:o,this.options.modifiers={},Object.keys(T({},e.Defaults.modifiers,i.modifiers)).forEach(function(t){n.options.modifiers[t]=T({},e.Defaults.modifiers[t]||{},i.modifiers?i.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return T({name:e},n.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&d(e.onLoad)&&e.onLoad(n.reference,n.popper,n.options,e,n.state)}),this.update();var r=this.options.eventsEnabled;r&&this.enableEventListeners(),this.state.eventsEnabled=r}return L(e,[{key:'update',value:function(){return q.call(this)}},{key:'destroy',value:function(){return z.call(this)}},{key:'enableEventListeners',value:function(){return J.call(this)}},{key:'disableEventListeners',value:function(){return Z.call(this)}}]),e}();Oe.Utils=('undefined'==typeof window?global:window).PopperUtils,Oe.placements=de,Oe.Defaults=xe;export default Oe;
//# sourceMappingURL=popper.min.js.map

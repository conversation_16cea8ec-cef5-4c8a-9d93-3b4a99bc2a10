﻿using INAIPI.Core;
using SI_INAIPI.Controls.CxP;
using SI_INAIPI.Models.CxP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace GestionExpedientes
{
    public partial class Consultar : System.Web.UI.Page
    {
        CxP_PagosExpedientesCtrl ctrl = new CxP_PagosExpedientesCtrl();
        private CxP_PagosExpedientes PagoExpediente
        {
            get { return (CxP_PagosExpedientes)Session["Default_PagoExpediente"]; }
            set { Session["Default_PagoExpediente"] = value; }
        }
        private CxP_PagosExpedientesTiposDocumentos TipoDocumentoSeleccionado
        {
            get { return (CxP_PagosExpedientesTiposDocumentos)Session["Default_PagoExpediente"]; }
            set { Session["Default_PagoExpediente"] = value; }
        }
        private List<CxP_PagosExpedientes> PagoExpedientes
        {
            get { return (List<CxP_PagosExpedientes>)Session["Default_PagoExpedientes"]; }
            set { Session["Default_PagoExpedientes"] = value; }
        }
        private List<CxP_PagosExpedientesTiposDocumentos> ListTiposDocumentos
        {
            get { return (List<CxP_PagosExpedientesTiposDocumentos>)Session["Default_ListTiposDocumentos"]; }
            set { Session["Default_ListTiposDocumentos"] = value; }
        }
        private List<CxP_PagosExpedientes> ListExpedientesPorTipoDocumento
        {
            get { return (List<CxP_PagosExpedientes>)Session["Default_PagoExpedientes_PorTipo"]; }
            set { Session["Default_PagoExpedientes_PorTipo"] = value; }
        }
        private string SeccionActiva
        {
            get { return (string)Session["Default_SeccionActiva"]; }
            set { Session["Default_SeccionActiva"] = value; }
        }
        private bool Editar
        {
            get { return (bool)Session["Default_Editar"]; }
            set { Session["Default_Editar"] = value; }
        }

        public void Totales(int remitente)
        {
            btnRegistros.Badge.Text = ctrl.TotalRegistrados(remitente).ToString();
            btnRecibidos.Badge.Text = ctrl.TotalRecibidos(remitente).ToString();
            btnEnviados.Badge.Text = ctrl.TotalEnviados(remitente).ToString();
        }
        public void Redireccionar(string page, string parametro)
        {
            if (!string.IsNullOrEmpty(parametro))
            {
                PagoExpediente = PagoExpedientes.FirstOrDefault(x => x.IdExpediente == parametro.ToInt());
            }


            string strPathAndQuery = HttpContext.Current.Request.Url.PathAndQuery;
            string strUrl = HttpContext.Current.Request.Url.AbsoluteUri.Replace(strPathAndQuery, "/");

            string[] split = strPathAndQuery.Split('?');

            DevExpress.Web.ASPxWebControl.RedirectOnCallback(strUrl + page + ".aspx?" + split[1]);

        }

        protected void Page_Init(object sender, EventArgs e)
        {
            grdTiposDocumentos.DataSource = ListTiposDocumentos;
            grdTiposDocumentos.DataBind();

            grdConsultarExpedientes.DataSource = ListExpedientesPorTipoDocumento;
            grdConsultarExpedientes.DataBind();
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsCallback)
            {
                string UsuarioRemitente = ctrl.Decode(Request.QueryString[ctrl.Encode("UsuarioRemitente")] ?? ""); //Request.QueryString["UsuarioRemitente"];
                string DepartamentoRemitente = ctrl.Decode(Request.QueryString[ctrl.Encode("DepartamentoRemitente")] ?? ""); //Request.QueryString["DepartamentoRemitente"];
                string Rol = ctrl.Decode(Request.QueryString[ctrl.Encode("Rol")] ?? ""); //Request.QueryString["Rol"];
                string btnSeccionado = ctrl.Decode(Request.QueryString[ctrl.Encode("btnSeccionado")] ?? ""); //Request.QueryString["Rol"];

                if (string.IsNullOrEmpty(UsuarioRemitente) | string.IsNullOrEmpty(DepartamentoRemitente) | string.IsNullOrEmpty(Rol))
                {
                    Response.Redirect(System.Configuration.ConfigurationManager.AppSettings["MenuPrincipalUrl"]);
                }

                Session["UsuarioRemitente"] = UsuarioRemitente; //"2023";
                Session["DepartamentoRemitente"] = DepartamentoRemitente; // "22"; //22, 30, 23, 25
                Session["Rol"] = Rol; //"Administrador";               


                //PreviewDocumento.Visible = false;

                Usuario userLogin = new Usuario();
                userLogin = ctrl.Usuario(UsuarioRemitente.ToString().ToInt());

                LoginUser.InnerText = userLogin.NombreCompleto;
                LoginDepartamento.InnerText = userLogin.Departamento;
                //ListadoExpedientesEnviados(true);
                Totales(DepartamentoRemitente.ToString().ToInt());
                SeleccionTiposDocumentos();
              
            }
        }
        public void SeleccionTiposDocumentos()
        {
            ListTiposDocumentos = ctrl.ListExpedientesTiposDocumentos();
            grdTiposDocumentos.DataSource = ListTiposDocumentos;
            grdTiposDocumentos.DataBind();
        }
        public void ConsultarPorTipoDocumentos(int IdTipoDocumento)
        {
            if (IdTipoDocumento == 1)
            {
                grdConsultarExpedientes.Columns["gcBalance"].Visible = true;

                grdConsultarExpedientes.Columns["gcBeneficiario"].Visible = true;
                grdConsultarExpedientes.Columns["gcRNC"].Visible = true;
                grdConsultarExpedientes.Columns["gcNCF"].Visible = true;
                grdConsultarExpedientes.Columns["IdCxP"].Visible = true;
            }

            if (IdTipoDocumento == 2)
            {
                //labelDocumentoSeleccionado = "Requisición de Compras";
                grdConsultarExpedientes.Columns["gcBalance"].Visible = false;
                grdConsultarExpedientes.Columns["gcBeneficiario"].Visible = false;
                grdConsultarExpedientes.Columns["gcRNC"].Visible = false;
                grdConsultarExpedientes.Columns["gcNCF"].Visible = false;
                grdConsultarExpedientes.Columns["IdCxP"].Visible = false;
            }

            PagoExpedientes = ctrl.ListExpedientesPorTipoDocumento(IdTipoDocumento);
            grdConsultarExpedientes.DataSource = PagoExpedientes;
            grdConsultarExpedientes.DataBind();
        }
        protected void cbPrincipal_Callback(object sender, DevExpress.Web.CallbackEventArgsBase e)
        {
            string[] parametro = e.Parameter.Split('|');

            switch (parametro[0])
            {
                case "ABRIR-EXPEDIENTE":
                    //PagoExpediente = null;
                    Editar = false;
                    SeccionActiva = "CONSULTAR";
                    Redireccionar("Formulario", parametro[1]);
                    break;

                case "NUEVO":
                    SeccionActiva = "REGISTRADOS";
                    PagoExpediente = null;
                    Redireccionar("Formulario", parametro[1]);
                    break;

                case "EXPEDIENTES-REGISTRADOS":
                    SeccionActiva = "REGISTRADOS";
                    Redireccionar("Registrados", parametro[1]);
                    break;

                case "EXPEDIENTES-RECIBIDOS":
                    Redireccionar("Recibidos", parametro[1]);
                    break;
                case "EXPEDIENTES-ENVIADOS":
                    SeccionActiva = "ENVIADOS";
                    Redireccionar("Enviados", parametro[1]);
                    break;

                case "CONSULTAR-EXPEDIENTES":
                    SeccionActiva = "CONSULTAR";
                    Redireccionar("Consultar", parametro[1]);
                    break;

                case "ABRIR-EXPEDIENTE-POR-TIPO":
                    SeccionActiva = "CONSULTAR";
                    TipoDocumentoSeleccionado = ListTiposDocumentos.FirstOrDefault(x => x.IdTipoDocumento == parametro[1].ToString().ToInt());
                    TituloDocumentoSeleccionado.InnerHtml = TipoDocumentoSeleccionado.TipoDocumento;
                    ConsultarPorTipoDocumentos(parametro[1].ToInt());
                    break;


            }
        }
    }
}
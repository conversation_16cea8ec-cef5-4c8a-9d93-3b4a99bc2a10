﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterGestionExpedientes.Master" AutoEventWireup="true" CodeBehind="Formulario.aspx.cs" Inherits="GestionExpedientes.Formulario" %>

<%@ Register Assembly="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        #ContentPlaceHolder1_cbPrincipal_btnAplicarLibramiento, #ContentPlaceHolder1_cbPrincipal_btnAplicarOrdenamiento {
            background-color: #77bc1f;
            border: 1px solid #5B9218;
        }

        #ContentPlaceHolder1_cbPrincipal_vista2 {
            padding: 0;
        }

        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_TC {
            margin: -10px;
        }

        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_CC {
            padding: 10px;
        }

        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_cbTipoDocumento_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_txtProcedencia_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_txtBeneficiario_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_txtTipoPago_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_txtEntregadoPor_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_FechaEmision_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_FechaEntrega_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_selectEstatus_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_selectDestinatario_I {
            height: 33px;
            border-radius: 0;
        }

        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_ValorFactura_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_cbTotalCxP_Monto_I,
        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_Impuesto_I {
            border-radius: 0;
            width: 90%;
        }

        #ContentPlaceHolder1_cbPrincipal_TabsExpediente_Observacion {
            height: 93px;
        }

        .dxbs-edit-btn {
            height: 33px;
            background: linear-gradient(#fff, #ccc);
        }

        .nav-tabs a {
            color: #000;
            font-weight: 600;
        }

        .alto {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
        }
        /*.input-group-addon{
            background:linear-gradient(#fff, #ccc);
        }*/
    </style>
    <script>
        function OncustomButtonClick(s, e) {
            e.processOnServer = false;
            switch (e.buttonID) {
                case 'btnEliminar':
                    var r = confirm("Quiere eliminar el archivo!");
                    if (r == true) {
                        cbDocumentos.PerformCallback('ELIMINAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex));
                    }
                    break;

                case 'btnVisualizar':
                    doProcessClick = false;
                    $("#PreviewDocumento").show();
                    $("#PreviewDocumentoBg").show();
                    cbPreviewDocumento.PerformCallback('VISUALIZAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex));
                    break;

                case 'btnDescargar':
                    //doProcessClick = false;
                    //cbDocumentos.PerformCallback('DESCARGAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex));
                    e.processOnServer = true;
                    break;
            }
        }
        function onUploadControlFileUploadComplete(s, e) {
            e.processOnServer = false;
            if (e.isValid) {
                cbDocumentos.PerformCallback('SUBIR_ARCHIVO');
            } else {
                //setElementVisible("uploadedImage", e.isValid);
            }
        }
        function SeleccionarTipoDocumento() {

            if (cbTipoDocumento.GetValue() === '2') {
                txtBeneficiario.SetEnabled(false);
                txtTipoPago.SetEnabled(false);
                NCF.SetEnabled(false);
                BeneficiarioCorreo.SetEnabled(false);
                ValorFactura.SetEnabled(false);
                Impuesto.SetEnabled(false);
                Monto.SetEnabled(false);

            } else {
                txtBeneficiario.SetEnabled(true);
                txtTipoPago.SetEnabled(true);
                NCF.SetEnabled(true);
                BeneficiarioCorreo.SetEnabled(true);
                ValorFactura.SetEnabled(true);
                Impuesto.SetEnabled(true);
                Monto.SetEnabled(true);
            }
            if (cbTipoDocumento.GetValue() === '1') {
                $("#divNumeroRequerimiento").show();
            } else {
                $("#divNumeroRequerimiento").hide();
            }

        }
        function ExecuteSelectors() {

            $("#ContentPlaceHolder1_cbPrincipal_HoraEntrega_I").keyup(function () {

                if ($(this).val().length === 2) {
                    $(this).val($(this).val() + ':');
                }
                if ($(this).val().length === 5) {
                    $(this).val($(this).val() + ':00');
                }
            });
            $("#btnCerrarVentanaPreviewDocumento").click(function () {
                $("#PreviewDocumento").hide();
                $("#PreviewDocumentoBg").hide();
            });

        }
        function CalcularMonto() {
            var valorFactura = Number($("#ContentPlaceHolder1_cbPrincipal_ValorFactura_I").val());
            var impuesto = Number($("#ContentPlaceHolder1_cbPrincipal_Impuesto_I").val());

            console.log(valorFactura + impuesto);
            $("#ContentPlaceHolder1_cbPrincipal_Monto_I").val(valorFactura + impuesto);
        }
        function FormatearHora(s, e) {

            var conteo = $("#ContentPlaceHolder1_cbPrincipal_TabsExpediente_HoraEntrega_I").val().length;

            if (conteo == 2) {
                HoraEntrega.SetValue(HoraEntrega.GetValue() + ":");
            }
            if (conteo == 5) {
                HoraEntrega.SetValue(HoraEntrega.GetValue() + ":");
            }
        }
        function OnInit(s, e) {
            ExecuteSelectors();
            SeleccionarTipoDocumento();
        }
        function OnEndCallback(s, e) {
            ExecuteSelectors();
            SeleccionarTipoDocumento();
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <dx:ASPxCallbackPanel ID="cbPrincipal" runat="server" ClientInstanceName="cbPrincipal" OnCallback="cbPrincipal_Callback">
        <ClientSideEvents Init="OnInit" EndCallback="OnEndCallback" />
        <PanelCollection>
            <dx:PanelContent>
                <div id="PreviewDocumento" style="position: absolute; left: 100px; right: 100px; bottom: 100px; top: 100px; background-color: #fff; z-index: 1000000; border: 1px solid #000; overflow: hidden; display: none;">
                    <div style="position: absolute; left: 0; right: 0; height: 25px; top: 0; background-color: #0070cd">
                        <div style="position: absolute; right: 5px; color: #fff;">
                            <button type="button" style="background: none; border: none" id="btnCerrarVentanaPreviewDocumento">X</button>
                        </div>
                        

                    </div>
                    <div style="position: absolute; left: 0; right: 0; bottom: 0; top: 25px;">
                        <dx:ASPxCallbackPanel CssClass="alto" ID="cbPreviewDocumento" runat="server" ClientInstanceName="cbPreviewDocumento" OnCallback="cbPreviewDocumento_Callback">
                            <PanelCollection>
                                <dx:PanelContent>
                                    <div visible="false" id="IframeMensaje" runat="server" style="position: absolute; left: 0; right: 0; top: 25px; bottom: 0">
                                        <h1 style="margin-left: 50px; margin-right: 50px;">El archivo no puede visualizarse</h1>
                                        <p style="margin-left: 50px; margin-right: 50px;">El archivo está siendo descargado en su computadora. Puede visualizarlo en la parte inferior de su navegador.</p>
                                    </div>
                                    <iframe id="Iframe" runat="server" style="width: 100%; height: 100%; border: none"></iframe>
                                </dx:PanelContent>
                            </PanelCollection>
                        </dx:ASPxCallbackPanel>

                    </div>
                </div>
                <div id="PreviewDocumentoBg" style="position: absolute; left: 0; right: 0; bottom: 0; top: 0; background-color: rgba(0, 0, 0, 0.6); z-index: 999; display: none"></div>

                <div id="output"></div>
                <div id="contenedor-principal">
                    <div id="navegacion"></div>
                    <div id="header-principal">
                        <div class="logo"></div>
                        <div class="header-principal-titulo">
                            <h1>SISTEMA DE INFORMACION</h1>
                            <h2>GESTIÓN DE PAGOS Y MANEJO DE CORRESPONDENCIAS</h2>
                        </div>
                        <div id="area-user">
                            <div id="area-user-info">
                                <div id="imgUsuario"></div>
                                <dx:BootstrapBinaryImage ID="ImgUsuario" runat="server"></dx:BootstrapBinaryImage>
                                <div id="InfoDatosUsuario">
                                    <h3 style="margin-top: -20px" runat="server" id="LoginUser"></h3>
                                    <h4 runat="server" id="LoginDepartamento"></h4>
                                </div>
                            </div>
                            <a href="http://sigepi.inaipi.gob.do/" runat="server" style="text-align: center; margin-right: 10px; display: block; padding: 5px; text-transform: uppercase; float: left; text-decoration: none"><span style="font-size: 35px;" class="glyphicon glyphicon-th"></span>
                                <br />
                                <span style="font-family: Arial; font-size: 10px; font-weight: bold">Menu Principal</span></a>
                            <div style="display: block; float: right; width: 97px; height: 62px; text-align: center; padding-top: 9px">
                                <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-search" ID="BootstrapButton1" runat="server" UseSubmitBehavior="false" AutoPostBack="false">
                                    <SettingsBootstrap RenderOption="Default" />
                                    <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('CONSULTAR-EXPEDIENTES|');}" />
                                </dx:BootstrapButton>
                                <br />
                                <span style="font-family: Arial; font-size: 10px; font-weight: bold; text-transform: uppercase; color: #337AB7">Consultar</span>
                            </div>
                        </div>
                    </div>
                    <div id="header-modulo">
                        <span class="glyphicon glyphicon-th-list"></span><span runat="server" id="labelTituloModulo">Capturar Expedientes</span>
                        <div class="header-modulo-tool">
                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-plus" ID="btnNuevo" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text=" Nuevo" Visible="false">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('NUEVO|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-check" ID="btnSalvar" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text=" Salvar" Visible="false">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('SALVAR|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton Visible="false" CssClasses-Icon="glyphicon glyphicon-check" ID="btnAplicarLibramiento" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Aplicar Libramiento">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('APLICAR_LIBRAMIENTO|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton Visible="false" CssClasses-Icon="glyphicon glyphicon-check" ID="btnAplicarOrdenamiento" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Aplicar Ordenamiento">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('APLICAR_ORDENAMIENTO|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-check" ID="btnActualizar" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Actualizar">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('ACTUALIZAR|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnRegistros" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Registrados">
                                <Badge Text="" IconCssClass="far fa-thumbs-up" />
                                <SettingsBootstrap RenderOption="Default" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-REGISTRADOS|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnRecibidos" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Recibidos">
                                <Badge Text="" IconCssClass="far fa-thumbs-up" />
                                <SettingsBootstrap RenderOption="Default" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-RECIBIDOS|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnEnviados" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Enviados">
                                <Badge Text="" IconCssClass="far fa-thumbs-up" />
                                <SettingsBootstrap RenderOption="Default" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-ENVIADOS|');}" />
                            </dx:BootstrapButton>
                        </div>
                    </div>
                    <div id="contenido">
                        <div class="vista-modulo" id="vista1" runat="server">

                            <div class="vista-modulo ocultar-vista" id="vista2" runat="server" style="background-color: #fff; left: 5px; right: 5px; top: 5px; bottom: 5px; border: 1px solid #ccc; padding: 0">
                                <div class="content-formulario" style="top: 0">
                                    <div class="tab-content">
                                        <dx:BootstrapPageControl runat="server" TabAlign="Left" ID="TabsExpediente">
                                            <TabPages>
                                                <dx:BootstrapTabPage Text="EXPEDIENTE" Name="TabExpediente">
                                                    <ContentCollection>
                                                        <dx:ContentControl runat="server" SupportsDisabledAttribute="True">
                                                            <div style="margin-top: 10px">
                                                                <div class="row">
                                                                    <div class="col-sm-4">
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <strong>Datos del documento</strong>
                                                                            </div>
                                                                            <div class="panel-body">
                                                                                <div class="row">

                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group" style="margin-bottom: 10px; width: 100%">
                                                                                            <span class="input-group-addon">Tipo de Documento</span>
                                                                                            <dx:BootstrapComboBox ReadOnly="false" ClientInstanceName="cbTipoDocumento" Width="100%" AutoPostBack="false" ID="cbTipoDocumento" runat="server" TextField="TipoDocumento" ValueField="IdTipoDocumento">
                                                                                                <ClientSideEvents ValueChanged="function(){ SeleccionarTipoDocumento(); }" />
                                                                                            </dx:BootstrapComboBox>

                                                                                        </div>
                                                                                    </div>

                                                                                </div>
                                                                                <div class="row" style="margin-bottom: 10px; display: none" id="divNumeroRequerimiento">
                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">No. de Requisición</span>
                                                                                            <dx:BootstrapTextBox ClientInstanceName="txtNumeroRequisicion" MaxLength="50" ID="txtNumeroRequisicion" runat="server"></dx:BootstrapTextBox>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <div class="row" style="margin-bottom: 10px">

                                                                                    <div class="col-sm-6" style="padding-right: 30px">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">Emision</span>
                                                                                            <dx:BootstrapDateEdit Width="135" ID="FechaEmision" runat="server"></dx:BootstrapDateEdit>
                                                                                        </div>
                                                                                    </div>

                                                                                    <div class="col-sm-6">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">No.</span>
                                                                                            <dx:BootstrapTextBox MaxLength="50" ID="txtNumeroDocumento" runat="server"></dx:BootstrapTextBox>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                                <div class="row" style="margin-bottom: 10px">
                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">Asunto</span>

                                                                                            <dx:BootstrapMemo ID="Asunto" runat="server" MaxLength="256"></dx:BootstrapMemo>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>
                                                                                <div class="row" style="margin-bottom: 10px">
                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">NCF</span>

                                                                                            <dx:BootstrapTextBox ClientInstanceName="NCF" MaxLength="50" ID="NCF" runat="server"></dx:BootstrapTextBox>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>
                                                                                <div class="row" style="margin-bottom: 10px">
                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">Descripción</span>
                                                                                            <textarea runat="server" maxlength="256" class="form-control Descripcion" id="Descripcion" name="Descripcion" placeholder="Descripción"></textarea>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                                <div class="row" style="margin-bottom: 10px;">
                                                                                    <div class="col-sm-12">

                                                                                        <div class="input-group" style="width: 100%">
                                                                                            <span class="input-group-addon" style="z-index: 10">Procedencia</span>
                                                                                            <dx:BootstrapComboBox ClientInstanceName="txtProcedencia" CssClasses-Control="IdProcedencia" Width="100%" AutoPostBack="false" CssClasses-IconDropDownButton="glyphicon glyphicon-search" ID="txtProcedencia" runat="server" TextField="Procedencia" ValueField="IdProcedencia">
                                                                                            </dx:BootstrapComboBox>

                                                                                        </div>

                                                                                    </div>

                                                                                </div>
                                                                                <div class="row" style="margin-bottom: 10px">
                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group" style="width: 100%">
                                                                                            <span class="input-group-addon">Entregado por</span>
                                                                                            <dx:BootstrapComboBox CallbackPageSize="15" Width="100%" AutoPostBack="false" CssClasses-Control="EntregadoPor" CssClasses-IconDropDownButton="glyphicon glyphicon-search" ID="txtEntregadoPor" runat="server" TextField="EntregadoPor" ValueField="IdEntregadoPor">
                                                                                            </dx:BootstrapComboBox>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>
                                                                                <div class="row" style="margin-bottom: 10px">

                                                                                    <div class="col-sm-6">
                                                                                        <div class="input-group" style="z-index: 10">
                                                                                            <span class="input-group-addon">Fecha entrega</span>
                                                                                            <dx:BootstrapDateEdit Width="120" runat="server" EditFormat="Date" ID="FechaEntrega">
                                                                                            </dx:BootstrapDateEdit>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="col-sm-6">
                                                                                        <div class="input-group" style="padding-left: 30px;">
                                                                                            <span class="input-group-addon">Hora</span>
                                                                                            <dx:BootstrapTextBox ClientInstanceName="HoraEntrega" ClientSideEvents-KeyPress="FormatearHora" MaxLength="8" Width="110" ID="HoraEntrega" runat="server"></dx:BootstrapTextBox>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-5">
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <strong>Datos del Beneficiario</strong>
                                                                            </div>
                                                                            <div class="panel-body">

                                                                                <div class="row" style="margin-bottom: 10px">
                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group" style="width: 100%; position: relative">
                                                                                            <span class="input-group-addon">Suplidor</span>
                                                                                            <dx:BootstrapComboBox ClientEnabled="false" ClientInstanceName="txtBeneficiario" CallbackPageSize="15" Width="100%" AutoPostBack="false" CssClasses-IconDropDownButton="glyphicon glyphicon-search" ID="txtBeneficiario" runat="server" TextField="Beneficiario" ValueField="IdBeneficiario">
                                                                                            </dx:BootstrapComboBox>


                                                                                        </div>
                                                                                    </div>


                                                                                </div>


                                                                                <div class="row" style="margin-bottom: 10px">

                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">Correo</span>
                                                                                            <dx:BootstrapTextBox MaxLength="50" ClientInstanceName="BeneficiarioCorreo" ID="BeneficiarioCorreo" runat="server"></dx:BootstrapTextBox>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>


                                                                                <div class="row" style="margin-bottom: 10px">

                                                                                    <div class="col-sm-12">
                                                                                        <div class="input-group" style="width: 100%">

                                                                                            <span class="input-group-addon">Tipo de Pago</span>
                                                                                            <dx:BootstrapComboBox ClientEnabled="false" ClientInstanceName="txtTipoPago" Width="100%" AutoPostBack="false" ID="txtTipoPago" runat="server" TextField="TipoPago" ValueField="IdTipoPago">
                                                                                            </dx:BootstrapComboBox>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                                <div class="row">

                                                                                    <div class="col-sm-4">
                                                                                        <div class="form-group">
                                                                                            VALOR FACTURA
                                                                                        </div>
                                                                                    </div>



                                                                                    <div class="col-sm-4">
                                                                                        <div class="form-group">
                                                                                            IMPUESTO
                                                                                        </div>
                                                                                    </div>


                                                                                    <div class="col-sm-4">
                                                                                        <div class="form-group">
                                                                                            MONTO
                                                                                        </div>
                                                                                    </div>

                                                                                </div>


                                                                                <div class="row" style="margin-bottom: 10px">

                                                                                    <div class="col-sm-4">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">$</span>

                                                                                            <%--<dx:BootstrapTextBox  MaxLength="10" ID="ValorFactura" ma runat="server"></dx:BootstrapTextBox>--%>
                                                                                            <dx:BootstrapSpinEdit DisplayFormatString="n2" SpinButtons-ClientVisible="false" NumberType="Float" Number="0.00" ID="ValorFactura" ClientInstanceName="ValorFactura" runat="server" AllowNull="false" AllowMouseWheel="false">
                                                                                                <ClientSideEvents ValueChanged="function(s, e) { cbTotalCxP.PerformCallback(); }" />
                                                                                            </dx:BootstrapSpinEdit>
                                                                                        </div>
                                                                                    </div>



                                                                                    <div class="col-sm-4">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">$</span>

                                                                                            <dx:BootstrapSpinEdit DisplayFormatString="n2" SpinButtons-ClientVisible="false" NumberType="Float" Number="0.00" ID="Impuesto" ClientInstanceName="Impuesto" runat="server" AllowNull="false" AllowMouseWheel="false">
                                                                                                <ClientSideEvents ValueChanged="function(s, e) { cbTotalCxP.PerformCallback(); }" />
                                                                                            </dx:BootstrapSpinEdit>
                                                                                        </div>
                                                                                    </div>


                                                                                    <div class="col-sm-4">
                                                                                        <div class="input-group">
                                                                                            <span class="input-group-addon">$</span>




                                                                                            <dx:ASPxCallbackPanel ID="cbTotalCxP" ClientInstanceName="cbTotalCxP" OnCallback="cbTotalCxP_Callback" runat="server">
                                                                                                <ClientSideEvents></ClientSideEvents>
                                                                                                <PanelCollection>
                                                                                                    <dx:PanelContent>
                                                                                                        <dx:BootstrapSpinEdit ReadOnly="true" DisplayFormatString="n2" SpinButtons-ClientVisible="false" NumberType="Float" Number="0.00" ID="Monto" ClientInstanceName="Monto" runat="server" AllowNull="false" AllowMouseWheel="false"></dx:BootstrapSpinEdit>
                                                                                                    </dx:PanelContent>
                                                                                                </PanelCollection>
                                                                                            </dx:ASPxCallbackPanel>





                                                                                        </div>
                                                                                    </div>

                                                                                </div>




                                                                            </div>
                                                                        </div>
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <strong>Observación</strong>
                                                                            </div>
                                                                            <div class="panel-body">

                                                                                <div class="row">
                                                                                    <div class="col-sm-12">
                                                                                        <div class="form-group" style="padding-bottom: 10px">
                                                                                            <textarea runat="server" maxlength="512" rows="4" class="form-control Observacion" id="Observacion" name="Observacion" placeholder="Observación"></textarea>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-3" style="padding-right: 15px">
                                                                        <!-- One of three columns-->

                                                                        <div class="panel panel-default panel-data-registro">
                                                                            <div class="panel-heading">
                                                                                <strong>Datos del Registro</strong>
                                                                            </div>
                                                                            <div class="panel-body" style="padding-bottom: 10px;">

                                                                                <div class="row">
                                                                                    <div class="col-sm-12">
                                                                                        <p>Fecha de Registro: <strong><span runat="server" id="fechaRegistro"></span></strong></p>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="row">
                                                                                    <div class="col-sm-12">
                                                                                        <p>Registrado por: <strong><span runat="server" id="registradoPor"></span></strong></p>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="row">
                                                                                    <div class="col-sm-12">
                                                                                        <p>Ultima modificación: <strong><span runat="server" id="ultimaModificacion"></span></strong></p>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="row">
                                                                                    <div class="col-sm-12">
                                                                                        <p>Modificado por: <strong><span runat="server" id="modificadoPor"></span></strong></p>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="row">
                                                                                    <div class="col-sm-12">
                                                                                    </div>
                                                                                </div>

                                                                            </div>
                                                                        </div>


                                                                        <div class="panel panel-default panel-data-registro" style="margin-top: -10px">
                                                                            <div class="panel-heading">
                                                                                <strong>Antigüedad</strong>
                                                                            </div>
                                                                            <div class="panel-body" style="padding-bottom: 10px;">
                                                                                <p>Ubicación: <strong><span runat="server" id="ultimaUbicacion"></span></strong></p>

                                                                                <p>Estatus: <strong><span runat="server" id="ultimoEstatus"></span></strong></p>
                                                                            </div>
                                                                        </div>
                                                                        
                                                                            <div class="panel panel-default panel-data-registro" style="margin-top: -10px">
                                                                                <div class="panel-heading">
                                                                                    <strong>Información de Pago</strong>
                                                                                </div>
                                                                                <div class="panel-body" style="padding-bottom: 10px;">
                                                                                    <div class="row" style="margin-bottom: 10px">
                                                                                        <div class="col-sm-12">
                                                                                            <div class="input-group">
                                                                                                <span class="input-group-addon">No. Libramiento</span>
                                                                                                <dx:BootstrapTextBox Enabled="false" ClientInstanceName="NumeroLibramiento" MaxLength="50" ID="NumeroLibramiento" runat="server"></dx:BootstrapTextBox>
                                                                                            </div>
                                                                                        </div>

                                                                                    </div>
                                                                                    <div class="row" style="margin-bottom: 10px">
                                                                                        <div class="col-sm-12">
                                                                                            <div class="input-group">
                                                                                                <span class="input-group-addon">No. Ordenamiento</span>
                                                                                                <dx:BootstrapTextBox Enabled="false" ClientInstanceName="NumeroOrdenamiento" MaxLength="50" ID="NumeroOrdenamiento" runat="server"></dx:BootstrapTextBox>
                                                                                            </div>
                                                                                        </div>

                                                                                    </div>

                                                                                </div>
                                                                            </div>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </dx:ContentControl>
                                                    </ContentCollection>
                                                </dx:BootstrapTabPage>
                                                <dx:BootstrapTabPage Text="CRONOLOGÍA" Name="TabCronologia">
                                                    <ContentCollection>
                                                        <dx:ContentControl runat="server" SupportsDisabledAttribute="True">
                                                            <!--- cronologia ----->
                                                            <div class="data-grid" style="border: none; left: 0px; right: 0px; top: 50px; bottom: 0px;">

                                                                <div class="data-grid-contenido" style="top: 25px">

                                                                    <dx:ASPxGridView ID="grdCronologia" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" HorizontalScrollBarMode="Auto" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">


                                                                        <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                                                        <Settings ShowFooter="true" ShowHeaderFilterButton="false" VerticalScrollBarMode="Auto" VerticalScrollableHeight="300" VerticalScrollBarStyle="VirtualSmooth" />
                                                                        <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                                                        <Columns>

                                                                            <dx:GridViewDataTextColumn FieldName="Ubicacion" Name="gcUbicacion" Caption="Ubicaci&#243;n Actual" VisibleIndex="1"></dx:GridViewDataTextColumn>
                                                                            <dx:GridViewDataTextColumn FieldName="Estatus" Name="gcEstatus" Caption="Estatus" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                                                            <dx:GridViewDataTextColumn FieldName="DepartamentoRemitente" Name="gcDepartamentoRemitente" Caption="Departamento Remitente" VisibleIndex="3"></dx:GridViewDataTextColumn>
                                                                            <dx:GridViewDataTextColumn Width="200" CellStyle-HorizontalAlign="Center" FieldName="EnviadoPor" Name="gcEnviadoPor" Caption="Enviado por" VisibleIndex="4"></dx:GridViewDataTextColumn>
                                                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" Width="100" CellStyle-HorizontalAlign="Center" FieldName="Fecha" Name="gcFecha" Caption="Fecha" VisibleIndex="5"></dx:GridViewDataTextColumn>
                                                                            <dx:GridViewDataTextColumn FieldName="Comentario" Name="gcComentario" Caption="Comentario" VisibleIndex="6"></dx:GridViewDataTextColumn>
                                                                        </Columns>
                                                                    </dx:ASPxGridView>

                                                                </div>
                                                            </div>
                                                            <!--- ----->
                                                        </dx:ContentControl>
                                                    </ContentCollection>
                                                </dx:BootstrapTabPage>
                                                <dx:BootstrapTabPage Text="DOCUMENTOS" Name="TabDocumentos">
                                                    <ContentCollection>
                                                        <dx:ContentControl runat="server" SupportsDisabledAttribute="True">
                                                            <div class="data-grid" style="border: none; left: 0px; right: 0px; top: 50px; bottom: 0px;">
                                                                <div style="position: absolute">
                                                                    <div class="input-group" style="margin-top: 25px">
                                                                        <%--onchange="__doPostBack('ContentPlaceHolder1_cbPrincipal_btnAgregarArchivo_CD','CARGAR_ARCHIVO');"--%>
                                                                        <%--<asp:FileUpload ID="cargador" CssClass="btn" Style="width: 85%; height: 15px;" runat="server" />--%>
                                                                        <%--<asp:FileUpload ID="uploader" runat="server" />--%>

                                                                        <dx:BootstrapUploadControl ID="UploadControl" runat="server" ShowUploadButton="true" DialogTriggerID="externalDropZone"
                                                                            ShowProgressPanel="True" OnFileUploadComplete="UploadControl_FileUploadComplete">
                                                                            <UploadButton IconCssClass="glyphicon glyphicon-cloud-upload" Text="Cargar Archivo" />
                                                                            <AdvancedModeSettings EnableDragAndDrop="True" EnableFileList="False" EnableMultiSelect="False" ExternalDropZoneID="externalDropZone" DropZoneText="Soltar archivo aquí" />
                                                                            <ValidationSettings MaxFileSize="200194304" />
                                                                            <BrowseButton Text="Agregar archivo" />
                                                                            <%--<DropZoneStyle CssClass="uploadControlDropZone" />--%>
                                                                            <%--<ProgressBarStyle CssClass="uploadControlProgressBar" />--%>
                                                                            <ClientSideEvents
                                                                                DropZoneEnter="function(s, e) { if(e.dropZone.id == 'externalDropZone') setElementVisible('dropZone', true); }"
                                                                                DropZoneLeave="function(s, e) { if(e.dropZone.id == 'externalDropZone') setElementVisible('dropZone', false); }" FileUploadComplete="onUploadControlFileUploadComplete"></ClientSideEvents>
                                                                        </dx:BootstrapUploadControl>
                                                                        <br />
                                                                        <%--<dx:ASPxButton Width="200" ID="btnAgregarArchivo" UseSubmitBehavior="false" Visible="true" AutoPostBack="true" Theme="MetropolisBlue" Text="Agregar Archivo" runat="server" OnClick="btnAgregarArchivo_Click">
                                                                <ClientSideEvents Click="function(s, e) { e.processOnServer = true;}"></ClientSideEvents>
                                                                <Image IconID="actions_addfile_16x16office2013"></Image> 
                                                            </dx:ASPxButton>--%>
                                                                    </div>
                                                                </div>
                                                                <div class="data-grid-contenido" style="top: 100px">

                                                                    <dx:ASPxCallbackPanel ID="cbDocumentos" runat="server" ClientInstanceName="cbDocumentos" OnCallback="cbDocumentos_Callback">
                                                                        <PanelCollection>
                                                                            <dx:PanelContent>

                                                                                <dx:ASPxGridView ID="grdArchivos" EnableCallBacks="false" ClientInstanceName="grdArchivos" runat="server" Theme="MetropolisBlue" SettingsBehavior-EnableCustomizationWindow="true" OnCustomButtonInitialize="grdArchivos_CustomButtonInitialize" OnCustomButtonCallback="grdArchivos_CustomButtonCallback" HorizontalScrollBarMode="Auto" KeyFieldName="IdArchivo" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">
                                                                                    <%--<ClientSideEvents CustomButtonClick="function(s, e) {  cbDocumentos.PerformCallback('ELIMINAR_ARCHIVO|' + s.GetRowKey(e.visibleIndex)); }" />--%>
                                                                                    <ClientSideEvents CustomButtonClick="OncustomButtonClick" />
                                                                                    <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                                                                    <Settings ShowFooter="true" ShowHeaderFilterButton="false" VerticalScrollBarMode="Auto" VerticalScrollableHeight="300" VerticalScrollBarStyle="VirtualSmooth" />
                                                                                    <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                                                                    <Columns>
                                                                                        <dx:GridViewDataTextColumn Width="50" FieldName="IdDocumento" Name="gcIdDocumento" Caption="DOC" VisibleIndex="0" CellStyle-HorizontalAlign="Center"></dx:GridViewDataTextColumn>
                                                                                        <dx:GridViewDataTextColumn Width="50" FieldName="IdArchivo" Name="gcIdArchivo" Caption="ID" VisibleIndex="1" CellStyle-HorizontalAlign="Center"></dx:GridViewDataTextColumn>
                                                                                        <dx:GridViewDataTextColumn FieldName="Nombre" Name="gcNombre" Caption="Archivo" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                                                                        <dx:GridViewDataTextColumn Visible="false" FieldName="RutaVirtual" Name="gcRutaVirtual" Caption="Ruta Virtual" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                                                                        <dx:GridViewDataTextColumn Width="150" FieldName="UsuarioRegistraArchivo" Name="gcUsuarioRegistraArchivo" Caption="Agregado por" VisibleIndex="4"></dx:GridViewDataTextColumn>
                                                                                        <dx:GridViewDataCheckColumn Width="100" FieldName="EliminadoArchivo" Name="gcEliminadoArchivo" Caption="&#191;Eliminado?" VisibleIndex="3"></dx:GridViewDataCheckColumn>

                                                                                        <dx:GridViewCommandColumn Caption="#" Width="100" VisibleIndex="5">
                                                                                            <CustomButtons>
                                                                                                <dx:GridViewCommandColumnCustomButton Text="Visualizar" ID="btnVisualizar">
                                                                                                    <Image IconID="print_preview_16x16"></Image>
                                                                                                </dx:GridViewCommandColumnCustomButton>
                                                                                            </CustomButtons>
                                                                                        </dx:GridViewCommandColumn>
                                                                                        <dx:GridViewCommandColumn Caption="#" Width="100" VisibleIndex="5">
                                                                                            <CustomButtons>
                                                                                                <dx:GridViewCommandColumnCustomButton Text="Descargar" ID="btnDescargar">
                                                                                                    <Image IconID="actions_download_16x16"></Image>
                                                                                                </dx:GridViewCommandColumnCustomButton>
                                                                                            </CustomButtons>
                                                                                        </dx:GridViewCommandColumn>
                                                                                        <dx:GridViewCommandColumn Caption="#" Width="100" VisibleIndex="5">
                                                                                            <CustomButtons>
                                                                                                <dx:GridViewCommandColumnCustomButton Text="Eliminar" ID="btnEliminar">
                                                                                                    <Image IconID="edit_delete_16x16office2013"></Image>
                                                                                                </dx:GridViewCommandColumnCustomButton>
                                                                                            </CustomButtons>
                                                                                        </dx:GridViewCommandColumn>

                                                                                    </Columns>
                                                                                </dx:ASPxGridView>



                                                                                <dx:BootstrapPopupControl ID="ModalSubirArchivo" runat="server" ShowFooter="true"
                                                                                    ClientInstanceName="infoPopup" CloseAction="None"
                                                                                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                                                                                    <HeaderTemplate>
                                                                                        <h4>AGREGAR ARCHIVO
                                                                                        </h4>
                                                                                    </HeaderTemplate>
                                                                                    <FooterTemplate>
                                                                                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                                                                                            <ClientSideEvents Click="function(s, e) { $('#ContentPlaceHolder1_cbPrincipal_TabsExpediente_cbDocumentos_ModalSubirArchivo').hide();$('.modal-backdrop').hide() }" />
                                                                                        </dx:BootstrapButton>
                                                                                    </FooterTemplate>
                                                                                    <ContentCollection>
                                                                                        <dx:ContentControl>
                                                                                            <p id="ModalSubirArchivoContent" runat="server">El archivo se a agregado satisfactoriamente</p>
                                                                                        </dx:ContentControl>
                                                                                    </ContentCollection>
                                                                                </dx:BootstrapPopupControl>


                                                                                <dx:BootstrapPopupControl ID="ModalConfirmacion" runat="server" ShowFooter="true"
                                                                                    ClientInstanceName="infoPopup" CloseAction="None"
                                                                                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                                                                                    <HeaderTemplate>
                                                                                        <h4>Eliminar archivo
                                                                                        </h4>
                                                                                    </HeaderTemplate>
                                                                                    <FooterTemplate>
                                                                                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                                                                                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); }" />
                                                                                        </dx:BootstrapButton>
                                                                                    </FooterTemplate>
                                                                                    <ContentCollection>
                                                                                        <dx:ContentControl>
                                                                                            <p id="P1" runat="server">El archivo se a agregado satisfactoriamente</p>
                                                                                        </dx:ContentControl>
                                                                                    </ContentCollection>
                                                                                </dx:BootstrapPopupControl>

                                                                            </dx:PanelContent>
                                                                        </PanelCollection>
                                                                    </dx:ASPxCallbackPanel>



                                                                </div>
                                                            </div>
                                                        </dx:ContentControl>
                                                    </ContentCollection>
                                                </dx:BootstrapTabPage>
                                                <dx:BootstrapTabPage Text="REMITIR" Name="TabRemitir">
                                                    <ContentCollection>
                                                        <dx:ContentControl runat="server" SupportsDisabledAttribute="True">
                                                            <!--- ----->
                                                            <div class="panel panel-default" style="margin-top: 10px;">
                                                                <div class="panel-heading mi-header">
                                                                    <strong>Estatus y Destino del Expediente</strong>
                                                                </div>
                                                                <div class="panel-body" style="padding-bottom: 10px;">
                                                                    <div class="row">
                                                                        <div class="col-sm-5">
                                                                            <div class="input-group" style="margin-bottom: 10px; width: 100%">
                                                                                <span class="input-group-addon" style="width: 100px">Estatus</span>

                                                                                <dx:BootstrapComboBox EnableViewState="false" ViewStateMode="Disabled" Width="100%" AutoPostBack="false" ID="selectEstatus" runat="server" TextField="EstatusAplicado" ValueField="IdEstatusAplicado">
                                                                                </dx:BootstrapComboBox>

                                                                            </div>

                                                                        </div>
                                                                        <div class="col-sm-7">
                                                                            <div class="input-group" style="margin-bottom: 10px; width: 99%;">

                                                                                <span class="input-group-addon">Destinatario</span>

                                                                                <dx:BootstrapComboBox EnableViewState="false" ViewStateMode="Disabled" Width="100%" AutoPostBack="false" ID="selectDestinatario" runat="server" TextField="Destinatario" ValueField="IdDestinatario">
                                                                                </dx:BootstrapComboBox>

                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12">
                                                                        <div class="input-group" style="margin: -15px; padding: 0; margin-top: 0px; margin-bottom: 10px; width: 102%">
                                                                            <span class="input-group-addon" style="width: 100px">Comentario</span>
                                                                            <textarea style="margin: 0" rows="10" runat="server" maxlength="256" class="form-control" id="TxtComentario" name="TxtComentario" placeholder="Comentario"></textarea>
                                                                        </div>
                                                                    </div>


                                                                    <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-envelope" ID="btnEnviar" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text=" Gestionar">
                                                                        <SettingsBootstrap RenderOption="Success" />
                                                                        <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('ENVIAR|');}" />
                                                                    </dx:BootstrapButton>

                                                                </div>
                                                            </div>
                                                            <!--- ----->
                                                            <div style="position: absolute; color: #fff; bottom: 5px; left: 5px; right: 5px; height: 25px; border-top: 1px solid #ccc; padding-left: 10px">Registro: <span runat="server" id="RegistroSeleccionado"></span>Estatus: <span runat="server" id="EstatusActual"></span>Ultima Fecha: <span runat="server" id="UltimaFecha"></span><span id="Seccion" runat="server"></span></div>

                                                        </dx:ContentControl>
                                                    </ContentCollection>
                                                </dx:BootstrapTabPage>

                                            </TabPages>
                                        </dx:BootstrapPageControl>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    

                <dx:BootstrapPopupControl ID="ModalAplicarLibramiento" runat="server" ShowFooter="true"
                    ClientInstanceName="infoPopup" CloseAction="None"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                    <HeaderTemplate>
                        <h4>APLICAR LIBRAMIENTO
                        </h4>
                    </HeaderTemplate>
                    <FooterTemplate>
                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                            <ClientSideEvents Click="function(s, e) { $('#ContentPlaceHolder1_cbPrincipal_ModalAplicarLibramiento').hide();$('.modal-backdrop').hide() }" />
                        </dx:BootstrapButton>
                    </FooterTemplate>
                    <ContentCollection>
                        <dx:ContentControl>
                            <p runat="server">El libramiento ha sido aplicado.</p>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>

                <dx:BootstrapPopupControl ID="ModalAplicarOrdenamiento" runat="server" ShowFooter="true"
                    ClientInstanceName="infoPopup" CloseAction="None"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                    <HeaderTemplate>
                        <h4>APLICAR ORDENAMIENTO
                        </h4>
                    </HeaderTemplate>
                    <FooterTemplate>
                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                            <ClientSideEvents Click="function(s, e) { $('#ContentPlaceHolder1_cbPrincipal_ModalAplicarOrdenamiento').hide();$('.modal-backdrop').hide() }" />
                        </dx:BootstrapButton>
                    </FooterTemplate>
                    <ContentCollection>
                        <dx:ContentControl>
                            <p runat="server">El número de ordenamiento ha sido aplicado.</p>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>

                <dx:BootstrapPopupControl ID="ModalSalvarRegistro" runat="server" ShowFooter="true"
                    ClientInstanceName="infoPopup" CloseAction="None"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                    <HeaderTemplate>
                        <h4>SALVANDO REGISTRO
                        </h4>
                    </HeaderTemplate>
                    <FooterTemplate>
                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); $('#ContentPlaceHolder1_cbPrincipal_ModalSalvarRegistro').hide();$('.modal-backdrop').hide() }" />
                        </dx:BootstrapButton>
                    </FooterTemplate>
                    <ContentCollection>
                        <dx:ContentControl>
                            <p id="msjSalvado" runat="server">El registro se ha salvado correctamente</p>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>

                <dx:BootstrapPopupControl ID="ModalEnviar" runat="server" ShowFooter="true"
                    ClientInstanceName="infoPopup" CloseAction="None"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                    <HeaderTemplate>
                        <h4>ENVIANDO EXPEDIENTE
                        </h4>
                    </HeaderTemplate>
                    <FooterTemplate>
                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); $('#ContentPlaceHolder1_cbPrincipal_ModalEnviar').hide();$('.modal-backdrop').hide(); window.history.back(); }" />
                        </dx:BootstrapButton>
                    </FooterTemplate>
                    <ContentCollection>
                        <dx:ContentControl>
                            <p id="modalMensajeEnviar" runat="server">El registro se ha envíado correctamente</p>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>

                <dx:BootstrapPopupControl ID="ModalActualizarRegistro" runat="server" ShowFooter="true" CloseAction="CloseButton"
                    ClientInstanceName="infoPopup"
                    PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" Width="500px" Modal="true">
                    <HeaderTemplate>
                        <h4>ACTUALIZANDO REGISTRO</h4>
                    </HeaderTemplate>
                    <FooterTemplate>
                        <dx:BootstrapButton runat="server" Text="ACEPTAR" AutoPostBack="false" UseSubmitBehavior="false">
                            <ClientSideEvents Click="function(s, e) { infoPopup.Hide(); $('#ContentPlaceHolder1_cbPrincipal_ModalActualizarRegistro').hide();$('.modal-backdrop').hide() }" />
                        </dx:BootstrapButton>
                    </FooterTemplate>
                    <ContentCollection>
                        <dx:ContentControl>
                            <p id="modalMensajeActualizar" runat="server">El registro se ha actualizado correctamente</p>
                        </dx:ContentControl>
                    </ContentCollection>
                </dx:BootstrapPopupControl>

            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>

﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using SI_INAIPI.Controls.CxP;
using SI_INAIPI.DataAccess.CxP;
using SI_INAIPI.Models.CxP;
using INAIPI.Core;
using static SI_INAIPI.Global;
using System.Text.RegularExpressions;
using DevExpress.Web;
using System.IO;
using SI_INAIPI.Models.DIG;
using SI_INAIPI.Controls.DIG;
using DevExpress.Web.Demos;
using System.Net;

namespace GestionExpedientes
{
    public partial class Formulario : BasePage
    {
        CxP_PagosExpedientesCtrl ctrl = new CxP_PagosExpedientesCtrl();

        private CxP_PagosExpedientes PagoExpediente
        {
            get { return (CxP_PagosExpedientes)Session["Default_PagoExpediente"]; }
            set { Session["Default_PagoExpediente"] = value; }
        }
        private List<CxP_PagosExpedientes> PagoExpedientes
        {
            get { return (List<CxP_PagosExpedientes>)Session["Default_PagoExpedientes"]; }
            set { Session["Default_PagoExpedientes"] = value; }
        }

        private List<CxP_PagosExpedientesTiposDocumentos> ListTiposDocumentos
        {
            get { return (List<CxP_PagosExpedientesTiposDocumentos>)Session["Default_ListTiposDocumentos"]; }
            set { Session["Default_ListTiposDocumentos"] = value; }
        }
        private decimal UsuarioRemitente
        {
            get { return Convert.ToDecimal(Session["UsuarioRemitente"] ?? "0"); }
            set { Session["UsuarioRemitente"] = value; }
        }
        private List<DIG_Documentos.DIG_DocumentosArchivos> ListarDocumentos
        {
            get { return (List<DIG_Documentos.DIG_DocumentosArchivos>)Session["Default_ListarDocumentos"]; }
            set { Session["Default_ListarDocumentos"] = value; }
        }
        private String Src
        {
            get { return (String)Session["Default_Src"]; }
            set { Session["Default_Src"] = value; }
        }
        private List<CxP_PagosExpedientesProcedencias> ListProcedencias
        {
            get { return (List<CxP_PagosExpedientesProcedencias>)Session["Default_ListProcedencias"]; }
            set { Session["Default_ListProcedencias"] = value; }
        }
        private List<CxP_PagosExpedientesBeneficiarios> ListBeneficiarios
        {
            get { return (List<CxP_PagosExpedientesBeneficiarios>)Session["Default_ListBeneficiarios"]; }
            set { Session["Default_ListBeneficiarios"] = value; }
        }
        private List<CxP_PagosExpedientesTiposPagos> ListTiposPagos
        {
            get { return (List<CxP_PagosExpedientesTiposPagos>)Session["Default_ListTiposPagos"]; }
            set { Session["Default_ListTiposPagos"] = value; }
        }
        private List<CxP_PagosExpedientesCronologia> PagoExpedientesCronologia
        {
            get { return (List<CxP_PagosExpedientesCronologia>)Session["Default_PagoExpedientesCronologia"]; }
            set { Session["Default_PagoExpedientesCronologia"] = value; }
        }
        private List<CxP_PagosExpedientesEstatus> ListExpedientesEstatus
        {
            get { return (List<CxP_PagosExpedientesEstatus>)Session["Default_ListExpedientesEstatus"]; }
            set { Session["Default_ListExpedientesEstatus"] = value; }
        }
        private List<CxP_PagosExpedientesDestinatarios> ListDestinatarios
        {
            get { return (List<CxP_PagosExpedientesDestinatarios>)Session["Default_ListDestinatarios"]; }
            set { Session["Default_ListDestinatarios"] = value; }
        }
        private List<CxP_PagosExpedientesMensajeros> ListMensajeros
        {
            get { return (List<CxP_PagosExpedientesMensajeros>)Session["Default_ListMensajeros"]; }
            set { Session["Default_ListMensajeros"] = value; }
        }
        private string SeccionActiva
        {
            get { return (string)Session["Default_SeccionActiva"]; }
            set { Session["Default_SeccionActiva"] = value; }
        }
        private bool Editar
        {
            get { return (bool)Session["Default_Editar"]; }
            set { Session["Default_Editar"] = value; }
        }
        private List<CorreoElectronicoDestinatarios> ListCorreos
        {
            get { return (List<CorreoElectronicoDestinatarios>)Session["Default_PagoExpedientesCorreos"]; }
            set { Session["Default_PagoExpedientesCorreos"] = value; }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsCallback)
            {
                string UsuarioRemitente = ctrl.Decode(Request.QueryString[ctrl.Encode("UsuarioRemitente")] ?? ""); //Request.QueryString["UsuarioRemitente"];
                string DepartamentoRemitente = ctrl.Decode(Request.QueryString[ctrl.Encode("DepartamentoRemitente")] ?? ""); //Request.QueryString["DepartamentoRemitente"];
                string Rol = ctrl.Decode(Request.QueryString[ctrl.Encode("Rol")] ?? ""); //Request.QueryString["Rol"];
                string btnSeccionado = ctrl.Decode(Request.QueryString[ctrl.Encode("btnSeccionado")] ?? ""); //Request.QueryString["Rol"];

                if (string.IsNullOrEmpty(UsuarioRemitente) | string.IsNullOrEmpty(DepartamentoRemitente) | string.IsNullOrEmpty(Rol))
                {
                    Response.Redirect(System.Configuration.ConfigurationManager.AppSettings["MenuPrincipalUrl"]);
                }

                Session["UsuarioRemitente"] = UsuarioRemitente; //"2023";
                Session["DepartamentoRemitente"] = DepartamentoRemitente; // "22"; //22, 30, 23, 25
                Session["Rol"] = Rol; //"Administrador";               


                //PreviewDocumento.Visible = false;

                Usuario userLogin = new Usuario();
                userLogin = ctrl.Usuario(UsuarioRemitente.ToString().ToInt());

                LoginUser.InnerText = userLogin.NombreCompleto;
                LoginDepartamento.InnerText = userLogin.Departamento;
                FechaEntrega.Value = DateTime.Now;

                //btnActualizar.Visible = false;
                // btnSalvar.Visible = true;

                UISeccionActiva();
                OcultarTabs();

                if (PagoExpediente != null)
                {
                    OpenRegistro();

                    Cronologia(PagoExpediente.IdExpediente);
                    LitadoArchivos(PagoExpediente.IdExpediente);
                    ListadoExepedienteEstatus();
                    ListadoDestinatarios(PagoExpediente.IdExpediente);


                }
                ListadoTiposDeDocumentos();
                ListadoProcedencias1();
                ListadoBeneficiarios();
                ListadoTiposPagos();
                ListadoMensajeros();

                Totales(DepartamentoRemitente.ToString().ToInt());

            }
        }
        public void OcultarTabs()
        {
            TabsExpediente.TabPages.FindByName("TabCronologia").Visible = false;
            TabsExpediente.TabPages.FindByName("TabDocumentos").Visible = false;
            TabsExpediente.TabPages.FindByName("TabRemitir").Visible = false;

            if (SeccionActiva == "REGISTRADOS" && Editar == true)
            {
                TabsExpediente.TabPages.FindByName("TabCronologia").Visible = true;
                TabsExpediente.TabPages.FindByName("TabDocumentos").Visible = true;
                TabsExpediente.TabPages.FindByName("TabRemitir").Visible = false;
                UploadControl.Enabled = false;
            }
            if (SeccionActiva == "RECIBIDOS")
            {
                TabsExpediente.TabPages.FindByName("TabCronologia").Visible = true;
                TabsExpediente.TabPages.FindByName("TabDocumentos").Visible = true;
                TabsExpediente.TabPages.FindByName("TabRemitir").Visible = true;
            }
            if (SeccionActiva == "ENVIADOS")
            {
                TabsExpediente.TabPages.FindByName("TabCronologia").Visible = true;
                TabsExpediente.TabPages.FindByName("TabDocumentos").Visible = true;
                TabsExpediente.TabPages.FindByName("TabRemitir").Visible = false;
                UploadControl.Enabled = false;
            }
            if (SeccionActiva == "CONSULTAR")
            {
                TabsExpediente.TabPages.FindByName("TabCronologia").Visible = true;
                TabsExpediente.TabPages.FindByName("TabDocumentos").Visible = true;
                TabsExpediente.TabPages.FindByName("TabRemitir").Visible = false;
                UploadControl.Enabled = false;
            }

        }
        public void Totales(int remitente)
        {
            btnRegistros.Badge.Text = ctrl.TotalRegistrados(remitente).ToString();
            btnRecibidos.Badge.Text = ctrl.TotalRecibidos(remitente).ToString();
            btnEnviados.Badge.Text = ctrl.TotalEnviados(remitente).ToString();
        }
        public void Redireccionar(string page, string parametro)
        {
            if (!string.IsNullOrEmpty(parametro))
            {
                PagoExpediente = PagoExpedientes.FirstOrDefault(x => x.IdExpediente == parametro.ToString().ToInt());
            }


            string strPathAndQuery = HttpContext.Current.Request.Url.PathAndQuery;
            string strUrl = HttpContext.Current.Request.Url.AbsoluteUri.Replace(strPathAndQuery, "/");

            string[] split = strPathAndQuery.Split('?');

            DevExpress.Web.ASPxWebControl.RedirectOnCallback(strUrl + page + ".aspx?" + split[1]);

        }
        protected void Page_Init(object sender, EventArgs e)
        {

            cbTipoDocumento.DataSource = ListTiposDocumentos;
            cbTipoDocumento.DataBind();

            txtProcedencia.DataSource = ListProcedencias;
            txtProcedencia.DataBind();

            txtBeneficiario.DataSource = ListBeneficiarios;
            txtBeneficiario.DataBind();

            txtTipoPago.DataSource = ListTiposPagos;
            txtTipoPago.DataBind();

            txtEntregadoPor.DataSource = ListMensajeros;
            txtEntregadoPor.DataBind();

            selectDestinatario.DataSource = ListDestinatarios;
            selectDestinatario.DataBind();

            selectEstatus.DataSource = ListExpedientesEstatus;
            selectEstatus.DataBind();

        }
        protected void cbPrincipal_Callback(object sender, DevExpress.Web.CallbackEventArgsBase e)
        {
            string[] parametro = e.Parameter.Split('|');

            switch (parametro[0])
            {
                //case "ABRIR-EXPEDIENTE":
                //    Redireccionar("Formulario", parametro[1]);
                //    break;

                case "CONSULTAR-EXPEDIENTES":
                    SeccionActiva = "CONSULTAR";
                    Redireccionar("Consultar", parametro[1]);
                    break;

                case "NUEVO":
                    SeccionActiva = "REGISTRADOS";
                    Editar = false;
                    PagoExpediente = null;
                    Redireccionar("Formulario", parametro[1]);
                    break;

                case "EXPEDIENTES-REGISTRADOS":
                    Redireccionar("Registrados", parametro[1]);
                    break;

                case "EXPEDIENTES-RECIBIDOS":
                    Redireccionar("Recibidos", parametro[1]);
                    break;

                case "EXPEDIENTES-ENVIADOS":
                    Redireccionar("Enviados", parametro[1]);
                    break;
                case "SUBIR_ARCHIVO":
                    ModalSubirArchivoContent.InnerText = "El archivo se a agregado satisfactoriamente";
                    ModalSubirArchivo.ShowOnPageLoad = true;

                    ListarDocumentos = ctrl.ListarArchivosDocumento(PagoExpediente.IdDocumento.Value);
                    grdArchivos.DataSource = ListarDocumentos;
                    grdArchivos.DataBind();
                    //TODO: MENSAJE DE TODO BIEN
                    break;
                case "SALVAR":
                    SalvarExpediente();
                    break;

                case "ACTUALIZAR":
                    ActualizarExpediente();

                    //ModalActualizarRegistro.ShowHeader = true;
                    //ModalActualizarRegistro.HeaderText = "Actualizando registro";
                    //ModalActualizarRegistro.PopupVerticalAlign = DevExpress.Web.PopupVerticalAlign.WindowCenter;
                    //ModalActualizarRegistro.PopupHorizontalAlign = DevExpress.Web.PopupHorizontalAlign.WindowCenter;


                    //ModalActualizarRegistro.Height = 200;
                    break;

                case "APLICAR_LIBRAMIENTO":
                    AplicarLibramiento();
                    break;

                case "APLICAR_ORDENAMIENTO":
                    AplicarOrdenamiento();
                    break;

                case "ENVIAR":
                    Gestionar();
                    break;
            }

        }

        public void AplicarLibramiento()
        {
            PagoExpediente.NumeroLibramiento = NumeroLibramiento.Text;

            Resultado aplicarLibramiento = ctrl.AplicarLibramiento(PagoExpediente);
            if (aplicarLibramiento.TodoBien == true)
            {
                ModalAplicarLibramiento.ShowOnPageLoad = true;
            }
        }

        public void AplicarOrdenamiento()
        {
            PagoExpediente.NumeroOrdenamiento = NumeroOrdenamiento.Text;

            Resultado aplicarOrdenamiento = ctrl.AplicarOrdenamiento(PagoExpediente);
            if (aplicarOrdenamiento.TodoBien == true)
            {
                ModalAplicarOrdenamiento.ShowOnPageLoad = true;
            }
        }

        protected void cbTotalCxP_Callback(object sender, DevExpress.Web.CallbackEventArgsBase e)
        {
            Monto.Number = ValorFactura.Number + Impuesto.Number;
        }
        protected void UploadControl_FileUploadComplete(object sender, FileUploadCompleteEventArgs e)
        {
            SubirArchivo(e.UploadedFile.FileName, e.UploadedFile.FileBytes, PagoExpediente);
        }
        private void SubirArchivo(string FileName, byte[] FileBytes, CxP_PagosExpedientes PagoExpediente)
        {
            Resultado r = new Resultado();

            DIG_Documentos documento = new DIG_Documentos();
            documento.IdDocumento = (PagoExpediente.IdDocumento ?? 0);
            documento.IdSecPKOrigen = PagoExpediente.IdProcedencia.ToString().ToInt();
            documento.CodigoDoc = PagoExpediente.NumeroDocumento;
            documento.FechaDoc = PagoExpediente.FechaEmision.Date;
            documento.NombreDoc = PagoExpediente.Asunto.Replace("/", "|");
            documento.NombreDoc = PagoExpediente.Asunto.Replace(" .", ".");
            documento.CedulaRNCDoc = PagoExpediente.RNC;
            documento.Asunto = PagoExpediente.Asunto.Replace("/", "|").Replace(System.Environment.NewLine, "");
            documento.Asunto = PagoExpediente.Asunto.Replace(" .", ".").Replace(System.Environment.NewLine, "");
            documento.IdCategoria = ListTiposDocumentos.FirstOrDefault(t => t.IdTipoDocumento == PagoExpediente.IdTipoDocumento).IdDocumentoCategoria;
            documento.Categoria = ListTiposDocumentos.FirstOrDefault(t => t.IdTipoDocumento == PagoExpediente.IdTipoDocumento).DocumentoCategoria; //TODO: BINDEAR ESTE CAMPO DEL IdDocumentoCategoria del tipo de documento
            documento.Observaciones = PagoExpediente.Observacion;
            documento.IdSecPKUbicacion = PagoExpediente.IdUltimaUbicacion.ToString().ToInt();
            //documento.IdSecPKDestino = PagoExpediente.IdUltimaUbicacion.ToString().ToInt();
            documento.IdUsuarioRegistra = UsuarioRemitente;//PagoExpediente.IdUsuarioRegistra.ToString().ToInt();

            DIG_Documentos.DIG_DocumentosArchivos archivo = new DIG_Documentos.DIG_DocumentosArchivos { IdDocumento = documento.IdDocumento, IdArchivo = (ListarDocumentos.Count + 1), Nombre = FileName, Archivo = FileBytes, IdUsuarioRegistraArchivo = UsuarioRemitente };
            if (documento.IdDocumento > 0)
            {
                r = ctrl.AgregarArchivo(documento, archivo);
            }
            else
            {
                documento.Archivos.Add(archivo);
                r = ctrl.SalvarArchivos(PagoExpediente, documento);
            }

            if (r.TodoBien)
            {
                PagoExpediente.IdDocumento = documento.IdDocumento;
            }
            else
            {
                //TODO: MENSAJE DE ERROR
                ModalSubirArchivo.ShowOnPageLoad = true;
                ModalSubirArchivoContent.InnerText = "Error. El archivo no pudo subir al servidor";
            }

            //documento.NombreRelacionadoDoc = PagoExpediente.nu

            //documento.Archivos.Add(new DIG_Documentos.DIG_DocumentosArchivos { Archivo = cargador.UploadedFiles[0].UploadedFiles[0].FileBytes, Nombre = cargador.UploadedFiles[0].FileName } );
            //documento.Archivos.Add(new DIG_Documentos.DIG_DocumentosArchivos { Archivo = cargador.fi, Nombre = cargador.UploadedFiles[0].FileName });
            //ctrldig.Registrar(documento);


        }
        protected void grdArchivos_CustomButtonInitialize(object sender, ASPxGridViewCustomButtonEventArgs e)
        {
            DIG_Documentos.DIG_DocumentosArchivos archivo = (DIG_Documentos.DIG_DocumentosArchivos)grdArchivos.GetRow(e.VisibleIndex);

            if (archivo != null)
            {
                e.Enabled = !archivo.EliminadoArchivo;
            }

            //if (archivo.EliminadoArchivo)
            //{
            //    if (e.ButtonID.ToUpper() == "BTNELIMINAR")
            //    {
            //        //e.Text = "Reactivar";
            //        //e.Image.IconID = "actions_convert_16x16office2013";
            //        e.Enabled = false;
            //    }

            //    if (e.ButtonID.ToUpper() == "BTNVISUALIZAR")
            //    {
            //        e.Enabled = false;
            //    }

            //    if (e.ButtonID.ToUpper() == "BTNDESCARGAR")
            //    {
            //        e.Enabled = false;
            //    }
            //}


        }
        protected void grdArchivos_CustomButtonCallback(object sender, ASPxGridViewCustomButtonCallbackEventArgs e)
        {
            DIG_Documentos.DIG_DocumentosArchivos archivo = (DIG_Documentos.DIG_DocumentosArchivos)grdArchivos.GetRow(e.VisibleIndex);

            switch (e.ButtonID)
            {
                case "btnDescargar":
                    DescargarArchivo(archivo);
                    break;

                case "btnVisualizar":
                    //PreviewDocumento.Visible = true;

                    //Src = archivo.RutaVirtual.ToString().Replace("~", "http://localhost:51196");
                    Src = archivo.RutaVirtual.ToString().Replace("~", "http://sigepi.inaipi.gob.do");

                    //Iframe.Src = "http://localhost:51196/FileServer/Digitalizacion/REQUISICIONES%20(45)/asunto%20(23)/asdfsdfdsfi%20(4).pdf";
                    Iframe.Visible = true;
                    Iframe.Attributes.Add("src", Src);
                    break;
                default:
                    break;
            }

        }
        private void DescargarArchivo(DIG_Documentos.DIG_DocumentosArchivos Archivo)
        {

            if (Archivo.RutaFisica.Length > 0)
            {
                //FileInfo file = new FileInfo(Archivo.RutaFisica);
                FileInfo file = new FileInfo(System.Web.HttpContext.Current.Server.MapPath(Archivo?.RutaVirtual));

                if (file.Exists)
                {
                    Response.Clear();
                    Response.ClearHeaders();
                    Response.ClearContent();
                    Response.AddHeader("Content-Disposition", "attachment; filename=" + Archivo.Nombre);
                    Response.AddHeader("Content-Length", file.Length.ToString());
                    Response.ContentType = "text/plain";
                    Response.Flush();
                    //Response.WriteFile(file.FullName);
                    //Response.BinaryWrite(file.FullName);
                    Response.TransmitFile(file.FullName);
                    Response.End();
                    //HttpContext.Current.ApplicationInstance.CompleteRequest();

                }
                else
                {
                    //MsgBox("NO SE ENCONTRÓ ESTE ARCHIVO EN LA RUTA FÍSICA ESPECIFICADA.", Models.Comun.Msjtype.Error, DEFAULT_TITLE_FOR_ERROR);
                    //cbPrincipal.JSProperties["cp_title"] = DEFAULT_TITLE_FOR_ERROR;
                    //cbPrincipal.JSProperties["cp_text"] = "NO SE ENCONTRÓ ESTE ARCHIVO EN LA RUTA FÍSICA ESPECIFICADA.";
                    //cbPrincipal.JSProperties["cp_type"] = "error";
                }

                file = null;
            }
        }
        protected void cbDocumentos_Callback(object sender, CallbackEventArgsBase e)
        {

            string[] Parametros = e.Parameter.Split('|');


            switch (Parametros[0])
            {
                case "SUBIR_ARCHIVO":
                    ModalSubirArchivoContent.InnerText = "El archivo se a agregado satisfactoriamente";
                    ModalSubirArchivo.ShowOnPageLoad = true;


                    ListarDocumentos = ctrl.ListarArchivosDocumento(PagoExpediente.IdDocumento.ToString().ToInt());
                    grdArchivos.DataSource = ListarDocumentos;
                    grdArchivos.DataBind();
                    //TODO: MENSAJE DE TODO BIEN
                    break;

                case "ELIMINAR_ARCHIVO":
                    //int IdArchivo = 0;
                    int.TryParse(Parametros[1], out int IdArchivo);
                    DIG_Documentos.DIG_DocumentosArchivos archivo = ListarDocumentos.FirstOrDefault(a => a.IdArchivo == IdArchivo);
                    //Archivo = Archivos.FirstOrDefault(a => a.IdArchivo == IdArchivo);
                    //DIG_Documentos.DIG_DocumentosArchivos archivo = new DIG_Documentos.DIG_DocumentosArchivos();


                    //archivo.IdDocumento = 21;
                    // archivo.IdArchivo = IdArchivo;
                    //archivo.RutaFisica = "";
                    //archivo.RutaVirtual = "";

                    ctrl.EliminarArchivo(archivo, UsuarioRemitente);

                    ListarDocumentos = ctrl.ListarArchivosDocumento(PagoExpediente.IdDocumento.Value);
                    grdArchivos.DataSource = ListarDocumentos;
                    grdArchivos.DataBind();
                    break;
                case "DESCARGAR_ARCHIVO":

                    int.TryParse(Parametros[1], out int idArchivo);
                    DIG_Documentos.DIG_DocumentosArchivos Archivo = ListarDocumentos.FirstOrDefault(a => a.IdArchivo == idArchivo);
                    DescargarArchivo(Archivo);
                    break;

                case "VISUALIZAR_ARCHIVO":
                    int.TryParse(Parametros[1], out int vidArchivo);
                    DIG_Documentos.DIG_DocumentosArchivos vArchivo = ListarDocumentos.FirstOrDefault(a => a.IdArchivo == vidArchivo);
                    //Src = vArchivo.RutaVirtual.ToString().Replace("~", "http://localhost:51196");
                    Src = vArchivo.RutaVirtual.ToString().Replace("~", "http://sigepi.inaipi.gob.do");
                    //Iframe.Src = "http://localhost:51196/FileServer/Digitalizacion/REQUISICIONES%20(45)/asunto%20(23)/asdfsdfdsfi%20(4).pdf";
                    //PreviewDocumento.Visible = true;
                    Iframe.Attributes.Add("src", Src);
                    //PreviewDocumento.Visible = true;
                    //PArchivo.Value = 1;

                    break;

            }

        }
        public void OpenRegistro()
        {

            if (PagoExpediente != null)
            {

                //FechaEmision.Value = PagoExpediente.FechaEmision.Date.ToString("yyyy-MM-dd");
                FechaEmision.Value = PagoExpediente.FechaEmision;
                txtNumeroDocumento.Value = PagoExpediente.NumeroDocumento;
                Asunto.Value = PagoExpediente.Asunto;
                NCF.Value = PagoExpediente.NCF;
                Descripcion.Value = PagoExpediente.Descripcion;

                //FechaEntrega.Value = PagoExpediente.FechaEntrega.Date.ToString("yyyy-MM-dd");
                FechaEntrega.Value = PagoExpediente.FechaEntrega.ToString();
                HoraEntrega.Value = PagoExpediente.HoraEntrega.ToString();
                BeneficiarioCorreo.Text = PagoExpediente.BeneficiarioCorreo;

                ValorFactura.Value = PagoExpediente.ValorFactura.ToString();
                Impuesto.Value = PagoExpediente.Impuesto.ToString();
                Monto.Value = PagoExpediente.Monto.ToString();
                Observacion.Value = PagoExpediente.Observacion;
                fechaRegistro.InnerText = PagoExpediente.FechaRegistro.ToString();
                registradoPor.InnerText = PagoExpediente.RegistradoPor;
                modificadoPor.InnerText = PagoExpediente.ModificadoPor;
                ultimaModificacion.InnerText = PagoExpediente.FechaModifica.ToString();
                //labelIdExpediente.InnerText = PagoExpediente.IdExpediente.ToString();

                ultimaUbicacion.InnerText = PagoExpediente.Ubicacion;
                txtProcedencia.Value = PagoExpediente.IdProcedencia.ToString();
                txtEntregadoPor.Value = PagoExpediente.IdEntregadoPor.ToString();
                txtBeneficiario.Value = PagoExpediente.IdBeneficiario.ToString();
                cbTipoDocumento.Value = PagoExpediente.IdTipoDocumento.ToString();
                txtTipoPago.Value = PagoExpediente.IdTipoPago.ToString();
                ultimaUbicacion.InnerHtml = PagoExpediente.LabelUbicacion;
                ultimoEstatus.InnerHtml = PagoExpediente.LabelEstatus;
                txtNumeroRequisicion.Value = PagoExpediente.NumeroRequisicion;
                NumeroLibramiento.Text = PagoExpediente.NumeroLibramiento;
                NumeroOrdenamiento.Text = PagoExpediente.NumeroOrdenamiento;

                labelTituloModulo.InnerHtml = "Editar Expediente #" + PagoExpediente.IdExpediente.ToString();
            }


        }
        private void ListadoTiposDeDocumentos()
        {
            ListTiposDocumentos = ctrl.ListExpedientesTiposDocumentos();
            cbTipoDocumento.DataSource = ListTiposDocumentos;
            cbTipoDocumento.DataBind();
        }
        private void ListadoProcedencias1()
        {
            ListProcedencias = ctrl.ListProcedencias();
            txtProcedencia.DataSource = ListProcedencias;
            txtProcedencia.DataBind();
        }
        private void ListadoBeneficiarios()
        {
            ListBeneficiarios = ctrl.ListBeneficiarios();
            txtBeneficiario.DataSource = ListBeneficiarios;
            txtBeneficiario.DataBind();
        }
        private void ListadoTiposPagos()
        {
            ListTiposPagos = ctrl.ListExpedientesTiposPagos();
            txtTipoPago.DataSource = ListTiposPagos;
            txtTipoPago.DataBind();
        }
        private void Cronologia(int Idexpediente)
        {

            if (Idexpediente > 0)
            {
                PagoExpedientesCronologia = ctrl.Cronologia(Idexpediente);
            }
            grdCronologia.DataSource = PagoExpedientesCronologia;
            grdCronologia.DataBind();

        }
        private void LitadoArchivos(int Idexpediente)
        {
            // if (PagoExpediente.IdDocumento != null)
            // {
            if (Idexpediente > 0)
            {
                ListarDocumentos = ctrl.ListarArchivosDocumento(PagoExpediente.IdDocumento.ToString().ToInt());
            }
            grdArchivos.DataSource = ListarDocumentos;
            grdArchivos.DataBind();
            //}



        }
        private void ListadoExepedienteEstatus()
        {
            ListExpedientesEstatus = ctrl.ListExpedientesEstatus(Session["DepartamentoRemitente"].ToString().ToInt(), PagoExpediente.IdTipoDocumento.Value.ToString().ToInt());
            selectEstatus.DataSource = ListExpedientesEstatus;
            selectEstatus.DataBind();

        }
        private void ListadoDestinatarios(int Idexpediente)
        {
            if (Idexpediente > 0)
            {
                ListDestinatarios = ctrl.ListDestinatarios(Session["DepartamentoRemitente"].ToString().ToInt());
            }
            selectDestinatario.DataSource = ListDestinatarios;
            selectDestinatario.DataBind();
        }

        public void ActivarCampos()
        {
            cbTipoDocumento.ClientEnabled = true;
            txtProcedencia.ClientEnabled = true;
            txtBeneficiario.ClientEnabled = true;
            txtTipoPago.ClientEnabled = true;
        }
        private void ListadoMensajeros()
        {
            ListMensajeros = ctrl.ListMensajeros();
            txtEntregadoPor.DataSource = ListMensajeros;
            txtEntregadoPor.DataBind();
        }
        private void UISeccionActiva()
        {
            switch (SeccionActiva)
            {
                case "REGISTRADOS":
                    ActivarCampos();
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;

                    btnAplicarLibramiento.Visible = false;
                    btnAplicarOrdenamiento.Visible = false;
                    if (Editar == true)
                    {
                        btnSalvar.Visible = false;
                        btnActualizar.Visible = true;

                    }
                    else
                    {
                        btnSalvar.Visible = true;
                        btnActualizar.Visible = false;
                    }
                    break;
                case "RECIBIDOS":
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    if (Editar == false)
                    {
                        btnActualizar.Visible = false;
                        btnSalvar.Visible = false;
                        
                        //PagoExpediente.IdDepartamentoSolicitante = 30;

                       // PagoExpediente.IdDepartamentoSolicitante = 31;

                        if (Session["DepartamentoRemitente"].ToString().ToInt() == 31)
                        {
                            btnAplicarLibramiento.Visible = true;
                            NumeroLibramiento.Enabled = true;
                        }
                        if (Session["DepartamentoRemitente"].ToString().ToInt() == 30)
                        {
                            btnAplicarOrdenamiento.Visible = true;
                            NumeroOrdenamiento.Enabled = true;

                            btnAplicarLibramiento.Visible = true;
                            NumeroLibramiento.Enabled = true;
                        }
                     
                    }
                    break;
                case "ENVIADOS":
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Success;
                    if (Editar == false)
                    {
                        btnActualizar.Visible = false;
                        btnSalvar.Visible = false;
                    }
                    break;
                case "CONSULTAR":
                    btnRegistros.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnRecibidos.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;
                    btnEnviados.SettingsBootstrap.RenderOption = DevExpress.Web.Bootstrap.BootstrapRenderOption.Default;

                    if (Editar == false)
                    {
                        btnActualizar.Visible = false;
                        btnSalvar.Visible = false;
                    }
                    break;

            }
        }

        protected void cbPreviewDocumento_Callback(object sender, CallbackEventArgsBase e)
        {
            string[] Parametros = e.Parameter.Split('|');

            int.TryParse(Parametros[1], out int vidArchivo);
            DIG_Documentos.DIG_DocumentosArchivos vArchivo = ListarDocumentos.FirstOrDefault(a => a.IdArchivo == vidArchivo);
            //Src = vArchivo.RutaVirtual.ToString().Replace("~", "http://localhost:51196");
            Src = vArchivo.RutaVirtual.ToString().Replace("~", "http://sigepi.inaipi.gob.do");

            string extension = vArchivo.Nombre.Substring(vArchivo.Nombre.Length - 3);
            if (extension != "pdf")
            {
                IframeMensaje.Visible = true;
            }

            Iframe.Attributes.Add("src", Src);



            //Iframe.Attributes.Add("src", Src);
        }
        private bool ValidAndBinding()
        {


            bool todobien = true;
            byte.TryParse((cbTipoDocumento.Value ?? "").ToString(), out byte numIdTipoDocumento);
            if (numIdTipoDocumento <= 0)
            {
                cbTipoDocumento.IsValid = false;
                cbTipoDocumento.ErrorText = "Debe llenar este campo";
                todobien = false;
            }
            else
            {
                if (numIdTipoDocumento == 1)
                {


                    if (txtNumeroRequisicion.Value == null)
                    {
                        txtNumeroRequisicion.IsValid = false;
                        txtNumeroRequisicion.ErrorText = "Debe llenar este campo";
                        todobien = false;
                    }
                }
                else
                {
                    txtNumeroRequisicion.Value = null;
                }
            }


            DateTime.TryParse((FechaEmision.Value ?? "").ToString(), out DateTime fecFechaEmision);
            if (fecFechaEmision == new DateTime(1, 1, 1))
            {
                FechaEmision.IsValid = false;
                FechaEmision.ErrorText = "Debe seleccionar la fecha";
                todobien = false;

            }
            if (txtNumeroDocumento.Value == null)
            {
                txtNumeroDocumento.IsValid = false;
                txtNumeroDocumento.ErrorText = "Debe escribir el número del documento";
                todobien = false;
            }

            if (string.IsNullOrEmpty((Asunto.Value ?? "").ToString()))
            {
                Asunto.IsValid = false;
                Asunto.ErrorText = "Debe agregar el asunto sobre que trata el expediente";
                todobien = false;
            }

            if (numIdTipoDocumento == 1)
            {
                if (string.IsNullOrEmpty((NCF.Value ?? "").ToString()))
                {
                    NCF.IsValid = false;
                    NCF.ErrorText = "Debe llenar el comprobante fiscal";
                    todobien = false;
                }
                else
                {
                    if (!NCF.Value.ToString().IsValidNCF())
                    {
                        NCF.IsValid = false;
                        NCF.ErrorText = "El Número de comprobante fiscal es inválido";
                        todobien = false;
                    }
                }
            }
            else
            {
                NCF.IsValid = true;
                NCF.Text = "";
            }

            int.TryParse((txtProcedencia.Value ?? "").ToString(), out int numIdProcedencia);
            int.TryParse((txtEntregadoPor.Value ?? "").ToString(), out int numIdEntregadoPor);

            DateTime.TryParse((FechaEntrega.Value ?? "").ToString(), out DateTime fecFechaEntrega);
            if (fecFechaEntrega == new DateTime(1, 1, 1))
            {
                FechaEntrega.IsValid = false;
                FechaEntrega.ErrorText = "Debe seleccionar la fecha";
                todobien = false;

            }
            TimeSpan.TryParse((HoraEntrega.Value ?? "").ToString(), out TimeSpan timHoraEntrega);
            if (timHoraEntrega == new TimeSpan(1, 1, 1))
            {
                FechaEntrega.IsValid = false;
                FechaEntrega.ErrorText = "Debe seleccionar la hora en la que se entregó el documento físicamente";
                todobien = false;

            }

            Monto.Number = ValorFactura.Number + Impuesto.Number;

            decimal.TryParse((Monto.Number).ToString(), out decimal numMonto);
            decimal.TryParse((Impuesto.Number).ToString(), out decimal numImpuesto);
            decimal.TryParse((ValorFactura.Value ?? "").ToString(), out decimal numValorFactura);

            if (PagoExpediente == null)
            {
                PagoExpediente = new CxP_PagosExpedientes();
            }

            if (string.IsNullOrEmpty(txtBeneficiario.Text))
            {
                if (numIdTipoDocumento == 2)
                {
                    PagoExpediente.IdBeneficiario = null;
                }
                else
                {
                    txtBeneficiario.IsValid = false;
                    txtBeneficiario.ErrorText = "Debe seleccionar un Suplidor o Beneficiario";
                    todobien = false;
                }
            }
            else
            {
                int.TryParse((txtBeneficiario.Value ?? "").ToString(), out int numIdBeneficiario);
                PagoExpediente.IdBeneficiario = numIdBeneficiario;
            }

            if (string.IsNullOrEmpty(txtTipoPago.Text))
            {

                if (numIdTipoDocumento == 2)
                {
                    PagoExpediente.IdTipoPago = null;
                }
                else
                {
                    txtTipoPago.IsValid = false;
                    txtTipoPago.ErrorText = "Debe seleccionar el tipo de pago que aplica con esta factura";
                    todobien = false;
                }
            }
            else
            {
                byte.TryParse((txtTipoPago.Value ?? "").ToString(), out byte numIdTipoPago);
                PagoExpediente.IdTipoPago = numIdTipoPago;
            }

            PagoExpediente.IdTipoDocumento = numIdTipoDocumento;
            PagoExpediente.FechaEmision = fecFechaEmision;

            PagoExpediente.NumeroDocumento = txtNumeroDocumento.Text;

            PagoExpediente.Asunto = Asunto.Text;
            PagoExpediente.Descripcion = Descripcion.Value;
            PagoExpediente.IdProcedencia = numIdProcedencia;
            PagoExpediente.IdEntregadoPor = numIdEntregadoPor;

            PagoExpediente.FechaEntrega = fecFechaEntrega;
            PagoExpediente.HoraEntrega = timHoraEntrega;

            PagoExpediente.BeneficiarioCorreo = BeneficiarioCorreo.Text;

            PagoExpediente.Monto = numMonto;
            PagoExpediente.Observacion = Observacion.Value;
            PagoExpediente.IdUsuarioRegistra = UsuarioRemitente;
            PagoExpediente.IdUsuarioModifica = UsuarioRemitente;

            PagoExpediente.NCF = NCF.Text;

            PagoExpediente.IdDepartamentoSolicitante = Session["DepartamentoRemitente"].ToString().ToInt();
            PagoExpediente.UltimoEstatus = 1;
            PagoExpediente.IdUltimaUbicacion = Session["DepartamentoRemitente"].ToString().ToInt();

            PagoExpediente.ValorFactura = numValorFactura;
            PagoExpediente.Impuesto = numImpuesto;
            PagoExpediente.NumeroRequisicion = txtNumeroRequisicion.Text.ToString().ToIntNuleable();

            return todobien;
        }
        protected void AutoEnviar(int _IdExpediente)
        {
            CxP_PagosExpedientesEnviados ExpedienteEnviado = new CxP_PagosExpedientesEnviados();
            int.TryParse(PagoExpediente.IdExpediente.ToString(), out int intIdExpediente);
            ExpedienteEnviado.IdExpediente = _IdExpediente;
            int.TryParse(Session["DepartamentoRemitente"].ToString(), out int intDepartamentoRemitente);
            ExpedienteEnviado.IdDepartamentoRemitente = intDepartamentoRemitente;
            int.TryParse(Session["UsuarioRemitente"].ToString(), out int intUsuarioRemitente);
            ExpedienteEnviado.IdUsuarioRemitente = intUsuarioRemitente;
            ExpedienteEnviado.IdDepartamentoDestinatario = Session["DepartamentoRemitente"].ToString().ToInt();
            ExpedienteEnviado.Estatus = 1;

            ExpedienteEnviado.Comentario = "Registro iniciado";
            int.TryParse(selectEstatus.Value?.ToString(), out int intEstatusAplicado);

            ExpedienteEnviado.EstatusAplicado = 1;

            Resultado resultado = ctrl.EnviarExpediente(ExpedienteEnviado);

            if (resultado.TodoBien)
            {
                //ListadoExpedientesRecibidos(true);
                btnRegistros.Badge.Text = "";
                btnRecibidos.Badge.Text = "";
                btnEnviados.Badge.Text = "";
                Totales(Session["DepartamentoRemitente"].ToString().ToInt());
            }
            else
            {
                modalMensajeEnviar.InnerHtml = "Error al enviar " + resultado.strError;
                ModalEnviar.ShowOnPageLoad = true;
            }
        }
        public void SalvarExpediente()
        {
            if (ValidAndBinding())
            {

                Resultado resultado = ctrl.SalvarExpediente(PagoExpediente);
                if (resultado.TodoBien)
                {
                    btnSalvar.Visible = false;
                    btnActualizar.Visible = true;
                    msjSalvado.InnerText = "El registro se ha salvado correctamente. Expediente No.: " + resultado.ID.ToString();
                    ModalSalvarRegistro.ShowOnPageLoad = true;
                    AutoEnviar(resultado.ID.ToString().ToInt());
                    labelTituloModulo.InnerHtml = " Editar Expediente #" + resultado.ID.ToString();
                    Editar = true;
                }
                else
                {
                    msjSalvado.InnerText = "Error guardando el expediente. " + resultado.strError;
                    ModalSalvarRegistro.ShowOnPageLoad = true;
                    Editar = false;
                    btnSalvar.Visible = true;
                }
            }
            else
            {
                btnSalvar.Visible = true;

            }
        }

        public void ActualizarExpediente()
        {

            if (ValidAndBinding())
            {
                Resultado resultado = ctrl.ActualizarExpediente(PagoExpediente);


                if (resultado.TodoBien)
                {
                    //OpenRegistro(PagoExpediente.IdExpediente);
                    modalMensajeActualizar.InnerText = "El registro se ha actualizado correctamente. Expediente No.: " + PagoExpediente.IdExpediente;
                    ModalActualizarRegistro.ShowOnPageLoad = true;
                    PagoExpediente = ctrl.Consultar(PagoExpediente.IdExpediente);
                    OpenRegistro();
                }
            }

        }


        protected void Gestionar()
        {
            ListExpedientesEstatus = ctrl.ListExpedientesEstatus(Session["DepartamentoRemitente"].ToString().ToInt(), cbTipoDocumento.Value.ToString().ToInt());
            selectEstatus.DataSource = ListExpedientesEstatus;
            selectEstatus.DataBind();

            CxP_PagosExpedientesEnviados ExpedienteEnviado = new CxP_PagosExpedientesEnviados();
            int.TryParse(PagoExpediente.IdExpediente.ToString(), out int intIdExpediente);
            ExpedienteEnviado.IdExpediente = intIdExpediente;
            int.TryParse(Session["DepartamentoRemitente"].ToString(), out int intDepartamentoRemitente);
            ExpedienteEnviado.IdDepartamentoRemitente = intDepartamentoRemitente;
            int.TryParse(Session["UsuarioRemitente"].ToString(), out int intUsuarioRemitente);
            ExpedienteEnviado.IdUsuarioRemitente = intUsuarioRemitente;
            int.TryParse(selectDestinatario.Value?.ToString(), out int intDestinatario);
            ExpedienteEnviado.IdDepartamentoDestinatario = selectDestinatario.Value.ToString().ToInt();
            ExpedienteEnviado.Estatus = 1;
            ExpedienteEnviado.FechaEnviado = DateTime.Now;
            //ExpedienteEnviado.FechaEnviado = "2018-04-09 09:33:48.053";
            UltimaFecha.InnerText = PagoExpediente.FechaEnviado.ToString();

            ExpedienteEnviado.Comentario = TxtComentario.Value;
            int.TryParse(selectEstatus.Value?.ToString(), out int intEstatusAplicado);

            ExpedienteEnviado.EstatusAplicado = intEstatusAplicado;
            //ExpedienteEnviado.Estatus = 2;

            Resultado resultado = ctrl.EnviarExpediente(ExpedienteEnviado);

            if (resultado.TodoBien)
            {
                ExpedienteEnviado.IdRegistro = PagoExpediente.IdRegistro;
                ctrl.ActualizarUltimoEstatus(ExpedienteEnviado);

                CxP_PagosExpedientes Expediente = new CxP_PagosExpedientes();
                Expediente = ctrl.ResumenExpediente(resultado.ID.ToString().ToInt());

                ctrl.ActualizarEstatusRegistroEnviado(ExpedienteEnviado);

                ExpedienteEnviado.IdUltimaUbicacion = ExpedienteEnviado.IdDepartamentoDestinatario.ToString().ToInt();
                ExpedienteEnviado.UltimoEstatus = ExpedienteEnviado.EstatusAplicado.ToString().ToInt();

                modalMensajeEnviar.InnerHtml = "El expediente No.: " + PagoExpediente.IdExpediente + " Se ha enviado con exito";
                ModalEnviar.ShowOnPageLoad = true;

                ListCorreos = ctrl.ListCorreos(intDestinatario, intDepartamentoRemitente);

                CorreoElectronicoCtrl ManejarCorreo = new CorreoElectronicoCtrl();
                CorreoElectronico correo = new CorreoElectronico();

                correo.IdUsuario = Session["UsuarioRemitente"].ToString().ToInt();
                correo.PageId = HttpContext.Current.Request.Url.AbsoluteUri;
                correo.Asunto = "Gestión del Expediente No.: [" + ExpedienteEnviado.IdExpediente.ToString() + "]";

                DateTime hoy = DateTime.Today;
                DateTime.TryParse(UltimaFecha.InnerText.ToString(), out DateTime dateUltimaFecha);

                string totalDiasBarado = hoy.CalcularTiempoStr(dateUltimaFecha);

                Usuario user = new Usuario();
                user = ctrl.Usuario(intUsuarioRemitente);

                string UsuarioRemitente = user.NombreCompleto;

                string[] romperFechaHora = Expediente.FechaEntrega.ToString().Split(' ');
                string fechaEntrega = romperFechaHora[0].ToString();

                string DepartamentoRemitente = user.Departamento;

                string subTitulo = "Se ha enviado desde " + Expediente.DepartamentoRemitente + " hasta " + Expediente.DepartamentoDestinatario;
                string contenido = "";
                contenido += "<table width='600' border='0' cellspacing='0' cellpadding='0' style='border:1px solid #000;'><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Tipo de documento: </th><td style='border-bottom:1px solid #000; padding-left:5px;'>" + Expediente.TipoDocumento + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Fecha de emision: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + Expediente.FechaEmision + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>No documento: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + Expediente.NumeroDocumento + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Asunto: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + Expediente.Asunto + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Fecha de entrega: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + fechaEntrega + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Gestionado por: </th><td style='border-bottom:1px solid #000; padding-left:5px;text-transform:uppercase'>" + UsuarioRemitente + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>En fecha </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + ExpedienteEnviado.FechaEnviado + "</td></tr><tr><th width='200' style='text-align:left; border-bottom:1px solid #000; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase' scope='row'>Estuvo sin movimiento: </th><td style='border-bottom:1px solid #000; padding-left:5px; text-transform:uppercase'>" + totalDiasBarado + " con estatus: " + PagoExpediente.LabelEstatus + " </td></tr><tr><th width='200' scope='row' style='text-align:left; padding:5px; background-color:#0070cd; color:#fff; text-transform:uppercase'>Último estatus aplicado:</th><td style='padding-left:5px; text-transform:uppercase'>" + Expediente.LabelEstatus + "</td></tr></table>";

                correo.Para = ListCorreos.Where(g => g.EnviarComo == CorreoElectronicoDestinatarios.ModoEnvio.PARA && g.CorreoElectronico.IsValidEmail()).Select(u => u.CorreoElectronico).ToList();
                correo.Copia = ListCorreos.Where(g => g.EnviarComo == CorreoElectronicoDestinatarios.ModoEnvio.COPIA && g.CorreoElectronico.IsValidEmail()).Select(u => u.CorreoElectronico).ToList();
                correo.CopiaOculta = ListCorreos.Where(g => g.EnviarComo == CorreoElectronicoDestinatarios.ModoEnvio.COPIA_OCULTA && g.CorreoElectronico.IsValidEmail()).Select(u => u.CorreoElectronico).ToList();
                correo.Cuerpo = "<!DOCTYPE html PUBLIC \'-//W3C//DTD XHTML 1.0 Transitional //EN\' \'http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\'><html xmlns=\'http://www.w3.org/1999/xhtml\' xmlns:v=\'urn:schemas-microsoft-com:vml\' xmlns:o=\'urn:schemas-microsoft-com:office:office\'><head><meta http-equiv=\'Content-Type\' content=\'text/html; charset=utf-8\' /> <style type=\'text/css\'>  body, .mainTable { height:100% !important; width:100% !important; margin:0; padding:0; }  img, a img { border:0; outline:none; text-decoration:none; }  .imageFix { display:block; }  table, td { border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;}  p {margin-top:0; margin-right:0; margin-left:0; padding:0;}  .ReadMsgBody{width:100%;} .ExternalClass{width:100%;}.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, 	.ExternalClass td, 	.ExternalClass div { line-height: 100% ; } 	img { -ms-interpolation-mode: bicubic; 	} body, table, td, 	p, 	a, li, 	blockquote { -ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; } </style><style>table{ border-collapse: collapse; }@media only screen and (max-width: 600px) {    body[yahoo] .rimg {   max-width: 100%; height: auto; } body[yahoo].rtable { 	width: 100%!important;table-layout: fixed; } body[yahoo].rtable tr { height: auto!important; } } </style><!--[if gte mso 9]><xml>  <o:OfficeDocumentSettings> <o: AllowPNG / ><o: PixelsPerInch > 96</o:PixelsPerInch>  </o: OfficeDocumentSettings ></xml><![endif]--></head ><body yahoo = fix scroll = \'auto\' style=\'padding:0; margin:0; FONT-SIZE: 12px; FONT-FAMILY: Arial, Helvetica, sans-serif; cursor:auto; background:#F3F3F3\'><TABLE class=\'rtable mainTable\' cellSpacing=0 cellPadding=0 width=\'100%\' bgColor=#f3f3f3> <TR ><TD style = \'FONT-SIZE: 0px; HEIGHT: 20px; LINE-HEIGHT: 0\'>&#160;</TD></TR><TR><TD vAlign = top ><TABLE class = rtable style = \'WIDTH: 600px; MARGIN: 0px auto\' cellSpacing=0 cellPadding=0 width=600 align=center border=0> <TR ><TD style = \'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: medium none; PADDING-RIGHT: 0px; BACKGROUND-COLOR: transparent\'> <TABLE class = rtable style = \'WIDTH: 100%\' cellSpacing=0 cellPadding=0 align=left> <TR style = \'HEIGHT: 10px\'><TD style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 35px; TEXT-ALIGN: center; PADDING-TOP: 35px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent\'><TABLE cellSpacing=0 cellPadding=0 align=center border=0><TR> <TD style = \'PADDING-BOTTOM: 2px; PADDING-TOP: 2px; PADDING-LEFT: 2px; PADDING-RIGHT: 2px\' align=center><TABLE cellSpacing=0 cellPadding=0 border=0><TR><TD style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; BACKGROUND-COLOR: transparent\'><IMG class=rimg style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; DISPLAY: block; BACKGROUND-COLOR: transparent\' border=0 src=\'http://app.inaipi.gob.do:97/solicitudes/images/Image_1.png\' width=263 height=64 hspace=\'0\' vspace=\'0\'></TD></TR></TABLE></TD></TR></TABLE></TD></TR></TABLE></TD></TR><TR> <TD style = \'BORDER-TOP: medium none; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; PADDING-TOP: 1px; PADDING-LEFT: 0px; BORDER-LEFT: medium none; PADDING-RIGHT: 0px; BACKGROUND-COLOR: transparent\'><TABLE class=rtable style=\'WIDTH: 100%\' cellSpacing=0 cellPadding=0 align=left><TR style=\'HEIGHT: 10px\'><TD style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; TEXT-ALIGN: center; PADDING-TOP: 1px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent\'><P style=\'FONT-SIZE: 18px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #0070cd; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=center><STRONG>Instituto Nacional de Atenci&#243;n Integral a la Primera Infancia</STRONG><BR><FONT style=\'FONT-SIZE: 14px; COLOR: #ffa300\'><STRONG>&#161;Ser ni&#241;o y&#160;ni&#241;a nunca fue mejor&#160;!</STRONG></FONT><BR></P></TD></TR></TABLE></TD></TR> <TR ><TD style = \'BORDER-TOP: #dbdbdb 1px solid; BORDER-RIGHT: #dbdbdb 1px solid; BORDER-BOTTOM: #dbdbdb 1px solid; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: #dbdbdb 1px solid; PADDING-RIGHT: 0px; BACKGROUND-COLOR: #feffff\'><TABLE class=rtable style=\'WIDTH: 100%\' cellSpacing=0 cellPadding=0 align=left><TR style=\'HEIGHT: 10px\'><TD style=\'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 35px; TEXT-ALIGN: center; PADDING-TOP: 35px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: #feffff\'><P style=\'FONT-SIZE: 18px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #a8a7a7; LINE-HEIGHT: 155%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=center><STRONG><h2>" + correo.Asunto + "</h2></STRONG></P> <P style = \'FONT-SIZE: 12px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #a7a7a7; LINE-HEIGHT: 155%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=center></h1> <p style=\'font-size:14px\' >" + subTitulo + "</p><p>" + contenido + "</p> <p ></TD></TR></TABLE></TD></TR><TR> <TD style = \'BORDER-TOP: #dbdbdb 1px solid; BORDER-RIGHT: #dbdbdb 1px solid; BORDER-BOTTOM: #dbdbdb 1px solid; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 0px; BORDER-LEFT: #dbdbdb 1px solid; PADDING-RIGHT: 0px; BACKGROUND-COLOR: #feffff\'><TABLE class = rtable style = \'WIDTH: 100%\' cellSpacing=0 cellPadding=0 align=left><TR style=\'HEIGHT: 10px\'> <TD style = \'BORDER-TOP: medium none; BORDER-RIGHT: medium none; WIDTH: 100%; VERTICAL-ALIGN: top; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 1px; TEXT-ALIGN: center; PADDING-TOP: 1px; PADDING-LEFT: 15px; BORDER-LEFT: medium none; PADDING-RIGHT: 15px; BACKGROUND-COLOR: transparent\'> <P style = \'FONT-SIZE: 10px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=left>&#160;</P><P style=\'FONT-SIZE: 10px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=left>&#160;</P><P style=\'FONT-SIZE: 16px; MARGIN-BOTTOM: 1em; FONT-FAMILY: Arial, Helvetica, sans-serif; MARGIN-TOP: 0px; COLOR: #7c7c7c; LINE-HEIGHT: 125%; BACKGROUND-COLOR: transparent; mso-line-height-rule: exactly\' align=right>Sistema de Informaci&#243;n &#169;2018 <BR>Departamento de Desarrollo e Implementaci&#243;n de Sistemas, TIC </P></TD></TR></TABLE></TD></TR></TABLE></TD></TR><TR> <TD style = \'FONT-SIZE: 0px; HEIGHT: 8px; LINE-HEIGHT: 0\'>&#160;</TD></TR></TABLE></body></html></TD></TR><TR> <TD style = \'FONT-SIZE: 0px; HEIGHT: 8px; LINE-HEIGHT: 0\'>&#160;</TD></TR></TABLE></body></html>";
                ManejarCorreo.EnviarCorreo(correo, true);

                btnRegistros.Badge.Text = "";
                btnRecibidos.Badge.Text = "";
                btnEnviados.Badge.Text = "";
                Totales(Session["DepartamentoRemitente"].ToString().ToInt());
            }
            else
            {
                modalMensajeEnviar.InnerHtml = "Error al enviar " + resultado.strError;
                ModalEnviar.ShowOnPageLoad = true;
            }

        }
    }
}
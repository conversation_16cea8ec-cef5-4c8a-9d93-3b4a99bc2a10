﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterGestionExpedientes.Master" AutoEventWireup="true" CodeBehind="Enviados.aspx.cs" Inherits="GestionExpedientes.Enviados" %>
<%@ Register Assembly="DevExpress.Web.Bootstrap.v18.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.Bootstrap" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
      <dx:ASPxCallbackPanel ID="cbPrincipal" runat="server" ClientInstanceName="cbPrincipal" OnCallback="cbPrincipal_Callback">
        <ClientSideEvents />
        <PanelCollection>
            <dx:PanelContent>
                 <div id="contenedor-principal">
                    <div id="navegacion"></div>
                    <div id="header-principal">
                        <div class="logo"></div>
                        <div class="header-principal-titulo">
                            <h1>SISTEMA DE INFORMACION</h1>
                            <h2>GESTIÓN DE PAGOS Y MANEJO DE CORRESPONDENCIAS</h2>
                        </div>
                        <div id="area-user">
                            <div id="area-user-info">
                                <div id="imgUsuario"></div>
                                <dx:BootstrapBinaryImage ID="ImgUsuario" runat="server"></dx:BootstrapBinaryImage>
                                <div id="InfoDatosUsuario">
                                    <h3 style="margin-top: -20px" runat="server" id="LoginUser"></h3>
                                    <h4 runat="server" id="LoginDepartamento"></h4>
                                </div>
                            </div>
                            <a href="http://sigepi.inaipi.gob.do/" runat="server" style="text-align: center; margin-right: 10px; display: block; padding: 5px; text-transform: uppercase; float: left; text-decoration: none"><span style="font-size: 35px;" class="glyphicon glyphicon-th"></span>
                                <br />
                                <span style="font-family: Arial; font-size: 10px; font-weight: bold">Menu Principal</span></a>
                            <div style="display: block; float: right; width: 97px; height: 62px; text-align: center; padding-top: 9px">
                                <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-search" ID="BootstrapButton1" runat="server" UseSubmitBehavior="false" AutoPostBack="false">
                                    <SettingsBootstrap RenderOption="Default" />
                                    <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('CONSULTAR-EXPEDIENTES|');}" />
                                </dx:BootstrapButton>
                                <br />
                                <span style="font-family: Arial; font-size: 10px; font-weight: bold; text-transform: uppercase; color: #337AB7">Consultar</span>
                            </div>
                        </div>
                    </div>
                    <div id="header-modulo">
                        <span class="glyphicon glyphicon-th-list"></span> <span runat="server"> Expedientes Enviados</span>
                        <div class="header-modulo-tool">
                              <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-plus" ID="btnNuevo" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text=" Nuevo" Visible="true">
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('NUEVO|');}" />
                            </dx:BootstrapButton>                            

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnRegistros" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Registrados">
                                <Badge Text="34,000" IconCssClass="far fa-thumbs-up" />
                                <SettingsBootstrap RenderOption="Default" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-REGISTRADOS|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnRecibidos" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Recibidos">
                                <Badge Text="99" IconCssClass="far fa-thumbs-up" />
                                <SettingsBootstrap RenderOption="Default" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-RECIBIDOS|');}" />
                            </dx:BootstrapButton>

                            <dx:BootstrapButton CssClasses-Icon="glyphicon glyphicon-list" ID="btnEnviados" runat="server" UseSubmitBehavior="false" AutoPostBack="false" Text="Expedientes Enviados">
                                <Badge Text="99" IconCssClass="far fa-thumbs-up" />
                                <SettingsBootstrap RenderOption="Success" />
                                <ClientSideEvents Click="function(s, e) {	cbPrincipal.PerformCallback('EXPEDIENTES-ENVIADOS|');}" />
                            </dx:BootstrapButton>
                        </div>
                    </div>
                    <div id="contenido">
                        <div class="vista-modulo" id="vista1" runat="server">
                            <div class="data-grid">

                                <div class="data-grid-contenido" style="top: 0">
                                     <dx:ASPxGridView ID="grdListEnviados" runat="server" AutoGenerateColumns="False" Theme="MetropolisBlue" KeyFieldName="IdExpediente" HorizontalScrollBarMode="Auto" SettingsPager-Visible="False" Settings-ShowFooter="True" Width="100%">
                                  
                                        <ClientSideEvents CustomButtonClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR-EXPEDIENTE|&#39;+ s.GetRowKey(e.visibleIndex));}"
                                            RowDblClick="function(s, e) {	cbPrincipal.PerformCallback(&#39;ABRIR-EXPEDIENTE|&#39;+ s.GetRowKey(e.visibleIndex));}"></ClientSideEvents>


                                        <SettingsPager Mode="ShowPager" PageSize="15"></SettingsPager>
                                        <Settings ShowFooter="true" ShowHeaderFilterButton="false" VerticalScrollBarMode="Hidden" VerticalScrollableHeight="400" VerticalScrollBarStyle="VirtualSmooth" />
                                        <SettingsDataSecurity AllowEdit="False" AllowInsert="False" AllowDelete="False"></SettingsDataSecurity>
                                        <SettingsSearchPanel Visible="True"></SettingsSearchPanel>
                                        <Columns>
                                            <dx:GridViewCommandColumn Width="40" VisibleIndex="0" Name="gcIdExpediente">
                                                <CustomButtons>
                                                    <dx:GridViewCommandColumnCustomButton Image-IconID="actions_pagenext_16x16devav">
                                                    </dx:GridViewCommandColumnCustomButton>
                                                </CustomButtons>
                                            </dx:GridViewCommandColumn>
                                            <dx:GridViewDataTextColumn CellStyle-HorizontalAlign="Center" Width="100" FieldName="IdExpediente" Name="gcIdExpediente" Caption="Expediente" VisibleIndex="1"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="TipoDocumento" Name="gcTipoDocumento" Caption="Tipo" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="NumeroDocumento" Width="100" Name="gcNumeroDocumento" Caption="Número" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="Asunto" Name="gcAsunto" Caption="Asunto" VisibleIndex="2"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="DepartamentoRemitente" Name="gcDepartamentoRemitente" Caption="Departamento Remitente" VisibleIndex="3"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="UsuarioRemitente" Name="gcUsuarioRemitente" Caption="Usuario Remitente" VisibleIndex="4"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="DepartamentoDestinatario" Name="gcDepartamentoDestinatario" Caption="Departamento Destinatario" VisibleIndex="5"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" PropertiesTextEdit-DisplayFormatString="{0:dd/MM/yyyy}" CellStyle-HorizontalAlign="Center" FieldName="FechaEnviado" Name="gcFechaEnviado" Caption="Fecha" VisibleIndex="6"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn FieldName="LabelUbicacion" Name="gcLabelUbicacion" Caption="Ubicaci&#243;n Actual" VisibleIndex="7"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn Width="100" FieldName="LabelEstatus" Name="gcLabelEstatus" Caption="Estatus" VisibleIndex="8"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="c2" Width="100" FieldName="Balance" Name="gcBalance" Caption="Balance" VisibleIndex="9"></dx:GridViewDataTextColumn>
                                         <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="c2" Width="100" FieldName="NumeroLibramiento" Name="gcNumeroLibramiento" Caption="Libramiento" VisibleIndex="10"></dx:GridViewDataTextColumn>
                                            <dx:GridViewDataTextColumn PropertiesTextEdit-DisplayFormatString="c2" Width="100" FieldName="NumeroOrdenamiento" Name="gcNumeroOrdenamiento" Caption="Ordenamiento" VisibleIndex="11"></dx:GridViewDataTextColumn>
                                        </Columns>
                                    </dx:ASPxGridView>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>

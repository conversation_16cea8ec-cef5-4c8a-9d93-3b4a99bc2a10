﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.SessionState;

namespace GestionExpedientes
{
    public class Global : System.Web.HttpApplication
    {
        protected void Application_Start(object sender, EventArgs e)
        {

        }
        protected void Session_Start(object sender, EventArgs e)
        {
            Session.Timeout = 180;
        }
        public struct USER
        {
            public string Nombres
            {
                get { return HttpContext.Current.Session["User_Nombres"].ToString(); }
                set { HttpContext.Current.Session["User_Nombres"] = value; }
            }

            public string Apellidos
            {
                get { return HttpContext.Current.Session["User_Apellidos"].ToString(); }
                set { HttpContext.Current.Session["User_Apellidos"] = value; }
            }
        }
    }
}